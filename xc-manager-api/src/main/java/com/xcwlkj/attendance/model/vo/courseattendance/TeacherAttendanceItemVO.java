/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.attendance.model.vo.courseattendance;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 教师考勤列表vo
 * <AUTHOR>
 * @version $Id: TeacherAttendanceItemVO.java, v 0.1 2023年07月17日 09时43分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TeacherAttendanceItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 编号 */
    private String bh;
    /** 工号 */
    private String gh;
    /** 教师名称 */
    private String jsxm;
    /** 教室号 */
    private String jsh;
    /** 教室名称 */
    private String jsmc;
    /** 班级名称 */
    private String bjmc;
    /** 课程名称 */
    private String kcmc;
    /** 考勤结果  0：正常1：请假2：迟到3：早退4：旷课 */
    private Integer kqjg;
    /** 课程时间 */
    private String kcsj;
    /** 考勤时间 */
    private String kqsj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
