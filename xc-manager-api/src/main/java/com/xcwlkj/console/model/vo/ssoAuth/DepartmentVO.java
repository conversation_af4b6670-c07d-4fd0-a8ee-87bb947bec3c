/**
 * Xcwlkj.com Inc.
 * Copyright (c) 2019-2029 All Rights Reserved.
 */
package com.xcwlkj.console.model.vo.ssoAuth;

import com.xcwlkj.base.dto.BaseVo;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 部门
 * <AUTHOR>
 * @version $Id: DepartmentVO.java, v 0.1 2019年08月20日 15时31分 XcDev Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DepartmentVO extends BaseVo {
	/** 序列id */
	private static final long serialVersionUID = 1L;
    /** 父部门id */
    private String            parentId;
    /** 祖级列表 */
    private String            ancestors;
    /** 部门名称 */
    private String            name;
    /** 负责人 */
    private String            leader;
    /** 联系电话 */
    private String            phone;
    /** 部门简介 */
    private String            description;
    /** 部门状态:1正常,0停用 */
    private Integer            status;
    /** 部门服务器地址 */
    private String            sipUrl;
    /** 排序 */
    private Integer            orderNum;
    /** 类型：机构：mechanism  部门：department */
    private String            type;
    /** 父部门名称 */
    private String            parentName;
	
}