/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.resp.photo;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 新增校园相册视频地址响应
 * <AUTHOR>
 * @version $Id: InsertZhxyXcspRespModel.java, v 0.1 2023年07月20日 14时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InsertZhxyXcspRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}