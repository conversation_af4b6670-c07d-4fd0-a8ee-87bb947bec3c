/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2035 All Rights Reserved.
 */
package com.xcwlkj.campus.model.dto.classevaluation;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 班级评价-班级绑定新增dto
 * <AUTHOR>
 * @version $Id: ClassBindAddItemDTO.java, v 0.1 2025年06月25日 09时08分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ClassBindAddItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 模板编号 */
    @NotBlank(message = "模板编号不能为空")
    private String mbbh;

    /** 模板编号 */
    @NotBlank(message = "模板名称不能为空")
    private String mbmc;
    /** 班级列表 */
    @NotNull(message = "班级列表不能为空")
    private List<ClassAddItemDTO> classAddList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
