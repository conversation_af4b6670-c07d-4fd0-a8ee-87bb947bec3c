/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.campus.model.vo.bpbj;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;


/**
 * 布局模板列表vo
 * <AUTHOR>
 * @version $Id: MbItemVO.java, v 0.1 2023年07月19日 09时17分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 模板编号 */
    private Integer bh;
    /** 模板名称 */
    private String mbmc;
    /** 布局类型, 1：系统模板  2：自定义模板 */
    private Integer bjlx;
    /** 背景图片  dfsid */
    private String bjtp;
    /** 状态 0：未发布 1：已发布 */
    private Integer zt;
    /** 描述 */
    private String ms;
    /** 封面 */
    private String cover;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
