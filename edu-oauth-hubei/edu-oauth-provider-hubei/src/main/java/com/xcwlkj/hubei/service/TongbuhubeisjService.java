package com.xcwlkj.hubei.service;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.List;

import com.xcwlkj.hubei.model.enums.DataBaseEnum;
import com.xcwlkj.sjgz.model.vo.sjgztssjcx.CzzdxxItemVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class TongbuhubeisjService {

	@Autowired
	private GetConnectionService connectionService;

	// cs_ksgljg
	public void executeSqlOfCsksgljg(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement ps1 = con.createStatement();
			ps1.addBatch("DELETE FROM epms_ksyw.cs_ksgljg");
			ps1.executeBatch();
			// ps1.clearBatch();
			ps1.close();

			PreparedStatement ps = (PreparedStatement) con
					.prepareStatement("insert into epms_ksyw.cs_ksgljg(ksgljgid,ksgljgmc,ksgljgjc,ksgljgdz,"
							+ " ksgljgjd,ksgljgwd,gxfwxzqhm,fzrxm,fzrdh,sczt,create_time) "
							+ " values (?,?,?,?,?,?,?,?,?,'0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					ps.setString(i + 1, minList.get(i));
				}
				// 把一个SQL命令加入命令列表
				ps.addBatch();
			}
			// 执行批量更新
			ps.executeBatch();
			// 语句执行完毕，提交本事务
			con.commit();
			ps.close();
			// pst.clearBatch();

			// 其他更新语句
			Statement ps2 = con.createStatement();
			ps2.addBatch(
					"UPDATE epms_ksyw.cs_ksgljg SET sjgljgid = '#',jdzx = '#',cclx = 'Z1',ksszsf = '湖北省',ksszsfm = '420000' "
							+ " WHERE SUBSTR(ksgljgid,3,4) = '0000'");
			ps2.addBatch(
					"UPDATE epms_ksyw.cs_ksgljg SET sjgljgid = '420000',jdzx = '420000' ,cclx = 'Z2' ,ksszsf = '湖北省',ksszsfm = '420000',"
							+ " ksszsqm = ksgljgid WHERE SUBSTR(ksgljgid,5,2) = '00' AND SUBSTR(ksgljgid,3,4) != '0000'");
			ps2.addBatch(
					"UPDATE epms_ksyw.cs_ksgljg SET sjgljgid = CONCAT(SUBSTR(ksgljgid,1,4),'00'),jdzx = CONCAT('420000,',SUBSTR(ksgljgid,1,4),'00') ,"
							+ " cclx = 'Z3' ,ksszsf = '湖北省',ksszsfm = '420000',ksszsqm = CONCAT(SUBSTR(ksgljgid,1,4),'00'),ksszqxm = ksgljgid  "
							+ " WHERE SUBSTR(ksgljgid,5,2) != '00' AND SUBSTR(ksgljgid,3,4) != '0000'  AND SUBSTR(ksgljgid,1,3) != '429'");
			ps2.addBatch(
					"update epms_ksyw.cs_ksgljg a set a.ksszsq = (select b.MC from epms_ksyw.gj_xzqhdmb b where a.ksszsqm = b.DM),"
							+ " a.ksszqx = (select b.MC from epms_ksyw.gj_xzqhdmb b where a.ksszqxm = b.DM)");
			ps2.executeBatch();
			con.commit();
			ps2.close();

			con.close();// 一定要记住关闭连接，不然mysql回应为too many connection自我保护而断开。
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// cs_zhzx
	public void executeSqlOfCszhzx(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());

		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_ksyw.cs_zhzx");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("insert into epms_ksyw.cs_zhzx (zhzxid,ksgljgbzm,zhzxmc,zhzxdz,"
							+ " zhzxjd,zhzxwd,zhzxdh,sczt,create_time) values (?,?,?,?,?,?,?,'0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				// 把一个SQL命令加入命令列表
				pst2.addBatch();
			}
			// 执行批量更新
			pst2.executeBatch();
			con.commit();
			pst2.close();

			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// cs_bzhkd
	public void executeSqlOfCsbzhkd(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_ksyw.cs_bzhkd");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("insert into epms_ksyw.cs_bzhkd (bzhkdid,bzhkdmc,bzhkdjc,kdlbm,"
							+ " kdlb,kddz,kdjcsj,kdjd,kdwd,kdfzrxm,kdfzrdh,kwbgsdh,sjbgsdh,spjksdh,ksgljgbzm,sczt,create_time) "
							+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					// pst2.setString(i + 1, minList.get(i));
					if (i == 3 && StringUtils.isBlank(minList.get(3))) {
						pst2.setString(4, "4");
					} else if (i == 4 && StringUtils.isBlank(minList.get(4))) {
						pst2.setString(5, "完中（初中高中）");
					} else if (i == 7 && StringUtils.isBlank(minList.get(7))) {
						pst2.setString(8, "0");
					} else if (i == 8 && StringUtils.isBlank(minList.get(8))) {
						pst2.setString(9, "0");
					} else {
						pst2.setString(i + 1, minList.get(i));
					}
				}
				// 把一个SQL命令加入命令列表
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();

			// 其他更新语句
			Statement ps = con.createStatement();
			ps.addBatch(
					"UPDATE epms_ksyw.cs_bzhkd a INNER JOIN (SELECT ksgljgid,ksszsfm,ksszsf,ksszsqm,ksszsq,ksszqxm,ksszqx FROM "
							+ " epms_ksyw.cs_ksgljg) b ON a.ksgljgbzm = b.ksgljgid SET a.kdszsfm = b.ksszsfm,"
							+ " a.kdszsf = b.ksszsf,a.kdszsqm = b.ksszsqm,a.kdszsq = b.ksszsq,"
							+ " a.kdszqxm = b.ksszqxm,a.kdszqx = b.ksszqx");
			ps.executeBatch();
			con.commit();
			ps.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}


	// cs_bzhkc
	public void executeSqlOfCsbzhkc(int a, List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			if (a == 1) {
				// 先删除数据
				Statement st = con.createStatement();
				st.addBatch("DELETE FROM epms_ksyw.cs_bzhkc");
				st.executeBatch();
				con.commit();
				st.close();

				PreparedStatement pst1 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.cs_bzhkc (bzhkcid,bzhkcmc,kxrl,kcsjwz,"
								+ " bzhkdid,zcqswzm,zcqswz,zwbjfsm,zwbjfs,zwplfsm,zwplfs,sczt,bzhkcbh,create_time) "
								+ " values (?,?,?,?,?,'1','前左手位','6','7878','1','S型','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						if (i == 1 && StringUtils.isBlank(minList.get(1))) {
							pst1.setString(2, "0");
						} else {
							pst1.setString(i + 1, minList.get(i));
						}
					}
					// 把一个SQL命令加入命令列表
					pst1.addBatch();
				}
				pst1.executeBatch();
				con.commit();
				pst1.clearBatch();
				pst1.close();

			} else {
				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.cs_bzhkc (bzhkcid,bzhkcmc,kxrl,kcsjwz,"
								+ " bzhkdid,zcqswzm,zcqswz,zwbjfsm,zwbjfs,zwplfsm,zwplfs,sczt,bzhkcbh,create_time) "
								+ " values (?,?,?,?,?,'1','前左手位','6','7878','1','S型','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						if (i == 1 && StringUtils.isBlank(minList.get(1))) {
							pst2.setString(2, "0");
						} else {
							pst2.setString(i + 1, minList.get(i));
						}
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.clearBatch();
				pst2.close();
			}
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// ks_ksjh
	public void executeSqlOfKsksjh(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_ksyw.ks_ksjh");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("insert into epms_ksyw.ks_ksjh (ksjhbh,mc,nd,"
							+ " kssj,jssj,kssm,kszt,kslbmc,kslbdm,ksxmmc,ksxmdm,scztw,create_time) "
							+ " values (?,?,?,?,?,?,concat(?,'0'),?,?,?,?,'0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					if (i == 6 && StringUtils.isBlank(minList.get(6))) {
						pst2.setString(7, "0");
					} else if (i == 7 && StringUtils.isBlank(minList.get(7))) {
						pst2.setString(8, "国家教育考试");
					} else if (i == 8 && StringUtils.isBlank(minList.get(8))) {
						pst2.setString(9, "GJJYKS");
					} else if (i == 9 && StringUtils.isBlank(minList.get(9))) {
						pst2.setString(10, "选考");
					} else if (i == 10 && StringUtils.isBlank(minList.get(10))) {
						pst2.setString(11, "1006");
					} else {
						pst2.setString(i + 1, minList.get(i));
					}

				}
				// 把一个SQL命令加入命令列表
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// ks_kscc
	public void executeSqlOfKskscc(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_ksyw.ks_kscc");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("insert into epms_ksyw.ks_kscc(ksjhbh,ccm,kmm,"
							+ " ccmc,kmmc,mtfsm,mtfsmc,kmkssj,kmjssj,scztw,create_time) "
							+ " values (?,?,?,?,?,?,?,?,?,'0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				// 把一个SQL命令加入命令列表
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// kd_kqxx
	public void executeSqlOfKdkqxx(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_ksyw.kd_kqxx");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = con
					.prepareStatement("insert into epms_ksyw.kd_kqxx(kqbh,kqmc,ksgljgid,"
							+ " ksjhbh,xzbm,sczt,create_time)  values (?,?,?,?,?,'0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();

			// 其他更新语句
			Statement ps = con.createStatement();
			ps.addBatch(
					"UPDATE epms_ksyw.kd_kqxx SET sjkqbh = CONCAT(SUBSTR(ksgljgid,1,4) ,'00') WHERE SUBSTR(ksgljgid,5,2) != '00'"
							+ " AND SUBSTR(ksgljgid,3,4) != '0000' AND SUBSTR(ksgljgid,1,3) != 429");
			ps.addBatch(
					"UPDATE epms_ksyw.kd_kqxx SET sjkqbh = '420000' WHERE SUBSTR(ksgljgid,5,2) = '00' AND SUBSTR(ksgljgid,3,4) != '0000'"
							+ "OR SUBSTR(ksgljgid,1,3) = 429");
			ps.executeBatch();
			con.commit();
			ps.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// kd_kdxx
	public void executeSqlOfKdkdxx(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_ksyw.kd_kdxx");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("insert into epms_ksyw.kd_kdxx(kqbh,kdbh,kdmc,kdjc,sfbzhkd,bzhkdid,"
							+ " kdlbm,kdlb,kddz,kdjd,kdwd,kdfzrxm,kdfzrdh,kdxzqhm,ksjhbh,ccm,sczt,kdbzid,create_time) "
							+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0',MD5(uuid()),NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();

			// 其他更新语句
			Statement ps = con.createStatement();
			ps.addBatch(
					"UPDATE epms_ksyw.kd_kdxx AS a INNER JOIN (SELECT bzhkdid,bzhkdmc,kdszsfm,kdszsf,kdszsqm,kdszsq,kdszqxm,kdszqx"
							+ " FROM epms_ksyw.cs_bzhkd ) b ON a.bzhkdid = b.bzhkdid"
							+ " SET a.bzhkdmc = b.bzhkdmc,a.kdszsfm = b.kdszsfm,a.kdszsf = b.kdszsf,a.kdszsqm = b.kdszsqm,a.kdszsq = b.kdszsq,"
							+ " a.kdszqxm = b.kdszqxm,a.kdszqx = b.kdszqx");
			ps.executeBatch();
			con.commit();
			ps.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// kd_kcxx-超过10000的以后调用不进行删除操作
	public void executeSqlOfKdkcxx(int a, List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			if (a == 1) {
				// 先删除数据
				Statement st = con.createStatement();
				st.addBatch("DELETE FROM epms_ksyw.kd_kcxx");
				st.executeBatch();
				st.close();

				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.kd_kcxx(kqbh,kdbh,kcbh,ksjhbh,sfbzhkc,bzhkdid,"
								+ " bzhkcid,zcqswzm,zcqswz,zwbjfsm,zwbjfs,zwplfsm,zwplfs,ccm,kcmc,sczt,kcbzid,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst2.setString(i + 1, minList.get(i));
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.close();

			} else if (a == 2) {
				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.kd_kcxx(kqbh,kdbh,kcbh,ksjhbh,sfbzhkc,bzhkdid,"
								+ " bzhkcid,zcqswzm,zcqswz,zwbjfsm,zwbjfs,zwplfsm,zwplfs,ccm,kcmc,sczt,kcbzid,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst2.setString(i + 1, minList.get(i));
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.close();
			} else if (a == 3) {
				// 其他更新语句
				Statement ps = con.createStatement();
				ps.addBatch("UPDATE epms_ksyw.kd_kcxx AS a INNER JOIN (SELECT bzhkdid,bzhkcid,bzhkcmc,bzhkcbh"
						+ " FROM epms_ksyw.cs_bzhkc) b ON a.bzhkdid = b.bzhkdid AND a.bzhkcid = b.bzhkcid"
						+ " SET a.bzhkcmc = b.bzhkcmc,a.bzhkcbh = b.bzhkcbh");
				ps.executeBatch();
				con.commit();
				ps.close();
			} else if (a == 4) {
				// 先删除数据
				Statement st = con.createStatement();
				st.addBatch("DELETE FROM epms_ksyw.kd_kcxx");
				st.executeBatch();
				st.close();

				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.kd_kcxx(kqbh,kdbh,kcbh,ksjhbh,sfbzhkc,bzhkdid,"
								+ " bzhkcid,zcqswzm,zcqswz,zwbjfsm,zwbjfs,zwplfsm,zwplfs,ccm,kcmc,sczt,kcbzid,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst2.setString(i + 1, minList.get(i));
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.close();

				// 其他更新语句
				Statement ps = con.createStatement();
				ps.addBatch("UPDATE epms_ksyw.kd_kcxx AS a INNER JOIN (SELECT bzhkdid,bzhkcid,bzhkcmc,bzhkcbh"
						+ " FROM epms_ksyw.cs_bzhkc) b ON a.bzhkdid = b.bzhkdid AND a.bzhkcid = b.bzhkcid"
						+ " SET a.bzhkcmc = b.bzhkcmc,a.bzhkcbh = b.bzhkcbh");
				ps.executeBatch();
				con.commit();
				ps.close();
			} else {
				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.kd_kcxx(kqbh,kdbh,kcbh,ksjhbh,sfbzhkc,bzhkdid,"
								+ " bzhkcid,zcqswzm,zcqswz,zwbjfsm,zwbjfs,zwplfsm,zwplfs,ccm,kcmc,sczt,kcbzid,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,'0','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst2.setString(i + 1, minList.get(i));
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.close();

				// 其他更新语句
				Statement ps = con.createStatement();
				ps.addBatch("UPDATE epms_ksyw.kd_kcxx AS a INNER JOIN (SELECT bzhkdid,bzhkcid,bzhkcmc,bzhkcbh"
						+ " FROM epms_ksyw.cs_bzhkc) b ON a.bzhkdid = b.bzhkdid AND a.bzhkcid = b.bzhkcid"
						+ " SET a.bzhkcmc = b.bzhkcmc,a.bzhkcbh = b.bzhkcbh");
				ps.executeBatch();
				con.commit();
				ps.close();
			}
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// ks_bmxx
	public void executeSqlOfKsbmxx(int a, List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			if (a == 0) {
				log.info("-------0-----删除ks_bmxx---a-------- = {} ", a);
				// 先删除数据
				Statement st = con.createStatement();
				st.addBatch("DELETE FROM epms_ksyw.ks_bmxx");
				st.executeBatch();
				con.commit();
				st.close();
			} else if (a == 1) {
				log.info("-------1-----开始执行ks_bmxx---a-------- = {} ", a);
				PreparedStatement pst1 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.ks_bmxx(ksjhid,ksh,xm,xbm,"
								+ " sfzjlxm,sfzjlx,sfzjhm,csrq,mzm,hkszdm,hkszd,wyyzm,wyyz,xb,mz,zp,scztw,ksid,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,'0','0','0','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst1.setString(i + 1, minList.get(i));
					}
					pst1.addBatch();
				}
				pst1.executeBatch();
				con.commit();
				pst1.clearBatch();
				pst1.close();

			} else {
				log.info("-------2-----开始执行ks_bmxx---a-------- = {} ", a);
				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.ks_bmxx(ksjhid,ksh,xm,xbm,"
								+ " sfzjlxm,sfzjlx,sfzjhm,csrq,mzm,hkszdm,hkszd,wyyzm,wyyz,xb,mz,zp,scztw,ksid,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,?,?,'0','0','0','0',MD5(uuid()),NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst2.setString(i + 1, minList.get(i));
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.clearBatch();
				pst2.close();
			}
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// ks_bpxx
	public void executeSqlOfKsbpxx(int a, List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			if (a == 1) {
				// 先删除数据
				log.info("-------1-----开始执行ks_bpxx---a = {} ", a);
				Statement st = con.createStatement();
				st.addBatch("DELETE FROM epms_ksyw.ks_bpxx");
				st.executeBatch();
				con.commit();
				st.close();

				PreparedStatement pst1 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.ks_bpxx(ksjhbh,ccm,kmm,ksh,zkzh,kqbh,"
								+ " kdbh,kcbh,zwh,bzhkdbid,bzhkcbid,ksbpbh,scztw,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,MD5(uuid()),'0',NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst1.setString(i + 1, minList.get(i));
					}
					pst1.addBatch();
				}
				pst1.executeBatch();
				con.commit();
				pst1.clearBatch();
				pst1.close();

			} else {
				log.info("-------2-----开始执行ks_bpxx---a = {} ", a);
				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.ks_bpxx(ksjhbh,ccm,kmm,ksh,zkzh,kqbh,"
								+ " kdbh,kcbh,zwh,bzhkdbid,bzhkcbid,ksbpbh,scztw,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,?,MD5(uuid()),'0',NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						pst2.setString(i + 1, minList.get(i));
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.clearBatch();
				pst2.close();
			}
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// ks_kwry
	public void executeSqlOfKskwry(int a, List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.KSYW.getCode());
		try {
			con.setAutoCommit(false);
			if (a == 1) {
				// 先删除数据
				Statement st = con.createStatement();
				st.addBatch("DELETE FROM epms_ksyw.ks_kwry");
				st.executeBatch();
				st.close();

				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.ks_kwry(ksjhbh,ccm,kdbh,kcbh,csrymc,"
								+ " csrysfzh,csrysjh,csryxb,ksjsm,ksjs,kwrybh,scztw,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,MD5(uuid()),'0',NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						if (i == 1 && StringUtils.isBlank(minList.get(1))) {
							pst2.setString(2, "0");
						} else {
							pst2.setString(i + 1, minList.get(i));
						}

					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.close();

			} else if (a == 2) {
				PreparedStatement pst2 = (PreparedStatement) con
						.prepareStatement("insert into epms_ksyw.ks_kwry(ksjhbh,ccm,kdbh,kcbh,csrymc,"
								+ " csrysfzh,csrysjh,csryxb,ksjsm,ksjs,kwrybh,scztw,create_time) "
								+ " values (?,?,?,?,?,?,?,?,?,?,MD5(uuid()),'0',NOW())");
				for (List<String> minList : FindList) {
					for (int i = 0; i < minList.size(); i++) {
						if (i == 1 && StringUtils.isBlank(minList.get(1))) {
							pst2.setString(2, "0");
						} else {
							pst2.setString(i + 1, minList.get(i));
						}
					}
					pst2.addBatch();
				}
				pst2.executeBatch();
				con.commit();
				pst2.close();
			} else {
				// ks_bmxx更新语句
				Statement ps = con.createStatement();
				ps.addBatch("UPDATE epms_ksyw.ks_bmxx a,epms_ksyw.gj_xbdmb b,epms_ksyw.gj_mzdmb d "
						+ " SET a.xb = b.MC,a.mz = d.MC WHERE a.xbm = b.DM AND a.mzm = d.DM");
				ps.executeBatch();
				con.commit();
				ps.close();
			}
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// cs_sjbms-部署时要换为数据库-epms_bmsjk
	public void executeSqlOfCssjbms(List<List<String>> FindList) {
		Connection con = connectionService.getMysqlConnection(DataBaseEnum.BMS.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM epms_bmsjk.cs_sjbms");
			st.executeBatch();
			st.close();

			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("insert into epms_bmsjk.cs_sjbms (sjbmsid,ksgljgid,sjbmsmc,jc,"
							+ " sjbmsdz,sjbmsjcsj,sjbmsjd,sjbmswd,sjbmsfzrxm,sjbmsfzrdh,sjbmsdh,jdzx,sczt,create_time) "
							+ " values (?,?,?,?,?,?,?,?,?,?,?,'0','0',NOW())");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();

			// 其他更新语句
			Statement ps = con.createStatement();
			ps.addBatch("UPDATE epms_bmsjk.cs_sjbms a INNER JOIN epms_ksyw.cs_ksgljg b ON  a.ksgljgid = b.ksgljgid "
					+ " SET a.jdzx = CONCAT(b.jdzx, ',' ,a.ksgljgid) where b.jdzx is not null ");
			ps.executeBatch();
			con.commit();
			ps.close();

			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}





	// DC_BMSZSQKBGB--保密室值守情况--中间库---基础数据库
	public void executeSqlOfBmszsqkbgb(
			List<List<String>> FindList) /* throws SQLException */ {
		log.info("----------开始执行-保密室值守情况----DC_BMSZSQKBGB--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM JCSJV2.DC_BMSZSQKBGB");
			st.executeBatch();
			st.close();

			// 双引号转义符sql执行写入Oracle表
			PreparedStatement pst1 =  con.prepareStatement("insert into JCSJV2.DC_BMSZSQKBGB ("
					+ "\"SJBMSID\"" + "," + "\"ZSKSSJ\"" + "," + "\"ZSJSSJ\"" + "," + "\"SFMZ4RYSZG\"" + ","
					+ "\"SFYGAHWJZG\"" + "," + "\"BMGFTSFZC\"" + "," + "\"ZBQK\"" + "," + "\"YCQKSM\"" + ","
					+ "\"BGRXM\"" + "," + "\"BGSJ\"" + "," + "\"ID\"" + " ) values(?,?,?,?,?,?,?,?,?,?,?)");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// DC_BMSSPHFQKB--保密室视频回放--中间库--基础数据库
	public void executeSqlOfBmssphfqkb(
			List<List<String>> FindList) /* throws SQLException */ {
		log.info("----------开始执行-保密室视频回放----DC_BMSSPHFQKB--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		try {
			con.setAutoCommit(false);
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM JCSJV2.DC_BMSSPHFQKB");
			st.executeBatch();
			st.close();

			// 双引号转义符sql执行写入Oracle表
			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into JCSJV2.DC_BMSSPHFQKB ("
					+ "\"SJBMSID\"" + "," + "\"SPKSSJ\"" + "," + "\"SPJZSJ\"" + "," + "\"HFJG\"" + "," + "\"YCQKSM\""
					+ "," + "\"BGRXM\"" + "," + "\"BGSJ\"" + " ) values(?,?,?,?,?,?,?)");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	// ZH_SSSBXXB_TEMP--学校/教室设备信息--中间库---基础数据库
	public void executeSqlOfSbxxbwsxc(int a, List<List<String>> FindList) throws SQLException {
		log.info("----------开始执行-学校/教室设备信息----ZH_SSSBXXB_WSXC--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		con.setAutoCommit(false);

		if (a == 1) {
			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM JCSJV2.ZH_SSSBXXB_TEMP");
			st.executeBatch();
			st.close();

			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into JCSJV2.ZH_SSSBXXB_TEMP ("
					+ "\"SBAZWZ\"" + "," + "\"SBMC\"" + "," + "\"SBLB\"" + "," + "\"SBLBM\"" + "," + "\"SIPURI\"" + ","
					+ "\"FJSIPURI\"" + "," + "\"SBBDIP\"" + "," + "\"SBBH\"" + "," + "\"SBSSCSLXM\"" + ","
					+ "\"SBSSCSLX\"" + "," + "\"BZHKDMC\"" + "," + "\"BZHKCMC\"" + "," + "\"ORGID\"" + "," + "\"XH\""
					+ " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} else {
			PreparedStatement pst2 = (PreparedStatement) con.prepareStatement("insert into JCSJV2.ZH_SSSBXXB_TEMP ("
					+ "\"SBAZWZ\"" + "," + "\"SBMC\"" + "," + "\"SBLB\"" + "," + "\"SBLBM\"" + "," + "\"SIPURI\"" + ","
					+ "\"FJSIPURI\"" + "," + "\"SBBDIP\"" + "," + "\"SBBH\"" + "," + "\"SBSSCSLXM\"" + ","
					+ "\"SBSSCSLX\"" + "," + "\"BZHKDMC\"" + "," + "\"BZHKCMC\"" + "," + "\"ORGID\"" + "," + "\"XH\""
					+ " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?)");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();
			con.close();
		}
	}

	// ZH_SSSBXXB--其他场所设备信息--中间库---基础数据库
	public void executeSqlOfSbxxb(List<List<String>> FindList) throws SQLException {
		log.info("----------开始执行-其他场所设备信息----ZH_SSSBXXB--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		con.setAutoCommit(false);

		// 先删除数据
//		Statement st = con.createStatement();
//		st.addBatch("DELETE FROM JCSJV2.ZH_SSSBXXB");
//		st.executeBatch();
//		st.close();

		PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into JCSJV2.ZH_SSSBXXB (" + "\"SBMC\""
				+ "," + "\"SBLB\"" + "," + "\"SBLBM\"" + "," + "\"SBURIDZ\"" + "," + "\"SBBH\"" + "," + "\"SBSSLXM\""
				+ "," + "\"SBSSLX\"" + "," + "\"SJBMSID\"" + "," + "\"SFSYSJBMS\"" + "," + "\"KSGLJGID\"" + ","
				+ "\"SFSYKSGLJG\"" + "," + "\"ORGID\"" + "," + "\"XZQHM\"" + " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?)");
		for (List<String> minList : FindList) {
			for (int i = 0; i < minList.size(); i++) {
				pst1.setString(i + 1, minList.get(i));
			}
			pst1.addBatch();
		}
		pst1.executeBatch();
		con.commit();
		pst1.close();
		con.close();
	}

	// DC_SJYYRYXXB--试卷押运人员--中间库---基础数据库
	public void executeSqlOfSjyyryxxb(List<List<String>> FindList)  {
		log.info("----------开始执行-试卷押运人员----DC_SJYYRYXXB--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM JCSJV2.DC_SJYYRYXXB");
			st.executeBatch();
			st.close();

//		PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into JCSJV2.DC_SJYYRYXXB ("
//				+ "\"RWBH\"" + "," + "\"XM\"" + "," + "\"XBM\"" + "," + "\"XB\"" + "," + "\"SFZJLXM\"" + ","
//				+ "\"SFZJLX\"" + "," + "\"SFZJHM\"" + "," + "\"LXDH\"" + "," + "\"RYLXM\"" + "," + "\"RYLX\"" + ","
//				+ "\"RYID\"" + "," + "\"JGDM\"" + "," + "\"JGMC\"" + " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?)");

			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into JCSJV2.DC_SJYYRYXXB ("
					+ "\"RWBH\"" + "," + "\"XM\"" + "," + "\"XBM\"" + "," + "\"XB\"" + "," + "\"SFZJLXM\"" + ","
					+ "\"SFZJLX\"" + "," + "\"SFZJHM\"" + "," + "\"LXDH\"" + "," + "\"RYLXM\"" + "," + "\"RYLX\"" + ","
					+ "\"RYID\"" + "," + "\"JGDM\"" + "," + "\"JGMC\"" + " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?)");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("----------DC_SJYYRYXXB 报错 ------ {} ", e.getMessage());
			e.printStackTrace();
		}
	}

	// 3.2-数据统计信息表（T_WSXC_SJTJB-104-wsxcv1）
	public void executeSqlOfSjtj3(int a, List<List<String>> FindList) throws SQLException {
		log.info("----------开始执行-3.2-数据统计信息表----T_WSXC_SJTJB--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		con.setAutoCommit(false);

		if (a == 1) {
			// 删除并写入
			Statement st = con.createStatement();
//			st.addBatch("DELETE FROM WSXCV1.T_WSXC_SJTJB");
			st.addBatch("DELETE FROM JCSJV2.T_WSXC_SJTJB");
			st.executeBatch();
			st.close();

//			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement(
//					"insert into WSXCV1.T_WSXC_SJTJB (" + "\"SZDM\"" + "," + "\"SZMC\"" + "," + "\"KQSL\"" + ","
//							+ "\"BMSSL\"" + "," + "\"KSJHBM\"" + "," + "\"KSJHMC\"" + " ) values(?,?,?,?,?,'0')");
			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement(
					"insert into JCSJV2.T_WSXC_SJTJB (" + "\"SZDM\"" + "," + "\"SZMC\"" + "," + "\"KQSL\"" + ","
							+ "\"BMSSL\"" + "," + "\"KSJHBM\"" + "," + "\"KSJHMC\"" + " ) values(?,?,?,?,?,'*')");
			// KSJHMC-考试计划写入定值

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
		} else {
			// 更新
//			PreparedStatement pst2 = (PreparedStatement) con
//					.prepareStatement("update WSXCV1.T_WSXC_SJTJB set BMSYZXSBS = ?,BMSSJZXSBS = ?,BMSSBZXL = ?,"
//							+ " KCYZXSBS = ?, KCSJZXSBS = ?, KCSBZXL = ?,SBYXJS = ? where SZDM = ? ");
			PreparedStatement pst2 = (PreparedStatement) con
					.prepareStatement("update JCSJV2.T_WSXC_SJTJB set BMSYZXSBS = ?,BMSSJZXSBS = ?,BMSSBZXL = ?,"
							+ " KCYZXSBS = ?, KCSJZXSBS = ?, KCSBZXL = ?,SBYXJS = ? where SZDM = ? ");
			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst2.setString(i + 1, minList.get(i));
				}
				pst2.addBatch();
			}
			pst2.executeBatch();
			con.commit();
			pst2.close();
		}
	}

	// 更新设备信息表（ZH_SSSBXXB_TEMP-87-OPENEXAM-中间库）
	public void executeSqlOfSbxxb2(List<List<String>> FindList) throws SQLException {
		log.info("----------开始执行-更新设备信息表----ZH_SSSBXXB_WSXC--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_JCSJK.getCode());
		con.setAutoCommit(false);

		// 只更新
		PreparedStatement pst2 = (PreparedStatement) con.prepareStatement(
				"update JCSJV2.ZH_SSSBXXB_TEMP set BZHKDID = ?,BZHKCID = ?,BZHKDMC = ? where XH = ? ");
		for (List<String> minList : FindList) {
			for (int i = 0; i < minList.size(); i++) {
				pst2.setString(i + 1, minList.get(i));
			}
			pst2.addBatch();
		}
		pst2.executeBatch();
		con.commit();
		pst2.close();
	}

	// T_TRACK_TRANAPORT_TASK  试卷跟踪 跟踪过程
	public void executeSqlOfSjysrwb(List<List<String>> FindList)  {
		log.info("----------开始执行-试卷运送任务表----T_TRACK_TRANAPORT_TASK--FindList.size()- = {} ", FindList.size());
		// 数据库链接改为：T_TRACK_TRANAPORT_TASK(试卷押送任务信息-104-sjgz)
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_GZGC.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
//			Statement st = con.createStatement();
//			st.addBatch("DELETE FROM SJGZ.T_TRACK_TRANAPORT_TASK");
//			st.executeBatch();
//			st.close();

			PreparedStatement pst1 = con.prepareStatement("insert into SJGZ.T_TRACK_TRANAPORT_TASK ("
					+ "\"RWBH\"" + "," + "\"RWLXM\"" + "," + "\"RWMC\"" + "," + "\"CFDMC\"" + "," + "\"CFDDZ\"" + ","
					+ "\"CFDBSM\"" + "," + "\"CFDJD\"" + "," + "\"CFDWD\"" + "," + "\"MDDMC\"" + "," + "\"MDDDZ\"" + ","
					+ "\"MDDBSM\"" + "," + "\"MDDJD\"" + "," + "\"MDDWD\"" + "," + "\"RWKSSJ\"" + "," + "\"RWWCZT\"" + ","
					+ "\"RWWCSJ\"" + "," + "\"ZFZRXM\"" + "," + "\"ZFZRSFZJLXM\"" + "," + "\"ZFZRSFZJLX\"" + ","
					+ "\"ZFZRSFZJHM\"" + "," + "\"KSJHBM\"" + "," + "\"CPH\""
					+ " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("----------T_TRACK_TRANAPORT_TASK 报错 ------ {} ", e.getMessage());
			e.printStackTrace();
		}
	}

	// T_TRACK_TRANAPORT_DEMAND 试卷跟踪 跟踪过程
	// ToDo -数据库链接要改-20210329
	public void executeSqlOfSjysxqb(List<List<String>> FindList)  {
		log.info("----------开始执行-试卷运送需求信息表----T_TRACK_TRANAPORT_DEMAND--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_GZGC.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
//			Statement st = con.createStatement();
//			st.addBatch("DELETE FROM SJGZ.T_TRACK_TRANAPORT_DEMAND");
//			st.executeBatch();
//			st.close();

			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ.T_TRACK_TRANAPORT_DEMAND ("
					+ "\"ID\"" + "," + "\"KMM\"" + "," + "\"KMMC\"" + "," + "\"ZJSJDSL\"" + "," + "\"ZJDTKDSL\"" + ","
					+ "\"BYJDSL\"" + "," + "\"BYJDTKDSL\"" + "," + "\"RWBH\"" + "," + "\"CPH\"" + "," + "\"CX\"" + ","
					+ "\"KSJHBM\"" + "," + "\"KSJHMC\"" + " ) values(?,?,?,?,?,?,?,?,?,?,?,?)");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("-------T_TRACK_TRANAPORT_DEMAND-- 【报错】 ,message = {} ",e.getMessage());
			e.printStackTrace();
		}
	}

	// T_TRACK_SECRECY_ROOM_RECORD 试卷跟踪 跟踪过程
	public void executeSqlOfSjcrbmsjlb(List<List<String>> FindList)  {
		log.info("----------开始执行-试卷出入保密室记录表----T_TRACK_SECRECY_ROOM_RECORD--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_GZGC.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
//			Statement st = con.createStatement();
//			st.addBatch("DELETE FROM SJGZ.T_TRACK_SECRECY_ROOM_RECORD");
//			st.executeBatch();
//			st.close();

			PreparedStatement pst1 =  con
					.prepareStatement("insert into SJGZ.T_TRACK_SECRECY_ROOM_RECORD (" + "\"ID\"" + "," + "\"KMM\"" + ","
							+ "\"KMMC\"" + "," + "\"ZJSJDSL\"" + "," + "\"ZJDTKDSL\"" + "," + "\"BYJDSL\"" + ","
							+ "\"BYJDTKDSL\"" + "," + "\"RWBH\"" + "," + "\"SJBMSID\"" + "," + "\"BMSMC\"" + ","
							+ "\"CRKLX\"" + "," + "\"SJ\"" + "," + "\"JBRXM\"" + "," + "\"KSJHBM\"" + "," + "\"KSJHMC\""
							+ " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,'*')");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("-------T_TRACK_SECRECY_ROOM_RECORD----【报错】 ,message = {}",e.getMessage());
			e.printStackTrace();
		}
	}

	// T_TRACK_EXAM_SITE_RECORD 试卷跟踪 跟踪过程
	public void executeSqlOfSjcrkdjlb(List<List<String>> FindList)  {
		log.info("----------开始执行-试卷出入考点记录表----T_TRACK_EXAM_SITE_RECORD--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_GZGC.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM SJGZ.T_TRACK_EXAM_SITE_RECORD");
			st.executeBatch();
			st.close();

			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ.T_TRACK_EXAM_SITE_RECORD ("
					+ "\"ID\"" + "," + "\"KMM\"" + "," + "\"KMMC\"" + "," + "\"ZJSJDSL\"" + "," + "\"ZJDTKDSL\"" + ","
					+ "\"BYJDSL\"" + "," + "\"BYJDTKDSL\"" + "," + "\"RWBH\"" + "," + "\"KDBH\"" + "," + "\"KDMC\"" + ","
					+ "\"CRKLX\"" + "," + "\"SJ\"" + "," + "\"JBRXM\"" + "," + "\"KSJHBM\"" + "," + "\"KSJHMC\""
					+ " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,'*')");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("-------T_TRACK_EXAM_SITE_RECORD-- 【报错】 ,message = {} ",e.getMessage());
			e.printStackTrace();
		}
	}

	// T_TRACK_CAR_LOCUS 试卷跟踪 跟踪过程
	public void executeSqlOfYsgjxx(List<List<String>> FindList)  {
		log.info("----------开始执行-试卷车辆运送轨迹信息表----T_TRACK_CAR_LOCUS--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_GZGC.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
//			Statement st = con.createStatement();
//			st.addBatch("DELETE FROM SJGZ.T_TRACK_CAR_LOCUS");
//			st.executeBatch();
//			st.close();

			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ.T_TRACK_CAR_LOCUS ("
					+ "\"ID\"" + "," + "\"RWBH\"" + "," + "\"CPH\"" + "," + "\"CLJD\"" + "," + "\"CLWD\"" + ","
					+ "\"DHSJJLSJ\""
					+ " ) values(?,?,?,?,?,?)");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("-------T_TRACK_CAR_LOCUS-- 【报错】 ,message = {} ",e.getMessage());
			e.printStackTrace();
		}
	}

	// T_TRACK_STATISTICS 试卷跟踪 主题库
	// ToDo -数据库链接要改-20210331
	public void executeSqlOfSjtj1(List<List<String>> FindList)  {
		log.info("----------开始执行-2.2.1数据统计信息表----T_TRACK_STATISTICS--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_ZTK.getCode());
		try {
			con.setAutoCommit(false);

			// 不删除数据，只新增
			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ_ZT.T_TRACK_STATISTICS ("
					+ "\"ID\"" + "," + "\"EXAM_PLAN_NUM\"" + "," + "\"VEHICLE_TERMINAL\"" + ","+ "\"VEHICLE_TERMINAL_ABNORMAL\"" + ","
					+ "\"ELECTRONIC_LABEL\"" + "," + "\"ELECTRONIC_LABEL_ABNORNAR\"" + ","
					+ "\"TRANSPORT_TASK\"" + "," + "\"TRANSPORT_TASK_FINSH\"" + ","
					+ "\"PRINT_HOUSE_SHOULD_OUT\"" + "," + "\"PRINT_HOUSE_ACTUAL_OUT\"" + ","
					+ "\"CONFIDENTIAL_ROOM_SHOULD_OUT\"" + "," + "\"CONFIDENTIAL_ROOM_AUTUAL_OUT\"" + ","
					+ "\"CONFIDENTIAL_ROOM_SHOULD_IN\"" + "," + "\"CONFIDENTIAL_ROOM_ACTUAL_IN\"" + ","
					+ "\"EXAMSITE_SHOULD_OUT\"" + "," + "\"EXAMSITE_ACTUAL_OUT\"" + ","
					+ "\"EXAMSITE_SHOULD_IN\"" + "," + "\"EXAMSITE_ACTUAL_IN\"" + ","
					+ "\"MARKING_CENTER_SHOULD_IN\"" + "," + "\"MARKING_CENTER_ACTUAL_IN\"" + ","
					+ "\"CITY_CODE\"" + "," + "\"CITY_NAME\"" + " ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)	");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
		} catch (SQLException e) {
			log.info("-------T_TRACK_STATISTICS-- 【报错】 ,message = {} ",e.getMessage());
			e.printStackTrace();
		}

	}

	// T_TRACK_ESCORT_TASKS 试卷跟踪 主题库
	public void executeSqlOfSjtj2(List<List<String>> FindList) {
		log.info("----------开始执行-2.2.2试卷跟踪主题库信息表----T_TRACK_ESCORT_TASKS--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_ZTK.getCode());
		try {
			con.setAutoCommit(false);

			// 不删除数据，只新增
			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ_ZT.T_TRACK_ESCORT_TASKS ("
					+ "\"ID\"" + "," + "\"EXAM_PLAN_NUM\"" + "," + "\"ESCORT_TASKS\"" + "," + "\"FINSH_QUANTITY\"" + ","
					+ "\"PROCESSING_QUANTITY\"" + "," + "\"UNSTARTED_QUANTITY\"" + "," + "\"WARNINGS\"" + ","
					+ "\"WARNINGS_BUSINESS\"" + "," + "\"WARNINGS_EQUIPMENT\"" + "," + "\"CAR_EQUIPMENT\"" + ","
					+ "\"CAR_EQUIPMENT_ONLINE\"" + "," + "\"ELECTRONIC_LABEL\"" + "," + "\"ELECTRONIC_LABEL_ONLINE\"" + ","
					+ "\"TEST_PAPER_PACKEGS\"" + "," + "\"ESCORT_IN_TRANSIT\"" + "," + "\"CONFIDENTIAL_ROOM_STORE\"" + ","
					+ "\"HANDOVER\"" + ","+ "\"YSCCFSL\""+ "," + "\"PJZXCFSL\"" + "   ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
		} catch (SQLException e) {
			log.info("-------T_TRACK_ESCORT_TASKS-- 【报错】 ,message = {} ",e.getMessage());
			e.printStackTrace();
		}
	}


	// T_TRACK_CAR_EQUIPMENT 试卷跟踪 主题库
	public void executeSqlOfCzzdxx(List<List<String>> FindList) {
		log.info("----------开始执行-车载终端信息----T_TRACK_CAR_EQUIPMENT--FindList.size()- = {} ", FindList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_ZTK.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM SJGZ_ZT.T_TRACK_CAR_EQUIPMENT");
			st.executeBatch();
			st.close();

			PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ_ZT.T_TRACK_CAR_EQUIPMENT ("
					+ "\"TRANSPORT_TASK_ID\"" + "," + "\"TRANSPORT_TASK_NAME\"" + "," + "\"TRANSPORT_TASK_TYPE\"" + ","
					+ "\"TERMINAL_ID\"" + "," + "\"TERMINAL_NAME\"" + "," + "\"CITY_CODE\"" + "," + "\"CITY_NAME\"" + ","
					+ "\"TRANSPORT_TASK_STATUS\"" + "," + "\"EXEM_REGION_CODE\"" + "," + "\"EXEM_REGION_NAME\"" + ","
					+ "\"ELECTRONIC_LABEL_BIND\"" + "," + "\"OUT_IN_STATUS\"" + "," + "\"ID\"" + "," + "\"EXEM_PLAN_NUM\"" + ","
					+ "\"TRANSPORT_TIME\") values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,to_date(?,'SYYYY-MM-DD HH24:MI:SS'))");

			for (List<String> minList : FindList) {
				for (int i = 0; i < minList.size(); i++) {
					pst1.setString(i + 1, minList.get(i));
				}
				pst1.addBatch();
			}
			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("插入 T_TRACK_CAR_EQUIPMENT 报错 ------- = {}",e.getMessage());
			e.printStackTrace();
		}
	}

	//T_TRACK_CAR_EQUIPMENT 试卷跟踪主题库
	public void insertCzzdxxb(List<CzzdxxItemVO> czzdxxItemVOList) {
		log.info("----------开始执行-车载终端信息----T_TRACK_CAR_EQUIPMENT--FindList.size()- = {} ", czzdxxItemVOList.size());
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_ZTK.getCode());
		try {
			con.setAutoCommit(false);

			// 先删除数据
			Statement st = con.createStatement();
			st.addBatch("DELETE FROM SJGZ_ZT.T_TRACK_CAR_EQUIPMENT");
			st.executeBatch();
			st.close();

			PreparedStatement pst1 =  con.prepareStatement("merge into T_TRACK_CAR_EQUIPMENT eq" +
					"using (select ? as ysrwbh, ? as ksjhbh" +
					" from DUAL) temp" +
					"on (eq.TRANSPORT_TASK_ID = temp.ysrwbh and eq.EXEM_PLAN_NUM = temp.ksjhbh) " +
					"WHEN MATCHED THEN UPDATE SET eq.OUT_IN_STATUS = ?" +
					"WHEN NOT MATCHED THEN INSERT (eq.ID ,eq.TRANSPORT_TASK_ID, eq.EXEM_PLAN_NUM,eq.TRANSPORT_TASK_NAME," +
					"eq.TRANSPORT_TASK_TYPE,eq.TERMINAL_ID,eq.TERMINAL_NAME,eq.CITY_CODE," +
					"eq.CITY_NAME,eq.TRANSPORT_TASK_STATUS,eq.EXEM_REGION_CODE,eq.EXEM_REGION_NAME," +
					"eq.ELECTRONIC_LABEL_BIND,eq.TRANSPORT_TIME )" +
					" VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,to_date(?,'SYYYY-MM-DD HH24:MI:SS'))");

			for (CzzdxxItemVO itemVO : czzdxxItemVOList) {
				pst1.setString(1, itemVO.getYsrwbh());
				pst1.setString(2, itemVO.getKsjhbh());
				pst1.setString(3, itemVO.getCrkzt());
				pst1.setString(4, itemVO.getYsrwbh());
				pst1.setString(5, itemVO.getKsjhbh());
				pst1.setString(6, itemVO.getYsrwmc());
				pst1.setString(7, itemVO.getRwlxm());
				pst1.setString(8, itemVO.getSczdbh());
				pst1.setString(9, itemVO.getSczdmc());
				pst1.setString(10, itemVO.getSssqdm());
				pst1.setString(11, itemVO.getSssqmc());
				pst1.setString(12, itemVO.getRwwczt());
				pst1.setString(13, itemVO.getSskqbh());
				pst1.setString(14, itemVO.getSskqmc());
				pst1.setString(15, itemVO.getBqsl());
				pst1.setString(16, itemVO.getYsrwsj());
				pst1.addBatch();
			}

			pst1.executeBatch();
			con.commit();
			pst1.close();
			con.close();
		} catch (SQLException e) {
			log.info("插入 T_TRACK_CAR_EQUIPMENT 报错 ------- = {}",e.getMessage());
			e.printStackTrace();
		}
	}



    // T_TRACK_ESCORT_TASKS 试卷跟踪主题库信息表 单条统计
    public void insertSjgzztkXxb(List<String> dataList) {
		Connection con = connectionService.getOrcacleConnection(DataBaseEnum.ORCL_SJGZ_ZTK.getCode());
        try {
            con.setAutoCommit(false);

            // 不删除数据，只新增
            PreparedStatement pst1 = (PreparedStatement) con.prepareStatement("insert into SJGZ_ZT.T_TRACK_ESCORT_TASKS ("
                    + "\"ID\"" + "," + "\"EXAM_PLAN_NUM\"" + "," + "\"ESCORT_TASKS\"" + "," + "\"FINSH_QUANTITY\"" + ","
                    + "\"PROCESSING_QUANTITY\"" + "," + "\"UNSTARTED_QUANTITY\"" + "," + "\"WARNINGS\"" + ","
                    + "\"WARNINGS_BUSINESS\"" + "," + "\"WARNINGS_EQUIPMENT\"" + "," + "\"CAR_EQUIPMENT\"" + ","
                    + "\"CAR_EQUIPMENT_ONLINE\"" + "," + "\"ELECTRONIC_LABEL\"" + "," + "\"ELECTRONIC_LABEL_ONLINE\"" + ","
                    + "\"TEST_PAPER_PACKEGS\"" + "," + "\"ESCORT_IN_TRANSIT\"" + "," + "\"CONFIDENTIAL_ROOM_STORE\"" + ","
                    + "\"HANDOVER\"" + ","+ "\"YSCCFSL\""+ "," + "\"PJZXCFSL\"" + "   ) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)");

            for (int i = 0; i < dataList.size(); i++) {
                pst1.setString(i + 1, dataList.get(i));
            }
            pst1.execute();
            con.commit();
            pst1.close();
        } catch (SQLException e) {
            log.info("-------T_TRACK_ESCORT_TASKS-- 【报错】 ,message = {} ",e.getMessage());
            e.printStackTrace();
        }
    }




	// --3.3-预警信息信息表（T_WSXC_YJXXB-104-wsxcv1）
//	public void executeSqlOfYjxx(List<List<String>> FindList) throws SQLException {
//		log.info("----------开始执行-3.3-预警信息信息表----T_WSXC_YJXXB--FindList.size()- = {} ", FindList.size());
//		Connection con = getOracle104wsxcv1Con();
//		con.setAutoCommit(false);
//
//		// 不删除数据，只新增
//		PreparedStatement pst1 = (PreparedStatement) con.prepareStatement(
//				"insert into WSXCV1.T_WSXC_YJXXB (" + "\"KSJHBM\"" + "," + "\"YJID\"" + "," + "\"YJLX\"" + ","
//				// + "\"YJXQ\"" + ","+ "\"YJMC\"" + "," + "\"KSJHMC\"" + " )
//				// values(?,?,?,?,?,'*')");
//						+ "\"YJXQ\"" + "," + "\"YJMC\"" + "," + "\"YJSJ\"" + "," + "\"KSJHMC\""
//						+ " ) values(?,?,?,?,?,?,'*')");
//
//		for (List<String> minList : FindList) {
//			for (int i = 0; i < minList.size(); i++) {
//				pst1.setString(i + 1, minList.get(i));
//			}
//			pst1.addBatch();
//		}
//		pst1.executeBatch();
//		con.commit();
//		pst1.close();
//	}

//	// 未知--装包信息表（未知库）
//	public void executeSqlOfZbxxb(List<List<String>> FindList) throws SQLException {
//		// 市州名称、考区代码、考区名称需要根据ZH_JG_SJBMSXXB的KSGLJGID在DC_KQXXB中，根据KSGLJGIDS、KSJHBH获取
//	}

}
