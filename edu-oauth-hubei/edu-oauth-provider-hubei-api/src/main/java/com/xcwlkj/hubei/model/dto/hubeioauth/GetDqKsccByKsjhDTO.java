/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.hubei.model.dto.hubeioauth;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 根据考试计划，获取当前的考试场次信息dto
 * <AUTHOR>
 * @version $Id: GetDqKsccByKsjhDTO.java, v 0.1 2021年01月15日 16时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GetDqKsccByKsjhDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号（湖北） */
    @NotBlank(message = "考试计划编号（湖北）不能为空")
    private String ksjhbh;
    /** 湖北令牌 */
    @NotBlank(message = "湖北令牌不能为空")
    private String hubeiToken;

}
