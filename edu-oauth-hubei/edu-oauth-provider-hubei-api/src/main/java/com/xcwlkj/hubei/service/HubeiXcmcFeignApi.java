/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.hubei.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.xcwlkj.core.annotation.mock.YapiMock;
import com.xcwlkj.hubei.model.dto.oauth.GetTokenDTO;
import com.xcwlkj.hubei.model.vo.oauth.GetTokenVO;
import com.xcwlkj.hubei.service.hystrix.HubeiXcmcFeignHystrix;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * 
 * <AUTHOR>
 * @version $Id: HubeiXcmcFeignApi.java, v 0.1 2020年11月06日 10时20分 xcwlkj.com Exp $
 */
@FeignClient(value = "hubei-service", fallback = HubeiXcmcFeignHystrix.class)
public interface HubeiXcmcFeignApi {

   
	/**
	 * 获取认证令牌
	 * @param getTokenDto
	 * @return
	 */
	@YapiMock(projectId="172", returnClass = GetTokenVO.class)
    @PostMapping(value = "/hubei/oauth/getToken")
    Wrapper<GetTokenVO> getToken(@RequestBody GetTokenDTO getTokenDto);
}




