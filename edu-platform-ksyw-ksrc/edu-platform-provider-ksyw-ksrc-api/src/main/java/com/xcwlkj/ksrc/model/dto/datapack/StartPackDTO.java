/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksrc.model.dto.datapack;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 开始打包dto
 * <AUTHOR>
 * @version $Id: StartPackDTO.java, v 0.1 2021年10月27日 15时42分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class StartPackDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 标准化考点编号，","分隔表示分开打包，"|"分隔表示合并打包 */
    private String bzhkdid;
    /** 打包类型 */
    @NotBlank(message = "打包类型不能为空")
    private String pkgType;

}
