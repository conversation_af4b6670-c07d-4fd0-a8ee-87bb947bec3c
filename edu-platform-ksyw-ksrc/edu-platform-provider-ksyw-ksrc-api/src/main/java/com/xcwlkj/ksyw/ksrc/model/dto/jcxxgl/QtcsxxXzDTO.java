/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.jcxxgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 10-其他场所-新增dto
 * <AUTHOR>
 * @version $Id: QtcsxxXzDTO.java, v 0.1 2020年06月24日 09时51分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class QtcsxxXzDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 关联场所标志码 */
    @NotBlank(message = "关联场所标志码不能为空")
    private String glcsbsid;
    /** 名称 */
    @NotBlank(message = "名称不能为空")
    private String qtcsmc;
    /** 地址 */
    @NotBlank(message = "地址不能为空")
    private String qtcsdz;
    /** 建筑名称 */
    @NotBlank(message = "建筑名称不能为空")
    private String jzmc;
    /** 所在楼层 */
    @NotBlank(message = "所在楼层不能为空")
    private String szlc;
    /** 场所类型 */
    @NotBlank(message = "场所类型不能为空")
    private String qtcslx;
    /** sip编号 */
    private String sipid;

}
