/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.oagwtz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 44【意见建议】-获取意见建议列表vo
 * <AUTHOR>
 * @version $Id: YjjyLbVO.java, v 0.1 2020年12月30日 15时20分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class YjjyLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 意见建议列表 */
    private List<YjjyItemVO> yjjyList;

}
