/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.sfhytsqkcl;

import java.io.Serializable;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 特殊情况处理-5.编辑特殊情况信息dto
 * <AUTHOR>
 * @version $Id: TsqkxxBjDTO.java, v 0.1 2021年04月28日 16时46分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TsqkxxBjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 情况说明 */
    @NotBlank(message = "情况说明不能为空")
    private String qksm;
    /** 标志位 */
    @NotBlank(message = "标志位不能为空")
    private String id;
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 特殊情况类型码列表 */
    @NotNull(message = "特殊情况类型码列表不能为空")
    private List<String> tsqklxmLb;
    /** 特殊情况答错区域列表 */
    private List<JSONObject> tsqkDcqyLb;

}
