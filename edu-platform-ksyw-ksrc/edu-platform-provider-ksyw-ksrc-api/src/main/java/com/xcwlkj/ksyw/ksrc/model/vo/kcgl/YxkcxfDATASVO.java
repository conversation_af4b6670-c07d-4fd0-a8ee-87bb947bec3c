/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.kcgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.alibaba.fastjson.annotation.JSONField;


/**
 * 32 -逻辑考场-启用场所列表获取vo
 * <AUTHOR>
 * @version $Id: YxkcxfDATASVO.java, v 0.1 2021年05月05日 14时45分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class YxkcxfDATASVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试场次信息 */
    @JSONField(name = "CCDYB")
    @NotNull(message = "场次对应表")
    private List<String> yxkcxfCCDYB;
	
    /** 考试场次信息 */
    @JSONField(name = "KSCC")
    private List<YxkcxfKSCCItemVO> yxkcxfKSCC;
    /** 启用场所 */
    @JSONField(name = "QYCS")
    private List<YxkcxfQYCSItemVO> yxkcxfQYCS;

}
