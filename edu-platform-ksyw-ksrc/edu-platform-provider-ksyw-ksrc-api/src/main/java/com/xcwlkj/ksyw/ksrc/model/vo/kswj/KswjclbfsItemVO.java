/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.kswj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 6-考试违纪处理办法列表vo
 * <AUTHOR>
 * @version $Id: KswjclbfsItemVO.java, v 0.1 2020年08月19日 17时32分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KswjclbfsItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标题 */
    private String bt;
    /** 内容 */
    private String nr;
    /** 类型 */
    private String lx;
    /** uuid主键 */
    private String uuid;

}
