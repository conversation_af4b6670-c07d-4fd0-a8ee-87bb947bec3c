/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.sfhysjtj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 身份核验数据统计-2.考点考场考生信息查询dto
 * <AUTHOR>
 * @version $Id: KdkcKsxxCxDTO.java, v 0.1 2021年05月11日 13时46分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdkcKsxxCxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 场所类型  考试管理机构（ksgljg） / 考点（kd） / 考场（kc） */
    @NotBlank(message = "场所类型  考试管理机构（ksgljg） / 考点（kd） / 考场（kc）不能为空")
    private String cslx;
    /** 场所编号 考试管理机构id （ksgljgid ）/  考点编号 （kdbh）/ 考场编号 （kcbh） */
    @NotBlank(message = "场所编号 考试管理机构id （ksgljgid ）/  考点编号 （kdbh）/ 考场编号 （kcbh）不能为空")
    private String csbh;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 每页显示数量 */
    @NotNull(message = "每页显示数量不能为空")
    private Integer pageSize;
    /** 是否缺考状态位 0缺考/1已入场/2或空 全部 */
    private String sfqk;
    /** 考场编号 */
    private String kcbh;
    /** 准考证号 */
    private String zkzh;
    /** 考生姓名 */
    private String ksxm;
    /** 座位号 */
    private String zwh;
    /** 特殊情况类型码 0 答错区域 /  1 条码贴错/ 9 其他  /空 全部 */
    private String tsqklxm;

}
