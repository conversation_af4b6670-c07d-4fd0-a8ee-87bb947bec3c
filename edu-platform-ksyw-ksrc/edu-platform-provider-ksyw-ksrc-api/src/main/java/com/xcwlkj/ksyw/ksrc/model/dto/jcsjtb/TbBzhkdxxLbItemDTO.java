/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.jcsjtb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


/**
 * 52 更新 同步的 标准化考点信息列表dto
 * <AUTHOR>
 * @version $Id: TbBzhkdxxLbItemDTO.java, v 0.1 2020年11月28日 16时11分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TbBzhkdxxLbItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试管理机构标志码 */
    @NotBlank(message = "考试管理机构标志码不能为空")
    private String ksgljgbzm;
    /** 名称 */
    @NotBlank(message = "名称不能为空")
    private String bzhkdmc;
    /** 简称 */
    @NotBlank(message = "简称不能为空")
    private String bzhkdjc;
    /** 考点照片 */
    private String bzhkdzp;
    /** 考点类别码 */
    @NotBlank(message = "考点类别码不能为空")
    private String kdlbm;
    /** 考点类别 */
    @NotBlank(message = "考点类别不能为空")
    private String kdlb;
    /** 考点所在省码 */
    private String kdszsfm;
    /** 考点所在省 */
    private String kdszsf;
    /** 考点所在市码 */
    private String kdszsqm;
    /** 考点所在市 */
    private String kdszsq;
    /** 考点所在区县码 */
    private String kdszqxm;
    /** 考点所在区县 */
    private String kdszqx;
    /** 考点地址 */
    @NotBlank(message = "考点地址不能为空")
    private String kddz;
    /** 考点建成时间YYYYMM */
    private String kdjcsj;
    /** 考点经度 */
    @NotNull(message = "考点经度不能为空")
    private BigDecimal kdjd;
    /** 考点纬度 */
    @NotNull(message = "考点纬度不能为空")
    private BigDecimal kdwd;
    /** 考点负责人姓名 */
    private String kdfzrxm;
    /** 考点负责人电话 */
    private String kdfzrdh;
    /** 考务负责人姓名 */
    private String kwfzrxm;
    /** 考务负责人电话 */
    private String kwfzrdh;
    /** 考务办公室电话 */
    private String kwbgsdh;
    /** 试卷保管室电话 */
    private String sjbgsdh;
    /** 视频监考室电话 */
    private String spjksdh;
    /** SIP地址 */
    private String sipuri;
    /** 技术负责人姓名 */
    private String jsfzrxm;
    /** 技术负责人电话 */
    private String jsfzrdh;
    /** 专网线路接入位置 */
    private String zwxljrwz;
    /** 专网路由IP地址 */
    private String zwlyipdz;
    /** 流媒体IP */
    private String lmtip;
    /** 流媒体端口号 */
    private String lmtdkh;
    /** 流媒体扩展端口号 */
    private String lmtkzdkh;
    /** 层次类型    K1 省属学校   K2 市属学校  K3 县属学校 */
    private String cclx;

}
