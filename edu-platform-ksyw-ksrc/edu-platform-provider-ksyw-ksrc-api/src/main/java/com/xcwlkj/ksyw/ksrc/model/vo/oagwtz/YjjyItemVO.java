/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.oagwtz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 44【意见建议】-获取意见建议列表vo
 * <AUTHOR>
 * @version $Id: YjjyItemVO.java, v 0.1 2020年12月30日 15时20分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class YjjyItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 意见建议编号 */
    private String yjbh;
    /** 标题 */
    private String bt;
    /** 提交意见时间 */
    private String tjsj;
    /** 接收单位机构编号 */
    private String jsdwjgbh;
    /** 接收单位机构名称 */
    private String jsdwjgmc;
    /** 回执状态 0未读，1已读，2已回复。 */
    private String hzzt;
    /** 回执时间 */
    private String hzsj;
    /** 公开状态 0私有 1公开 */
    private String gkzt;
    /** 考试项目编号 */
    private String kxxmbh;
    /** 考试项目名称 */
    private String ksxmmc;

}
