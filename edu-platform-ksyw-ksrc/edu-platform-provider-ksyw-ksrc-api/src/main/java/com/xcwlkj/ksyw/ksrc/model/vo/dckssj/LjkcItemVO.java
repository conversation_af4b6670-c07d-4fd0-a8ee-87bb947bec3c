/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.dckssj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 08-逻辑考场-考场管理vo
 * <AUTHOR>
 * @version $Id: LjkcItemVO.java, v 0.1 2020年04月14日 10时16分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class LjkcItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考场名称 */
    private String kcmc;
    /** 考场编号 */
    private String kcbh;

}
