package com.xcwlkj.ksyw.ksrc.model.vo.bpxx;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class BpxxVO implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;


    private String ksjhbh;
    private String ccm;
    private String kmm;
    private String kmmc;
    private String zkzh;
    private String kqmc;
    private String kdmc;
}
