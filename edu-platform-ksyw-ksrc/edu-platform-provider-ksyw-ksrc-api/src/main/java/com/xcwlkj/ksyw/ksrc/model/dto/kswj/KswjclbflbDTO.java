/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.kswj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 6-考试违纪处理办法列表dto
 * <AUTHOR>
 * @version $Id: KswjclbflbDTO.java, v 0.1 2020年08月19日 09时44分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KswjclbflbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	

}
