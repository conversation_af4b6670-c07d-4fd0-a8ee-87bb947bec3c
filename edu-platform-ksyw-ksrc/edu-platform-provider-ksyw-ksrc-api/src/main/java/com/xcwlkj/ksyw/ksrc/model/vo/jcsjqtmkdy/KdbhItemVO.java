/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.jcsjqtmkdy;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 14- 视频巡查-查询考点编号列表vo
 * <AUTHOR>
 * @version $Id: KdbhItemVO.java, v 0.1 2020年05月30日 16时27分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdbhItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考点编号 */
    private String kdbh;
    /** 标准化考点编号 */
    private String bzhkdbh;

}
