/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 陕西接口-根据考场编号获取考生信息dto
 * <AUTHOR>
 * @version $Id: SxjkGjKcbhGetKsxxDTO.java, v 0.1 2020年11月20日 16时18分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SxjkGjKcbhGetKsxxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 考场编号 */
    @NotBlank(message = "考场编号不能为空")
    private String kcbh;

}
