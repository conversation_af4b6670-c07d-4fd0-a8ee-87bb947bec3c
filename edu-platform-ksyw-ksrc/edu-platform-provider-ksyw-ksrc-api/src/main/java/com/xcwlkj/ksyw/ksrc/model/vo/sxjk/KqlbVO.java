/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 陕西接口-根据行政区码获取省市县考区vo
 * <AUTHOR>
 * @version $Id: KqlbVO.java, v 0.1 2020年11月11日 09时26分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KqlbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 本级考区名称 */
    private String bjkqmc;
    /** 本级考区编号 */
    private String bjkqbh;
    /** 直属考区列表 */
    private List<ZskqItemVO> zskqList;

}
