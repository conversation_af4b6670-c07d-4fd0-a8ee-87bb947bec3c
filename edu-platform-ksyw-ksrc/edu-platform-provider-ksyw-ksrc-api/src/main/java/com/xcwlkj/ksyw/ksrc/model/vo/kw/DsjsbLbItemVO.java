/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.vo.kw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 统计数据-需上报列表vo
 * <AUTHOR>
 * @version $Id: DsjsbLbItemVO.java, v 0.1 2020年10月30日 14时06分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DsjsbLbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 数据上报名称 */
    private String sjsbmc;
    /** 开始时间 */
    private String kssj;
    /** 结束时间 */
    private String jssj;
    /** 状态 */
    private String zt;
    /** 上报状态，0-未上报，1-已上报 */
    private String sbzt;
    /** 上报数据编号 */
    private String tjsjsbbh;
    /** 提交数据内容编号 */
    private String tjsjnrbh;

}
