/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.dckssj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 03-考场关联-标准化考场列表dto
 * <AUTHOR>
 * @version $Id: BzhkcLbDTO.java, v 0.1 2020年04月14日 10时15分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class BzhkcLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    @NotBlank(message = "不能为空")
    private String bzhkdid;

}
