/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.model.dto.bmsjkdy;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 保密室业务调用-获取考试管理机构iddto
 * <AUTHOR>
 * @version $Id: HqksgljgidDTO.java, v 0.1 2021年01月19日 22时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqksgljgidDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 考区编号 */
    @NotBlank(message = "考区编号不能为空")
    private String kqbh;

}
