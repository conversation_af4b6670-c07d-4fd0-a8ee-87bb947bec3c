/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.ksyw.ksrc.mapper.CsBzhkdMapper;
import com.xcwlkj.ksyw.ksrc.model.domain.CsBzhkd;
import com.xcwlkj.ksyw.ksrc.model.domain.CsKsgljg;
import com.xcwlkj.ksyw.ksrc.model.dto.jcxxgl.BzhkddcwdDTO;
import com.xcwlkj.ksyw.ksrc.model.dto.jcxxgl.SxDcBzhkdWdDTO;
import com.xcwlkj.ksyw.ksrc.model.enums.ReporterEnum;
import com.xcwlkj.ksyw.ksrc.model.vo.jcxxgl.BzhkdManagerItemVO;
import com.xcwlkj.ksyw.ksrc.model.vo.jcxxgl.BzhkddcwdVO;
import com.xcwlkj.ksyw.ksrc.model.vo.jcxxgl.KsgljgManagerItemVO;
import com.xcwlkj.ksyw.ksrc.model.vo.jcxxgl.SxDcBzhkdWdVO;
import com.xcwlkj.ksyw.ksrc.service.CsBzhkdService;
import com.xcwlkj.ksyw.ksrc.service.CsKsgljgService;
import com.xcwlkj.model.enums.GeneralMsgTopic;
import com.xcwlkj.model.enums.JgslbcxlxEnum;
import com.xcwlkj.model.enums.PtsjTbczmEnum;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.model.enums.SubSysEnum;
import com.xcwlkj.model.msg.PtsjGeneralModel;
import com.xcwlkj.msgque.service.XcMsgService;
import com.xcwlkj.sbyw.model.dto.dwfw.LmtfwqDTO;
import com.xcwlkj.sbyw.model.vo.dwfw.LmtfwqVO;
import com.xcwlkj.sbyw.service.DwfwXcmcFeignApi;
import com.xcwlkj.share.fileGenerator.enums.DirectionEnum;
import com.xcwlkj.share.fileGenerator.service.GeneratorFileService;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.wrapper.Wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * 标准化考点服务
 * <AUTHOR>
 * @version $Id: CsBzhkdServiceImpl.java, v 0.1 2020年04月10日 00时22分 xcwlkj.com Exp $
 */
@Service("csBzhkdService")
public class CsBzhkdServiceImpl implements CsBzhkdService {

    @Resource
    private CsBzhkdMapper modelMapper;

    @Resource
    private XcMsgService  xcMsgService;
    
    @Autowired
    private GeneratorFileService generatorFileService;
    
    @Resource
	private DwfwXcmcFeignApi dwfwXcmcFeignApi;
    
    @Autowired
    private CsKsgljgService csKsgljgService;

    @Override
    public CsBzhkd selectByKey(String bzhkdid) {
        return modelMapper.selectByPrimaryKey(bzhkdid);
    }

    @Override
    public List<CsBzhkd> selectAll() {
        return modelMapper.selectAll();
    }

    @Override
    public List<CsBzhkd> selectByExample(Example example) {
        return modelMapper.selectByExample(example);
    }

    @Override
    public void updateByKey(CsBzhkd csBzhkd) {
        modelMapper.updateByPrimaryKey(csBzhkd);
        
        //新增数据同步
        PtsjGeneralModel ptsjGeneralModel = new PtsjGeneralModel();
        ptsjGeneralModel.setMapperName("CsBzhkdMapper");
        ptsjGeneralModel.setOperateCode(PtsjTbczmEnum.UPDATE.getCode());
        ptsjGeneralModel.setParamData(JSONObject.toJSONStringWithDateFormat(csBzhkd, JSON.DEFFAULT_DATE_FORMAT));

        xcMsgService.pushNoIsolateMsg(GeneralMsgTopic.PTYWSJTB.getCode(),
            JSONObject.toJSONString(ptsjGeneralModel), SubSysEnum.JYBDJ.getCode());
    }

    @Override
    public void insert(CsBzhkd csBzhkd) {
        csBzhkd.setSczt(ScztEnum.NOTDEL.getCode());
        csBzhkd.setCreateTime(DateUtil.getCurrentDT());
        modelMapper.insert(csBzhkd);

        //新增数据同步
        PtsjGeneralModel ptsjGeneralModel = new PtsjGeneralModel();
        ptsjGeneralModel.setMapperName("CsBzhkdMapper");
        ptsjGeneralModel.setOperateCode(PtsjTbczmEnum.INSERT.getCode());
        ptsjGeneralModel.setParamData(JSONObject.toJSONStringWithDateFormat(csBzhkd, JSON.DEFFAULT_DATE_FORMAT));

        xcMsgService.pushNoIsolateMsg(GeneralMsgTopic.PTYWSJTB.getCode(),
            JSONObject.toJSONString(ptsjGeneralModel), SubSysEnum.JYBDJ.getCode());
    }

    @Override
    public int updateByExampleSelective(CsBzhkd csBzhkd, Example example) {
        csBzhkd.setUpdateTime(DateUtil.getCurrentDT());

        //新增数据同步
        PtsjGeneralModel ptsjGeneralModel = new PtsjGeneralModel();
        ptsjGeneralModel.setMapperName("CsBzhkdMapper");
        ptsjGeneralModel.setOperateCode(PtsjTbczmEnum.UPDATEXAMPLE.getCode());
        ptsjGeneralModel.setParamData(JSONObject.toJSONStringWithDateFormat(csBzhkd, JSON.DEFFAULT_DATE_FORMAT));
        ptsjGeneralModel.setExampleDate(JSONObject.toJSONString(example));

        xcMsgService.pushNoIsolateMsg(GeneralMsgTopic.PTYWSJTB.getCode(),
            JSONObject.toJSONString(ptsjGeneralModel), SubSysEnum.JYBDJ.getCode());

        return modelMapper.updateByExampleSelective(csBzhkd, example);
    }

    @Override
    public int selectCountByExample(Example example) {
        return modelMapper.selectCountByExample(example);
    }

	@Override
	public BzhkddcwdVO bzhkddcwdVO(BzhkddcwdDTO dto) {
		BzhkddcwdVO result = new BzhkddcwdVO();

        Example em_csBzhkd = new Example(CsBzhkd.class);
        Criteria cri_csBzhkd = em_csBzhkd.createCriteria();
        cri_csBzhkd.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());

        if (StringUtils.isNotBlank(dto.getBzhkdmc())) {//标准化考点名称-非必填
            cri_csBzhkd.andLike("bzhkdmc", "%" + dto.getBzhkdmc() + "%");
        }
        if (StringUtils.isNotBlank(dto.getBzhkdbh())) {//标准化考点编号-非必填
            cri_csBzhkd.andLike("bzhkdid", "%" + dto.getBzhkdbh() + "%");
        }

        //机构类型-必填
        if (dto.getJglx().equals(JgslbcxlxEnum.Bzhkd.getCode())) {

            cri_csBzhkd.andEqualTo("bzhkdid", dto.getJgid());

        }
        // 20200622  zhangrj  改为只获取直接下级的考场，即  只获取 考点下的考场，不进行跨级
        else if (dto.getJglx().equals(JgslbcxlxEnum.Ksgljg.getCode())) {

            cri_csBzhkd.andLike("ksgljgbzm", dto.getJgid().replace("00", "") + "%");
        }

        //分页查询
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<CsBzhkd> csBzhkdlist = modelMapper.selectByExample(em_csBzhkd);
        List<List<String>> dataList = new ArrayList<>();
        for (CsBzhkd kdxx : csBzhkdlist) {
			/*
			2021-4-26 10:21:52 lihaobo
			修改 去掉 技术负责人、技术负责人电话、专网接入位置
			加上   地市代码、地市名称  、县区代码、 县区名称
			 */
            List<String> data = new ArrayList<>();
            data.add(kdxx.getKdszsqm());
            data.add(kdxx.getKdszsq());
            data.add(kdxx.getKdszqxm());
            data.add(kdxx.getKdszqx());
            data.add(kdxx.getBzhkdmc());
            data.add(kdxx.getBzhkdid());
            data.add(kdxx.getKwfzrxm());
            data.add(kdxx.getKwfzrdh()); 
            data.add(kdxx.getKdfzrxm());
            data.add(kdxx.getKdfzrdh());
//            data.add(kdxx.getJsfzrxm());
//            data.add(kdxx.getJsfzrdh());
            data.add(kdxx.getKddz());
         // 专网、流媒体 相关信息
// 			LmtfwqDTO lmtfwqDTO = new LmtfwqDTO();
// 			lmtfwqDTO.setCsbh(kdxx.getBzhkdid());
// 			Wrapper<LmtfwqVO> lmtvo = dwfwXcmcFeignApi.lmtfwq(lmtfwqDTO);
// 			LmtfwqVO lmtfwqVO = lmtvo.getResult();
// 			if (!BeanUtil.isEmpty(lmtfwqVO)) {
// 				data.add(lmtfwqVO.getZwxljrwz());// 专网线路接入位置
// 			}else {
// 				data.add("-");// 专网线路接入位置
// 			}
            dataList.add(data);
		}
        
        Map<String,String> paraMap = new HashMap<>();
        paraMap.put("title",ReporterEnum.BZHKDDC.getMsg());
        String dclj = generatorFileService.generator(dto.getDclx(), ReporterEnum.BZHKDDC.getCode(),paraMap,dataList);
        result.setDclj(dclj);
        result.setTotalRows(Integer.parseInt(new PageInfo<CsBzhkd>(csBzhkdlist).getTotal() + ""));
		return result;
	}

	@Override
	public CsBzhkd selectOne(CsBzhkd csBzhkd) {
		return modelMapper.selectOne(csBzhkd);
	}

	/**
	 * [一键锁定/解锁]
	 * @date 2020年8月20日 下午4:56:30
	 * <AUTHOR>
	 * @version 1.0
	 * @param keyLock
	 * @return
	 */
	@Override
	public int oneKeyLock(String keyLock) {
		return modelMapper.oneKeyLock(keyLock);
	}

	@Override
	public SxDcBzhkdWdVO sxDcBzhkdWdVO(SxDcBzhkdWdDTO dto) {
		String qmbzm = dto.getQmbzm();
		String synr = dto.getSynr(); //水印内容
		
		SxDcBzhkdWdVO result = new SxDcBzhkdWdVO();

        Example em_csBzhkd = new Example(CsBzhkd.class);
        Criteria cri_csBzhkd = em_csBzhkd.createCriteria();
        cri_csBzhkd.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());

        if (StringUtils.isNotBlank(dto.getBzhkdmc())) {//标准化考点名称-非必填
            cri_csBzhkd.andLike("bzhkdmc", "%" + dto.getBzhkdmc() + "%");
        }
        if (StringUtils.isNotBlank(dto.getBzhkdbh())) {//标准化考点编号-非必填
            cri_csBzhkd.andLike("bzhkdid", "%" + dto.getBzhkdbh() + "%");
        }

        //机构类型-必填
        if (dto.getJglx().equals(JgslbcxlxEnum.Bzhkd.getCode())) { //是考点 

            cri_csBzhkd.andEqualTo("bzhkdid", dto.getJgid());

        }

        else if (dto.getJglx().equals(JgslbcxlxEnum.Ksgljg.getCode())) {  //是考试管理机构
        	//查所有本级和下级所有ksgljgid
        	List<String> totalKsgljgId = getTotalXjKsgljgId(dto.getJgid());
            cri_csBzhkd.andIn("ksgljgbzm", totalKsgljgId);
        }

        
        List<CsBzhkd> csBzhkdlist = modelMapper.selectByExample(em_csBzhkd);
        List<List<String>> dataList = new ArrayList<>();
        for (CsBzhkd kdxx : csBzhkdlist) {
			
            List<String> data = new ArrayList<>();
            data.add(kdxx.getBzhkdmc());
            data.add(kdxx.getBzhkdid());
            data.add(kdxx.getKwfzrxm());
            data.add(kdxx.getKwfzrdh());
            data.add(kdxx.getKdfzrxm());
            data.add(kdxx.getKdfzrdh());
            data.add(kdxx.getJsfzrxm());
            data.add(kdxx.getJsfzrdh());
            data.add(kdxx.getKddz());
         // 专网、流媒体 相关信息
 			LmtfwqDTO lmtfwqDTO = new LmtfwqDTO();
 			lmtfwqDTO.setCsbh(kdxx.getBzhkdid());
 			Wrapper<LmtfwqVO> lmtvo = dwfwXcmcFeignApi.lmtfwq(lmtfwqDTO);
 			LmtfwqVO lmtfwqVO = lmtvo.getResult();
 			if(BeanUtil.isNotEmpty(lmtfwqVO)) {
 				data.add(lmtfwqVO.getZwxljrwz());// 专网线路接入位置
 			}
 			else {
 				data.add("-");// 专网线路接入位置
 			}
            dataList.add(data);
		}
        
        Map<String,String> paraMap = new HashMap<>();
        paraMap.put("title",ReporterEnum.SXBZHKDDC.getMsg());
//        paraMap.put("direction",DirectionEnum.TRANSVERSE.getCode());  //横页
        paraMap.put("qzbzm",qmbzm);  //必填为 1 或者 0 ， 1带签名 0 不带
        paraMap.put("synr", synr); 
        String dclj = generatorFileService.generator(dto.getDclx(), ReporterEnum.SXBZHKDDC.getCode(),paraMap,dataList);
        //拿带水印文档路径
//        String targetDclj = dclj.replace(".pdf", "_1.pdf");
        result.setDclj(dclj);
		return result;
	}
	
	//查所有下级考试管理机构id
	private List<String> getTotalXjKsgljgId(String bjksgljgid){
		
		Example emCsKsgljg = new Example(CsKsgljg.class);
		Criteria criCsKsgljg = emCsKsgljg.createCriteria().andLike("jdzx", "%" + bjksgljgid + "%");  //只能查到所有下级 不包括自身
		

		List<String> jgids = new ArrayList<String>();
		List<CsKsgljg> csKsgljgs = csKsgljgService.selectByExample(emCsKsgljg);
		List<String> csKsgljgidList = csKsgljgs.stream().map(CsKsgljg :: getKsgljgid).collect(Collectors.toList());
		csKsgljgidList.add(0,bjksgljgid);  //将本级id加入list
		return csKsgljgidList;
	}

    @Override
    public String selectMaxJybdjbh(String jybdjbhHeader) {

        Example emCsBzhkd = new Example(CsBzhkd.class);
        Criteria criCsBzhkd = emCsBzhkd.createCriteria().andLike("jybdjbh", jybdjbhHeader + "%"); 
        
        List<CsBzhkd> csBzhkds = modelMapper.selectByExample(emCsBzhkd);
        Optional<String> maxJybdjbh = csBzhkds.stream().map(CsBzhkd :: getJybdjbh).max(String::compareTo);
        
        if(maxJybdjbh.isPresent())
            return maxJybdjbh.get();
        
        return "";
    }

}