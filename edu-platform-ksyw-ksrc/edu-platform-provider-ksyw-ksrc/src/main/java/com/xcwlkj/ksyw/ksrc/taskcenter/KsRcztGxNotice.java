package com.xcwlkj.ksyw.ksrc.taskcenter;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.ksyw.ksrc.model.domain.KdKdxx;
import com.xcwlkj.ksyw.ksrc.model.domain.KsKscc;
import com.xcwlkj.ksyw.ksrc.model.domain.KsKsrcxx;
import com.xcwlkj.ksyw.ksrc.model.enums.KsrcsjlyEnum;
import com.xcwlkj.ksyw.ksrc.model.enums.RedisCacheTypeEunm;
import com.xcwlkj.ksyw.ksrc.model.enums.SfhySftgEnum;
import com.xcwlkj.ksyw.ksrc.model.enums.SfhyZtEnum;
import com.xcwlkj.ksyw.ksrc.service.KdKdxxService;
import com.xcwlkj.ksyw.ksrc.service.KsKsccService;
import com.xcwlkj.ksyw.ksrc.service.KsKsrcxxService;
import com.xcwlkj.model.enums.GeneralMsgTopic;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.msgque.model.domain.XcMsgModel;
import com.xcwlkj.msgque.service.AbstractBroadCastMsgExecutor;
import com.xcwlkj.util.StringUtil;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bouncycastle.jcajce.provider.asymmetric.util.DESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 考生入场状态更新
 *
 * <AUTHOR>
 */
@Service("ksrcKsrcztGxNotice")
@Slf4j
public class KsRcztGxNotice extends AbstractBroadCastMsgExecutor {

	@Autowired
	private KsKsrcxxService ksKsrcxxService;

	@Autowired
	private KsKsccService ksKsccService;

	@Autowired
	private KdKdxxService kdKdxxService;

	@Autowired
	private RedisUtil redisUtil;

	@Override
	public void exec(XcMsgModel model) {
		String msgParam = model.getMsgParam();

		log.info("接受到的消息：{}", msgParam);

		if (StringUtil.isBlank(msgParam)) {
			log.info("message param is blank");
			return;
		}

		// 存入数据库

		/*
		 * 2021 0509 修改 之前在【hyxx】外还包括一层 【data】的 json对象 根据现在数据观察 外层【data】 已经去掉
		 */

//        JSONObject data = json.getJSONObject("data");
//        String dataOrgCode = data.getString("orgCode");
//
//        JSONArray dataHyxxArray = data.getJSONArray("hyxx");
//        JSONObject dataExam = data.getJSONObject("exam");
//        String examPlanCode = dataExam.getString("examPlanCode");  //考试计划编号
//        String examSeqCode = dataExam.getString("examSeqCode");  //场次码

		JSONObject json = JSON.parseObject(msgParam);

		JSONArray dataHyxxArray = json.getJSONArray("hyxx");

		String orgCode = json.getString("orgCode");

		JSONObject dataExam = json.getJSONObject("exam");
		String examPlanCode = dataExam.getString("examPlanCode"); // 考试计划编号
		String examSeqCode = dataExam.getString("examSeqCode"); // 场次码

		Example ksKsccExample = new Example(KsKscc.class);
		ksKsccExample.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode()).andEqualTo("ksjhbh", examPlanCode)
				.andEqualTo("ccm", examSeqCode);
		List<KsKscc> ksKsccList = ksKsccService.selectByExample(ksKsccExample);
		if (CollectionUtils.isEmpty(ksKsccList)) {
			log.info("message ksjh or cc doesn't exist");
			return;
		} else {
			if (StringUtils.equals(ksKsccList.get(0).getJshysjzt(), "1")) {
				log.info("message Stop receiving test takers’ verification data");
				return;
			}
		}

		/*
		 * 20210510 修改 逐条update给数据库压力过大 如果压力过大报错，暂缓执行 并重新 执行此次update
		 */
		
		// {"currChannel":"SFHY","id":"cbc56753fdc54d24895fd05a0a1794a2","isolate":true,"msgMonitorType":"MONITOR","msgParam":
		// "{\"exam\":{\"examPlanCode\":\"1008202201\",\"examSeqCode\":\"11\"},
		// "hyxx\":[{\"kddm\":\"K330781002\",\"yzsj\":\"2022-01-04 20:54:36\",\"kch\":\"716599\",\"szjg\":\"1\",\"zwrzjg\":\"0\",
		//"xm\":\"舒惠姗\",\"rlsbjg\":\"1\",\"zkzh\":\"2215703112\",\"zjhm\":\"330781200504084340\",\"hyjg\":\"1\"}],
		// "orgCode\":\"K330781002\"}","msgTopic":"SFHY_KSJH_HYXX","nowExecute":true,"publishTime":"2022-01-04 20:55:00","repeatInterval":0,"repeatMsg":false,"startTime":"2022-01-04 20:55:00"}

		while (true) {
			int arrSize = dataHyxxArray.size();
			int flag = 0;
			for (int i = 0; i < dataHyxxArray.size(); i++) {

				JSONObject _hyxx = dataHyxxArray.getJSONObject(i);
				String zjhm = _hyxx.getString("zjhm");
				String xm = _hyxx.getString("xm");
				String yzsj = _hyxx.getString("yzsj"); // 验证时间
				String hyjg = _hyxx.getString("hyjg"); // 核验结果 通过或者 未通过
				String zkzh = _hyxx.getString("zkzh"); // 准考证号
				String bzhkdid = _hyxx.getString("kddm"); // 考点代码 // 浙江 与竟业达的接口 此处保存的是 标准化考点的编号
				String kch = _hyxx.getString("kch"); // 考场号
				String szjg = _hyxx.getString("szjg"); // 刷证
				String rlsbjg = _hyxx.getString("rlsbjg"); // 人脸识别
				String zwrzjg = _hyxx.getString("zwrzjg"); // 指纹

				String yzfs = ""; // 验证方式
				String yzjg = ""; // 验证结果
				if (StringUtils.isNotBlank(szjg) && StringUtils.equals(szjg, SfhySftgEnum.TG.getCode())) { // 存在刷证方式
																											// 并且通过
					yzfs += SfhySftgEnum.SFZYZ.getCode();
					yzjg += SfhySftgEnum.TG.getCode();
				} else if (StringUtils.isNotBlank(szjg) && StringUtils.equals(szjg, SfhySftgEnum.WTG.getCode())) { // 存在刷证方式
																													// 并且未通过
					yzfs += SfhySftgEnum.SFZYZ.getCode();
					yzjg += SfhySftgEnum.SJKWTG.getCode();
				} else if (StringUtils.isNotBlank(szjg) && StringUtils.equals(szjg, SfhySftgEnum.CY.getCode())) {// 存在刷证方式
																													// 并且
																													// 存疑
					yzfs += SfhySftgEnum.SFZYZ.getCode();
					yzjg += SfhySftgEnum.CY.getCode();
				}

				if (StringUtils.isNotBlank(rlsbjg) && StringUtils.equals(rlsbjg, SfhySftgEnum.TG.getCode())) { // 有人脸识别方式
																												// 并且通过
					yzfs += SfhySftgEnum.RLYZ.getCode();
					yzjg += SfhySftgEnum.TG.getCode();
				} else if (StringUtils.isNotBlank(rlsbjg) && StringUtils.equals(rlsbjg, SfhySftgEnum.WTG.getCode())) { // 有人脸识别方式
																														// 并且未通过
					yzfs += SfhySftgEnum.RLYZ.getCode();
					yzjg += SfhySftgEnum.SJKWTG.getCode();
				} else if (StringUtils.isNotBlank(rlsbjg) && StringUtils.equals(rlsbjg, SfhySftgEnum.CY.getCode())) {
					yzfs += SfhySftgEnum.RLYZ.getCode();
					yzjg += SfhySftgEnum.CY.getCode();
				}

				if (StringUtils.isNotBlank(zwrzjg) && StringUtils.equals(zwrzjg, SfhySftgEnum.TG.getCode())) { // 有指纹认证
																												// 通过
					yzfs += SfhySftgEnum.ZWYZ.getCode();
					yzjg += SfhySftgEnum.TG.getCode();
				} else if (StringUtils.isNotBlank(zwrzjg) && StringUtils.equals(zwrzjg, SfhySftgEnum.WTG.getCode())) {
					yzfs += SfhySftgEnum.ZWYZ.getCode();
					yzjg += SfhySftgEnum.SJKWTG.getCode();
				} else if (StringUtils.isNotBlank(zwrzjg) && StringUtils.equals(zwrzjg, SfhySftgEnum.CY.getCode())) {
					yzfs += SfhySftgEnum.ZWYZ.getCode();
					yzjg += SfhySftgEnum.CY.getCode();
				}

				HyxxsjObj hyxxsjObj = new HyxxsjObj();
				hyxxsjObj.setHyjg(hyjg);
				hyxxsjObj.setKddm(bzhkdid);
				
				hyxxsjObj.setYzsj(yzsj);
				hyxxsjObj.setZjhm(zjhm);
				hyxxsjObj.setZkzh(zkzh);
				hyxxsjObj.setOrgCode(orgCode);

				// 缓存失败 不能影响落库
				try {
					// 20210626 写入 redis 缓存
					sendHyxxToRedis(hyxxsjObj, examPlanCode, examSeqCode, "", "");
				} catch (Exception ex) {
					ex.printStackTrace();
				}

				KsKsrcxx ksKsrcxx = new KsKsrcxx();
				ksKsrcxx.setYzfs(yzfs);
				ksKsrcxx.setYzjg(yzjg);

				if (StringUtils.equals(hyjg, SfhySftgEnum.TG.getCode())) {
					ksKsrcxx.setSfrc(SfhySftgEnum.TG.getCode()); // 核验通过 入场
					try {
						SimpleDateFormat before = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						Date dataDate = before.parse(yzsj);
						SimpleDateFormat after = new SimpleDateFormat("yyyyMMdd HHmmss");// ("YYYYMMDD HHmmss");
						SimpleDateFormat rcsjfzSdf = new SimpleDateFormat("yyyyMMddHHmm");

						ksKsrcxx.setRcsj(after.format(dataDate).toString());
						ksKsrcxx.setRcsjfz(rcsjfzSdf.format(dataDate));
					} catch (ParseException e) {
						log.error("日期格式 不符合要求");
						ksKsrcxx.setRcsj("-");
						ksKsrcxx.setRcsjfz("-");
					}

				} else {// 核验未通过 未入场
					ksKsrcxx.setSfrc(SfhySftgEnum.WTG.getCode());
					ksKsrcxx.setRcsj("-");
					ksKsrcxx.setRcsjfz("-");
				}
				
				try {
					Example example = new Example(KsKsrcxx.class);
					Criteria criteria = example.createCriteria().andEqualTo("ksjhbh", examPlanCode).andEqualTo("ccm",
							examSeqCode);
					if (StringUtils.isNotBlank(zkzh)) {
						criteria.andEqualTo("zkzh", zkzh);
					} else {
						log.info("考生准考证号为空情况出现，请注意！");
						/*
						 * 不执行无用sql 直接忽略这一条
						 */
						flag++;
						continue;
					}
					
					// 20220104  报 未通过的 时候，要验证   考点编号   （jyd 是 标注化考点id） 
					if (!StringUtils.equals(hyjg, SfhySftgEnum.TG.getCode())) {
						criteria.andEqualTo("bzhkdid", bzhkdid);
					}
					
//					Criteria criteria2 = example.createCriteria();
//			        // 20220105 zhangrj  记录是来自于 web 的数据 ，  后台的逻辑，是不允许终端上报的数据 修改 
//					criteria2.andNotEqualTo("rcsjly", KsrcsjlyEnum.WEB.getCode()); 
//					criteria2.orIsNull("rcsjly");
//					example.and(criteria2);
					
//					criteria.andEqualTo("rcsjly", KsrcsjlyEnum.PAD.getCode());

					ksKsrcxx.setRcsjly(KsrcsjlyEnum.PAD.getCode());
//					log.info("------------"+KsrcsjlyEnum.PAD.getCode());
					log.info("------------"+ksKsrcxx.getRcsjly());
//					// 20220104 zhangrj  增加考场号的判断  
//					if (StringUtils.isNotBlank(kch)) {
//						criteria.andEqualTo("kcbh", kch);
//					}
					
					ksKsrcxxService.updateByExampleSelective(ksKsrcxx, example);
					
					
				} catch (Exception e) {
					log.error(e.getMessage());
					log.info("数据库压力过大,暂缓执行");
					try {
						Thread.sleep(1000); // 暂缓1秒
					} catch (InterruptedException e1) {
						e1.printStackTrace();
					}
					e.printStackTrace();
					break; // 跳出 目的是让该数组 重新执行
				}
				flag++;
			}
			log.info("flag     " + flag + "             arrSize     " + arrSize);
			if (flag >= arrSize)
				break;
		}

		// 每次执行完 sleep 0.1s
		try {
			Thread.sleep(100); // 暂缓0.1秒
		} catch (InterruptedException e1) {
			e1.printStackTrace();
		}

	}

//    JSONObject _hyxx = dataHyxxArray.getJSONObject(i);
//    String zjhm = _hyxx.getString("zjhm");
//    String xm = _hyxx.getString("xm");
//    String yzsj = _hyxx.getString("yzsj"); //验证时间
//    String hyjg = _hyxx.getString("hyjg");
//    String  zkzh = _hyxx.getString("zkzh"); //准考证号
//    String _kddm = _hyxx.getString("kddm"); //考点代码
//    String kch = _hyxx.getString("kch"); //考场号
//    String szjg = _hyxx.getString("szjg"); //刷证
//    String rlsbjg = _hyxx.getString("rlsbjg"); //人脸识别
//    String zwrzjg = _hyxx.getString("zwrzjg"); //指纹

	/**
	 * 20210628 修改，不再计数 缺考的统计数字 key 逻辑考点编号 改为 物理考点编号 去除 T F 的分类
	 * 
	 * @param hyxxsjObj
	 * @param ksjhbh
	 * @param ccm
	 * @param dataOrgCode
	 * @param encodeMode
	 */
	public void sendHyxxToRedis(HyxxsjObj hyxxsjObj, String ksjhbh, String ccm, String dataOrgCode22,
			String encodeMode) {

		String bzhkdbh = hyxxsjObj.getKddm();
		String hyjg = hyxxsjObj.getHyjg();
		String zjhm = hyxxsjObj.getZjhm();
		String zkzh = hyxxsjObj.getZkzh();
		String yzsj = hyxxsjObj.getYzsj();

		if (StringUtils.isEmpty(zkzh)) // 准考证号为空， 不计数，无法更新数据
			return;

		String kqbh = getKqbhByBzhkdbh(bzhkdbh, ksjhbh, ccm);

		Date dtYzsj = DateUtil.parseDateTime(yzsj);

		String key = SfhyZtEnum.SFHY.getCode() + ":" + ksjhbh + ":" + ccm + ":" + kqbh + ":" + bzhkdbh;
//		String keyT = key ;// + ":" + SfhyZtEnum.T.getCode();
//		String keyF = key ;//+ ":" + SfhyZtEnum.F.getCode();

		if (StringUtils.equals(encodeMode, "1")) {
			String zjhmEncode = zjhm;
			String charset = "a793716e943b299c7cb48088a9974631f4f62f27963f2aacfe74cb4a";

			zjhmEncode = SecureUtil.des(charset.getBytes()).decryptStr(zjhm);
		}

//		long formatSec = dtYzsj.getTime();
//		String val = zjhm + ":" + zkzh + ":" + formatSec;
		String val = zjhm + ":" + zkzh;
		if (StringUtils.isBlank(zkzh)) {
//			val = zjhm + ":-1:" + formatSec;
			val = zjhm + ":-1";
		}

		if (StringUtils.isBlank(hyjg)) {

			List<Object> listRedis = redisUtil.lRange(key, 0, -1);
			List<Object> listNew = new ArrayList<>();
//			log.info("从redis中        listRedis     " + listRedis);
			if (CollectionUtil.isEmpty(listRedis) || !CollectionUtil.contains(listRedis, val)) {   // 去重     //   考虑 改为 set 的方式进行处理
				listNew.add(val);
				redisUtil.lSet(key, listNew);
			}
		} else if (StringUtils.equals(hyjg, "1")) {

			List<Object> listRedis = redisUtil.lRange(key, 0, -1);
			List<Object> listNew = new ArrayList<>();
//			log.info("从redis中        listRedis     " + listRedis);
			if (CollectionUtil.isEmpty(listRedis) || !CollectionUtil.contains(listRedis, val)) {
				listNew.add(val);
				redisUtil.lSet(key, listNew);
			}

		} else if (StringUtils.equals(hyjg, "0")) {
//			key = keyF;
		}

	}

	/**
	 * 根据标准化考点 考试计划， 获取 逻辑考点的编号
	 * 
	 * @param bzhkdbh
	 * @param examPlanCode
	 * @param examSeqCode
	 * @return
	 */
	String getKqbhByBzhkdbh(String bzhkdbh, String ksjhbh, String ccm) {

		String key = RedisCacheTypeEunm.KDDYGX.getCode() + ":" + ksjhbh + ":" + ccm + ":" + bzhkdbh;

		Object kqbhObj = redisUtil.get(key);
		log.info("getKqbhByBzhkdbh  从redis中获取       kqbh     " + kqbhObj);
		String kqbh = "";
		if (ObjectUtil.isNotEmpty(kqbhObj)) {
			kqbh = (String) kqbhObj.toString();
			log.info("从redis中获取         kqbh    " + kqbh);
		} else {
			// 从数据库中获取
			Example exampleKd = new Example(KdKdxx.class);
			Criteria criteriaKd = exampleKd.createCriteria();
			Integer ybpsl = 0;
			criteriaKd.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
			criteriaKd.andEqualTo("ksjhbh", ksjhbh);
			criteriaKd.andEqualTo("ccm", ccm);
			criteriaKd.andEqualTo("bzhkdid", bzhkdbh);
			List<KdKdxx> kdList = kdKdxxService.selectByExample(exampleKd);
			// 查询到对应kdList则去查询kwry，否则不查询
			if (CollectionUtil.isNotEmpty(kdList)) {
				kqbh = kdList.get(0).getKqbh();
				redisUtil.set(key, kqbh);
			}
			log.info("getKqbhByBzhkdbh  从数据库中获取     " + kqbh);

		}
		return kqbh;
	}

	/**
	 * 根据标准化考点 考试计划， 获取 考区 和 逻辑考点的编号
	 * 
	 * @param bzhkdbh
	 * @param examPlanCode
	 * @param examSeqCode
	 * @return
	 */
	String getKqbhKdbhByBzhkdbh(String bzhkdbh, String ksjhbh, String ccm) {

		String key = RedisCacheTypeEunm.KDDYGX.getCode() + ":" + ksjhbh + ":" + ccm + ":" + bzhkdbh;

		Object kdbhSet = redisUtil.get(key);
		log.info("从redis中获取       kdbhSet     " + kdbhSet);
		String kqbhAndKdbh = "";
		if (ObjectUtil.isNotEmpty(kdbhSet)) {
			List<String> kdbhList = (List<String>) kdbhSet;
			String obj = kdbhList.get(0);

			kqbhAndKdbh = obj.toString();
			log.info("从redis中获取         kqbhAndKdbh    " + kqbhAndKdbh);
		} else {
			// 从数据库中获取
			Example exampleKd = new Example(KdKdxx.class);
			Criteria criteriaKd = exampleKd.createCriteria();
			Integer ybpsl = 0;
			criteriaKd.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
			criteriaKd.andEqualTo("ksjhbh", ksjhbh);
			criteriaKd.andEqualTo("ccm", ccm);
			criteriaKd.andEqualTo("bzhkdid", bzhkdbh);
			List<KdKdxx> kdList = kdKdxxService.selectByExample(exampleKd);
			// 查询到对应kdList则去查询kwry，否则不查询
			if (CollectionUtil.isNotEmpty(kdList)) {
				List<String> kdbhList = Lists.newArrayList();
				for (KdKdxx kdxx : kdList) {
					kdbhList.add(kdxx.getKqbh() + ":" + kdxx.getKdbh());
				}
				redisUtil.set(key, kdbhList);
				kqbhAndKdbh = kdbhList.get(0);
			}
			log.info("从数据库中获取     " + kqbhAndKdbh);

		}
		return kqbhAndKdbh;
	}

	@Override
	public String getMsgTopic() {
		return GeneralMsgTopic.SFHY_GXRCZT.getCode();
	}

	@Override
	public String getMsgChannel() {
//		return XcTaskChannelEnum.SFHY.getCode();
		return null;
	}

	@Data
	class HyxxsjObj {
		String kddm;
		String hyjg;
		String zjhm;
		String zkzh;
		String yzsj;
		String orgCode;
	}
}
