package com.xcwlkj.ksyw.ksrc.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * [运行模式-地域分支处理]
 * <AUTHOR>
 * @data 2020年10月30日 下午3:33:52
 * @version 1.0
 * @copyright copyright (c) 2019
 * [功能注释：]
 */
public enum RunModelEnum {

	SHANXI2("61","陕西"),
	SHANXI("14","山西"),
	ZHEJIANG("33","浙江");
	
	private String code;
    private String desc;

    private RunModelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static RunModelEnum get(String code) {
        for (RunModelEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
}
