package com.xcwlkj.ksrc.third.channel.unifyaccess.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 设备状态 1-在线,0-离线,2-未激活	
 */
public enum DeviceStatusEnum {

	onLine("1","在线")
	,offLine("0","离线")
	,notActive("2","未激活");
	
	private String code;
    private String desc;
    
    private DeviceStatusEnum(String code,String desc) {
    	this.code = code;
    	this.desc = desc;
    }
    
    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }
    
    public static DeviceStatusEnum get(String code) {
        for (DeviceStatusEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
}
