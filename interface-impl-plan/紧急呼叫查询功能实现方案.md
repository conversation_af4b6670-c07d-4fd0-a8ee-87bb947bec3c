# 紧急呼叫查询功能实现方案

> 本方案基于eeip-identity-verify模块现有架构，参考考务端紧急呼叫查询功能，实现身份核验模块下的紧急呼叫查询统计功能。

---

## 1. 需求分析

### 1.1 功能概述

- **模块/业务名称**：身份核验-紧急呼叫查询统计
- **核心目标**：在身份核验模块的"查询统计"下新增"紧急呼叫查询"功能，支持按考试计划、考场号、处理状态查询紧急呼叫记录

### 1.2 功能详细需求

- **场景 1**：考务管理员查询紧急呼叫记录
  - 输入条件：考试计划（下拉框）、考场号（文本框）、处理状态（下拉框：已处理、未处理）
  - 期望输出：分页显示紧急呼叫记录列表，包含发布时间、考场号、紧呼内容、回复内容、回复时间
- **场景 2**：按时间范围查询紧急呼叫记录
  - 输入条件：考试计划 + 时间范围过滤
  - 期望输出：指定时间内的紧急呼叫统计数据

### 1.3 技术约束

- **所属系统/模块**：eeip-identity-verify
- **数据库**：复用现有表 `ks_kw_message`
- **依赖服务**：无外部依赖，基于现有考务消息系统
- **其他限制**：遵循现有架构模式，单元测试添加在eeip-standalone模块

---

## 2. 架构设计

### 2.1 设计原则

- **复用现有架构**：基于现有KsKwMessage实体和KsKwMessageMapper
- **分层设计**：Controller → Service → Mapper
- **统一响应**：`Wrapper.ok()`

### 2.2 模块依赖关系

```text
eeip-identity-verify/
├── provider/
│   └── eeip-provider-identity-verify/
│       ├── facade/manager/
│       │   └── CxtjController.java (新增接口)
│       ├── service/
│       │   └── KsKwMessageService.java (扩展方法)
│       ├── service/impl/
│       │   └── KsKwMessageServiceImpl.java (实现)
│       ├── model/dto/
│       │   └── JjhjCxDTO.java (新增)
│       ├── model/vo/
│       │   └── JjhjCxVO.java (新增)
│       └── mapper/
│           └── KsKwMessageMapper.xml (新增SQL)
└── eeip-standalone/
    └── src/test/java/ (单元测试)
```

---

## 3. 接口设计

### 3.1 REST 接口

| 编号    | 接口名称         | URL                                      | Method | 请求模型       | 响应模型           |
| ----- | ------------ | ---------------------------------------- | ------ | ---------- | -------------- |
| 3.1.1 | 紧急呼叫查询       | `/manager/identityverify/cxtj/jjhjcx`    | POST   | `JjhjCxReq` | `JjhjCxResp`   |

> **示例代码**（接口 3.1.1）：

```java
@PostMapping("/manager/identityverify/cxtj/jjhjcx")
@Permission("identityverify:cxtj:jjhjcx")
public ResponseResult<JjhjCxResp> jjhjcx(@RequestBody JjhjCxReq req) {
    JjhjCxDTO dto = new JjhjCxDTO();
    BeanUtils.copyProperties(req, dto);

    PageInfo<JjhjCxVO> pageInfo = ksKwMessageService.queryJjhjList(dto);

    JjhjCxResp resp = new JjhjCxResp();
    resp.setData(pageInfo.getList());
    resp.setTotal(pageInfo.getTotal());
    resp.setPageNum(pageInfo.getPageNum());
    resp.setPageSize(pageInfo.getPageSize());

    return Wrapper.ok(resp);
}
```

### 3.2 数据传输对象（DTO / VO / Model）

#### 3.2.1 请求模型（Req）

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class JjhjCxReq extends RemoteReqBaseModel {
    private static final long serialVersionUID = -1L;

    /**
     * 考试计划编号
     */
    private String ksjhbh;

    /**
     * 考场号
     */
    private String kcbh;

    /**
     * 处理状态: 0-未处理, 1-已处理
     */
    private String clzt;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.2 响应模型（Resp）

```java
@Data
@EqualsAndHashCode(callSuper = true)
public class JjhjCxResp extends RemoteRespBaseModel {
    private static final long serialVersionUID = -1L;

    /**
     * 数据列表
     */
    private List<JjhjCxVO> data;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页面大小
     */
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

#### 3.2.3 内部 DTO / VO 示例

```java
@Data
@EqualsAndHashCode(callSuper = false)
public class JjhjCxDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 考试计划编号
     */
    private String ksjhbh;

    /**
     * 考场号
     */
    private String kcbh;

    /**
     * 处理状态
     */
    private String clzt;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 页码
     */
    private Integer pageNum;

    /**
     * 页面大小
     */
    private Integer pageSize;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

```java
@Data
@EqualsAndHashCode(callSuper = false)
public class JjhjCxVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 消息ID
     */
    private String id;

    /**
     * 发布时间
     */
    private String fbsj;

    /**
     * 考场号
     */
    private String kcbh;

    /**
     * 紧呼内容
     */
    private String jjhjnr;

    /**
     * 回复内容
     */
    private String hfnr;

    /**
     * 回复时间
     */
    private String hfsj;

    /**
     * 处理状态: 0-未处理, 1-已处理
     */
    private String clzt;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
```

> ⚠️ **字段设计规范**：出现嵌套对象时，Req 使用独立 `DTO`，Resp 使用独立 `VO`，数组一律使用 `List`，时间一律以字符串传递

---

## 4. 数据库设计

### 4.1 表结构

- **主表**：`ks_kw_message`（考务消息表，存储紧急呼叫记录）
  - 关键字段：`msg_type='2000'`（紧急呼叫），`origin_app_type='HYAPP'`（监考端来源）
  - 状态字段：`sczt='0'`（正常记录）
- **关联表**：`ks_kw_message`（自关联，通过replay_id关联回复消息）

### 4.2 SQL / MyBatis Mapper 片段

```xml
<select id="selectJjhjList" resultType="com.xcwlkj.identityverify.model.vo.JjhjCxVO">
    SELECT
        msg.id,
        DATE_FORMAT(msg.send_time, '%Y-%m-%d %H:%i:%s') as fbsj,
        msg.origin_kcbh as kcbh,
        msg.comment as jjhjnr,
        reply.comment as hfnr,
        DATE_FORMAT(reply.send_time, '%Y-%m-%d %H:%i:%s') as hfsj,
        CASE WHEN reply.id IS NULL THEN '0' ELSE '1' END as clzt
    FROM ks_kw_message msg
    LEFT JOIN ks_kw_message reply ON msg.ksjhbh = reply.ksjhbh
        AND msg.id = reply.replay_id
        AND reply.sczt = '0'
    WHERE msg.sczt = '0'
        AND msg.msg_type = '2000'
        AND msg.origin_app_type = 'HYAPP'
        AND msg.operate = '0'
        <if test="ksjhbh != null and ksjhbh != ''">
            AND msg.ksjhbh = #{ksjhbh}
        </if>
        <if test="kcbh != null and kcbh != ''">
            AND msg.origin_kcbh LIKE CONCAT('%', #{kcbh}, '%')
        </if>
        <if test="clzt != null and clzt != ''">
            <choose>
                <when test="clzt == '0'">
                    AND reply.id IS NULL
                </when>
                <when test="clzt == '1'">
                    AND reply.id IS NOT NULL
                </when>
            </choose>
        </if>
        <if test="startTime != null and startTime != ''">
            AND msg.send_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND msg.send_time <= #{endTime}
        </if>
    ORDER BY msg.send_time DESC
</select>
```

---

## 5. 业务逻辑实现

### 5.1 Service 接口

```java
public interface KsKwMessageService extends BaseService<KsKwMessage> {

    /**
     * 查询紧急呼叫列表
     */
    PageInfo<JjhjCxVO> queryJjhjList(JjhjCxDTO dto);
}
```

### 5.2 Service 实现要点

1. **参数校验**：验证必填参数，时间格式校验
2. **分页查询**：使用PageHelper进行分页
3. **VO/DTO 转换**：BeanUtils复制属性
4. **异常与日志**：记录查询异常，日志输出关键信息

### 5.3 Controller 要点

- **BeanUtils 复制**：Req → DTO，VO → Resp
- **统一异常处理**：通过GlobalExceptionHandler处理
- **权限控制**：使用@Permission注解

---

## 6. 单元测试设计

| 用例编号  | 场景        | 关键步骤                | 断言                           |
| ----- | --------- | ------------------- | ---------------------------- |
| 6.1.1 | 正常查询      | 调用 `queryJjhjList`  | 返回PageInfo size >= 0        |
| 6.1.2 | 按考试计划查询   | 传入ksjhbh             | 返回数据中ksjhbh匹配               |
| 6.1.3 | 按考场号查询    | 传入kcbh              | 返回数据中kcbh包含查询条件            |
| 6.1.4 | 按处理状态查询   | 传入clzt='0'          | 返回数据中hfnr为空（未处理）           |
| 6.1.5 | 参数缺失      | 传空 DTO              | 抛 `IllegalArgumentException` |
| 6.1.6 | 分页功能测试    | pageNum=1, pageSize=10 | 验证分页信息正确                     |

> **技术栈**：JUnit5 + Spring Boot Test；使用 MockMvc 测试 Controller，@Transactional 保证测试数据回滚。

---

## 7. 实施计划

| 阶段      | 任务                      | 预计工时  |
| ------- | ----------------------- | ----- |
| Phase 1 | DTO / VO / Req / Resp 创建 | 0.5 d |
| Phase 2 | Mapper XML SQL 编写       | 0.5 d |
| Phase 3 | Service 层扩展             | 1 d   |
| Phase 4 | Controller 新增接口         | 0.5 d |
| Phase 5 | 单元测试编写                  | 0.5 d |
| Phase 6 | 集成测试 & 联调               | 0.5 d |

---

## 8. 风险评估与应对

| 风险        | 影响       | 应对措施                    |
| --------- | -------- | ----------------------- |
| 性能瓶颈      | 大数据量查询慢  | 在send_time字段建索引，合理分页    |
| 数据一致性问题   | 回复关联错误   | 严格按replay_id关联，添加数据校验   |
| 权限控制风险    | 非授权访问    | 使用@Permission注解，统一权限管理  |
| 需求变更      | 查询条件增加   | 保持DTO可扩展，预留扩展字段         |

---

## 9. 总结

- **架构一致**：遵循eeip-identity-verify模块现有三层架构
- **代码复用**：基于现有KsKwMessage实体和相关服务
- **测试完备**：单元测试 + 集成测试，确保功能稳定性
- **性能优化**：合理使用索引和分页，支持大数据量查询

> **备注**：本方案充分复用现有考务消息系统架构，最小化开发工作量，确保与现有功能的一致性和稳定性。
