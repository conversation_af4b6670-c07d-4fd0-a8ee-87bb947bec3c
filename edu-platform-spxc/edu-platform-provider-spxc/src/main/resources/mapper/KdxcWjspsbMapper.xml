<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.spxc.mapper.KdxcWjspsbMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.spxc.model.domain.KdxcWjspsb">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="wjks" jdbcType="VARCHAR" property="wjks" />
        <result column="kszkzh" jdbcType="VARCHAR" property="kszkzh" />
        <result column="wjsj" jdbcType="VARCHAR" property="wjsj" />
        <result column="kcbh" jdbcType="VARCHAR" property="kcbh" />
        <result column="kdbh" jdbcType="VARCHAR" property="kdbh" />
        <result column="wjlxm" jdbcType="VARCHAR" property="wjlxm" />
        <result column="wjlx" jdbcType="VARCHAR" property="wjlx" />
        <result column="wjxmm" jdbcType="VARCHAR" property="wjxmm" />
        <result column="wjxm" jdbcType="VARCHAR" property="wjxm" />
        <result column="sbrid" jdbcType="VARCHAR" property="sbrid" />
        <result column="sbrmc" jdbcType="VARCHAR" property="sbrmc" />
        <result column="sbrsjh" jdbcType="VARCHAR" property="sbrsjh" />
        <result column="sbrjgid" jdbcType="VARCHAR" property="sbrjgid" />
        <result column="sbrjgmc" jdbcType="VARCHAR" property="sbrjgmc" />
        <result column="sbrq" jdbcType="VARCHAR" property="sbrq" />
        <result column="sbsj" jdbcType="VARCHAR" property="sbsj" />
        <result column="sjdj" jdbcType="VARCHAR" property="sjdj" />
        <result column="sjms" jdbcType="VARCHAR" property="sjms" />
        <result column="cz" jdbcType="VARCHAR" property="cz" />
        <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
        <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="ksjhbh" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="ccm" jdbcType="VARCHAR" property="ccm" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        wjks,
        kszkzh,
        wjsj,
        kcbh,
        kdbh,
        wjlxm,
        wjlx,
        wjxmm,
        wjxm,
        sbrid,
        sbrmc,
        sbrsjh,
        sbrjgid,
        sbrjgmc,
        sbrq,
        sbsj,
        sjdj,
        sjms,
        cz,
        createtime,
        updatetime,
        sczt,
        ksjhbh,
        ccm

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="wjks != null and wjks != ''">
            AND wjks = #{wjks,jdbcType=VARCHAR}
        </if>
        <if test="kszkzh != null and kszkzh != ''">
            AND kszkzh = #{kszkzh,jdbcType=VARCHAR}
        </if>
        <if test="wjsj != null and wjsj != ''">
            AND wjsj = #{wjsj,jdbcType=VARCHAR}
        </if>
        <if test="kcbh != null and kcbh != ''">
            AND kcbh = #{kcbh,jdbcType=VARCHAR}
        </if>
        <if test="kdbh != null and kdbh != ''">
            AND kdbh = #{kdbh,jdbcType=VARCHAR}
        </if>
        <if test="wjlxm != null and wjlxm != ''">
            AND wjlxm = #{wjlxm,jdbcType=VARCHAR}
        </if>
        <if test="wjlx != null and wjlx != ''">
            AND wjlx = #{wjlx,jdbcType=VARCHAR}
        </if>
        <if test="wjxmm != null and wjxmm != ''">
            AND wjxmm = #{wjxmm,jdbcType=VARCHAR}
        </if>
        <if test="wjxm != null and wjxm != ''">
            AND wjxm = #{wjxm,jdbcType=VARCHAR}
        </if>
        <if test="sbrid != null and sbrid != ''">
            AND sbrid = #{sbrid,jdbcType=VARCHAR}
        </if>
        <if test="sbrmc != null and sbrmc != ''">
            AND sbrmc = #{sbrmc,jdbcType=VARCHAR}
        </if>
        <if test="sbrsjh != null and sbrsjh != ''">
            AND sbrsjh = #{sbrsjh,jdbcType=VARCHAR}
        </if>
        <if test="sbrjgid != null and sbrjgid != ''">
            AND sbrjgid = #{sbrjgid,jdbcType=VARCHAR}
        </if>
        <if test="sbrjgmc != null and sbrjgmc != ''">
            AND sbrjgmc = #{sbrjgmc,jdbcType=VARCHAR}
        </if>
        <if test="sbrq != null and sbrq != ''">
            AND sbrq = #{sbrq,jdbcType=VARCHAR}
        </if>
        <if test="sbsj != null and sbsj != ''">
            AND sbsj = #{sbsj,jdbcType=VARCHAR}
        </if>
        <if test="sjdj != null and sjdj != ''">
            AND sjdj = #{sjdj,jdbcType=VARCHAR}
        </if>
        <if test="sjms != null and sjms != ''">
            AND sjms = #{sjms,jdbcType=VARCHAR}
        </if>
        <if test="cz != null and cz != ''">
            AND cz = #{cz,jdbcType=VARCHAR}
        </if>
        <if test="createtime != null and createtime != ''">
            AND createtime = #{createtime,jdbcType=TIMESTAMP}
        </if>
        <if test="updatetime != null and updatetime != ''">
            AND updatetime = #{updatetime,jdbcType=TIMESTAMP}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND ksjhbh = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="ccm != null and ccm != ''">
            AND ccm = #{ccm,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="wjks != null ">
            wjks = #{wjks,jdbcType=VARCHAR},
        </if>
        <if test="kszkzh != null ">
            kszkzh = #{kszkzh,jdbcType=VARCHAR},
        </if>
        <if test="wjsj != null ">
            wjsj = #{wjsj,jdbcType=VARCHAR},
        </if>
        <if test="kcbh != null ">
            kcbh = #{kcbh,jdbcType=VARCHAR},
        </if>
        <if test="kdbh != null ">
            kdbh = #{kdbh,jdbcType=VARCHAR},
        </if>
        <if test="wjlxm != null ">
            wjlxm = #{wjlxm,jdbcType=VARCHAR},
        </if>
        <if test="wjlx != null ">
            wjlx = #{wjlx,jdbcType=VARCHAR},
        </if>
        <if test="wjxmm != null ">
            wjxmm = #{wjxmm,jdbcType=VARCHAR},
        </if>
        <if test="wjxm != null ">
            wjxm = #{wjxm,jdbcType=VARCHAR},
        </if>
        <if test="sbrid != null ">
            sbrid = #{sbrid,jdbcType=VARCHAR},
        </if>
        <if test="sbrmc != null ">
            sbrmc = #{sbrmc,jdbcType=VARCHAR},
        </if>
        <if test="sbrsjh != null ">
            sbrsjh = #{sbrsjh,jdbcType=VARCHAR},
        </if>
        <if test="sbrjgid != null ">
            sbrjgid = #{sbrjgid,jdbcType=VARCHAR},
        </if>
        <if test="sbrjgmc != null ">
            sbrjgmc = #{sbrjgmc,jdbcType=VARCHAR},
        </if>
        <if test="sbrq != null ">
            sbrq = #{sbrq,jdbcType=VARCHAR},
        </if>
        <if test="sbsj != null ">
            sbsj = #{sbsj,jdbcType=VARCHAR},
        </if>
        <if test="sjdj != null ">
            sjdj = #{sjdj,jdbcType=VARCHAR},
        </if>
        <if test="sjms != null ">
            sjms = #{sjms,jdbcType=VARCHAR},
        </if>
        <if test="cz != null ">
            cz = #{cz,jdbcType=VARCHAR},
        </if>
        <if test="createtime != null ">
            createtime = #{createtime,jdbcType=TIMESTAMP},
        </if>
        <if test="updatetime != null ">
            updatetime = #{updatetime,jdbcType=TIMESTAMP},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="ksjhbh != null ">
            ksjhbh = #{ksjhbh,jdbcType=VARCHAR},
        </if>
        <if test="ccm != null ">
            ccm = #{ccm,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.spxc.model.domain.KdxcWjspsb"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from kdxc_wjspsb
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
