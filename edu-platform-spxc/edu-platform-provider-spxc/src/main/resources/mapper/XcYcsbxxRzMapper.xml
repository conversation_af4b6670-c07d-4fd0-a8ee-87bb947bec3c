<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.spxc.mapper.XcYcsbxxRzMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.spxc.model.domain.XcYcsbxxRz">
        <result column="id" jdbcType="VARCHAR" property="id" />
        <result column="xcsbxxid" jdbcType="VARCHAR" property="xcsbxxid" />
        <result column="clrbh" jdbcType="VARCHAR" property="clrbh" />
        <result column="clrxm" jdbcType="VARCHAR" property="clrxm" />
        <result column="clzt" jdbcType="VARCHAR" property="clzt" />
        <result column="clyj" jdbcType="VARCHAR" property="clyj" />
        <result column="createtime" jdbcType="TIMESTAMP" property="createtime" />
        <result column="updatetime" jdbcType="TIMESTAMP" property="updatetime" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        xcsbxxid,
        clrbh,
        clrxm,
        clzt,
        clyj,
        createtime,
        updatetime,
        sczt

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="xcsbxxid != null and xcsbxxid != ''">
            AND xcsbxxid = #{xcsbxxid,jdbcType=VARCHAR}
        </if>
        <if test="clrbh != null and clrbh != ''">
            AND clrbh = #{clrbh,jdbcType=VARCHAR}
        </if>
        <if test="clrxm != null and clrxm != ''">
            AND clrxm = #{clrxm,jdbcType=VARCHAR}
        </if>
        <if test="clzt != null and clzt != ''">
            AND clzt = #{clzt,jdbcType=VARCHAR}
        </if>
        <if test="clyj != null and clyj != ''">
            AND clyj = #{clyj,jdbcType=VARCHAR}
        </if>
        <if test="createtime != null and createtime != ''">
            AND createtime = #{createtime,jdbcType=TIMESTAMP}
        </if>
        <if test="updatetime != null and updatetime != ''">
            AND updatetime = #{updatetime,jdbcType=TIMESTAMP}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="xcsbxxid != null ">
            xcsbxxid = #{xcsbxxid,jdbcType=VARCHAR},
        </if>
        <if test="clrbh != null ">
            clrbh = #{clrbh,jdbcType=VARCHAR},
        </if>
        <if test="clrxm != null ">
            clrxm = #{clrxm,jdbcType=VARCHAR},
        </if>
        <if test="clzt != null ">
            clzt = #{clzt,jdbcType=VARCHAR},
        </if>
        <if test="clyj != null ">
            clyj = #{clyj,jdbcType=VARCHAR},
        </if>
        <if test="createtime != null ">
            createtime = #{createtime,jdbcType=TIMESTAMP},
        </if>
        <if test="updatetime != null ">
            updatetime = #{updatetime,jdbcType=TIMESTAMP},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.spxc.model.domain.XcYcsbxxRz"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from xc_ycsbxx_rz
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
