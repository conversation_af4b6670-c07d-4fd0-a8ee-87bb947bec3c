<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.spxc.mapper.KdxcWjCkWjwgmd1Mapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.spxc.model.domain.KdxcWjCkWjwgmd1">
        <id column="bzid" jdbcType="VARCHAR" property="bzid" />
        <result column="cljdsbh" jdbcType="VARCHAR" property="cljdsbh" />
        <result column="wjwgclbh" jdbcType="VARCHAR" property="wjwgclbh" />
        <result column="ksh" jdbcType="VARCHAR" property="ksh" />
        <result column="xm" jdbcType="VARCHAR" property="xm" />
        <result column="kmmc" jdbcType="VARCHAR" property="kmmc" />
        <result column="kmdm" jdbcType="VARCHAR" property="kmdm" />
        <result column="kh" jdbcType="VARCHAR" property="kh" />
        <result column="dm" jdbcType="VARCHAR" property="dm" />
        <result column="sd" jdbcType="VARCHAR" property="sd" />
        <result column="wglxdm" jdbcType="VARCHAR" property="wglxdm" />
        <result column="wgssdm" jdbcType="VARCHAR" property="wgssdm" />
        <result column="wgss" jdbcType="VARCHAR" property="wgss" />
        <result column="wgcldm" jdbcType="VARCHAR" property="wgcldm" />
        <result column="wgcl" jdbcType="VARCHAR" property="wgcl" />
        <result column="kssj" jdbcType="DATE" property="kssj" />
        <result column="sxw" jdbcType="VARCHAR" property="sxw" />
        <result column="sz" jdbcType="BIGINT" property="sz" />
        <result column="wz" jdbcType="BIGINT" property="wz" />
        <result column="zr" jdbcType="BIGINT" property="zr" />
        <result column="xcjl" jdbcType="BIGINT" property="xcjl" />
        <result column="tl1" jdbcType="VARCHAR" property="tl1" />
        <result column="tl2" jdbcType="VARCHAR" property="tl2" />
        <result column="zch" jdbcType="VARCHAR" property="zch" />
        <result column="kslx" jdbcType="VARCHAR" property="kslx" />
        <result column="nf" jdbcType="VARCHAR" property="nf" />
        <result column="wgsssm" jdbcType="VARCHAR" property="wgsssm" />
        <result column="xbdm" jdbcType="VARCHAR" property="xbdm" />
        <result column="kslbdm" jdbcType="VARCHAR" property="kslbdm" />
        <result column="sfzh" jdbcType="VARCHAR" property="sfzh" />
        <result column="bz" jdbcType="VARCHAR" property="bz" />
        <result column="zkzh" jdbcType="VARCHAR" property="zkzh" />
        <result column="bh" jdbcType="VARCHAR" property="bh" />
        <result column="jtwgxw" jdbcType="VARCHAR" property="jtwgxw" />
        <result column="wgsjxs" jdbcType="VARCHAR" property="wgsjxs" />
        <result column="wgsjfz" jdbcType="VARCHAR" property="wgsjfz" />
        <result column="kqmc" jdbcType="VARCHAR" property="kqmc" />
        <result column="kdmc" jdbcType="VARCHAR" property="kdmc" />
        <result column="qrry" jdbcType="VARCHAR" property="qrry" />
        <result column="ksrq" jdbcType="VARCHAR" property="ksrq" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="tjzt" jdbcType="VARCHAR" property="tjzt" />
        <result column="ksgljgid" jdbcType="VARCHAR" property="ksgljgid" />
        <result column="sfdccjs" jdbcType="VARCHAR" property="sfdccjs" />
        <result column="kscc" jdbcType="VARCHAR" property="kscc" />
        <result column="ksjh" jdbcType="VARCHAR" property="ksjh" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        bzid,
        cljdsbh,
        wjwgclbh,
        ksh,
        xm,
        kmmc,
        kmdm,
        kh,
        dm,
        sd,
        wglxdm,
        wgssdm,
        wgss,
        wgcldm,
        wgcl,
        kssj,
        sxw,
        sz,
        wz,
        zr,
        xcjl,
        tl1,
        tl2,
        zch,
        kslx,
        nf,
        wgsssm,
        xbdm,
        kslbdm,
        sfzh,
        bz,
        zkzh,
        bh,
        jtwgxw,
        wgsjxs,
        wgsjfz,
        kqmc,
        kdmc,
        qrry,
        ksrq,
        sczt,
        create_time,
        tjzt,
        ksgljgid,
        sfdccjs,
        kscc,
        ksjh

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="bzid != null and bzid != ''">
            AND bzid = #{bzid,jdbcType=VARCHAR}
        </if>
        <if test="cljdsbh != null and cljdsbh != ''">
            AND cljdsbh = #{cljdsbh,jdbcType=VARCHAR}
        </if>
        <if test="wjwgclbh != null and wjwgclbh != ''">
            AND wjwgclbh = #{wjwgclbh,jdbcType=VARCHAR}
        </if>
        <if test="ksh != null and ksh != ''">
            AND ksh = #{ksh,jdbcType=VARCHAR}
        </if>
        <if test="xm != null and xm != ''">
            AND xm = #{xm,jdbcType=VARCHAR}
        </if>
        <if test="kmmc != null and kmmc != ''">
            AND kmmc = #{kmmc,jdbcType=VARCHAR}
        </if>
        <if test="kmdm != null and kmdm != ''">
            AND kmdm = #{kmdm,jdbcType=VARCHAR}
        </if>
        <if test="kh != null and kh != ''">
            AND kh = #{kh,jdbcType=VARCHAR}
        </if>
        <if test="dm != null and dm != ''">
            AND dm = #{dm,jdbcType=VARCHAR}
        </if>
        <if test="sd != null and sd != ''">
            AND sd = #{sd,jdbcType=VARCHAR}
        </if>
        <if test="wglxdm != null and wglxdm != ''">
            AND wglxdm = #{wglxdm,jdbcType=VARCHAR}
        </if>
        <if test="wgssdm != null and wgssdm != ''">
            AND wgssdm = #{wgssdm,jdbcType=VARCHAR}
        </if>
        <if test="wgss != null and wgss != ''">
            AND wgss = #{wgss,jdbcType=VARCHAR}
        </if>
        <if test="wgcldm != null and wgcldm != ''">
            AND wgcldm = #{wgcldm,jdbcType=VARCHAR}
        </if>
        <if test="wgcl != null and wgcl != ''">
            AND wgcl = #{wgcl,jdbcType=VARCHAR}
        </if>
        <if test="kssj != null and kssj != ''">
            AND kssj = #{kssj,jdbcType=DATE}
        </if>
        <if test="sxw != null and sxw != ''">
            AND sxw = #{sxw,jdbcType=VARCHAR}
        </if>
        <if test="sz != null and sz != ''">
            AND sz = #{sz,jdbcType=BIGINT}
        </if>
        <if test="wz != null and wz != ''">
            AND wz = #{wz,jdbcType=BIGINT}
        </if>
        <if test="zr != null and zr != ''">
            AND zr = #{zr,jdbcType=BIGINT}
        </if>
        <if test="xcjl != null and xcjl != ''">
            AND xcjl = #{xcjl,jdbcType=BIGINT}
        </if>
        <if test="tl1 != null and tl1 != ''">
            AND tl1 = #{tl1,jdbcType=VARCHAR}
        </if>
        <if test="tl2 != null and tl2 != ''">
            AND tl2 = #{tl2,jdbcType=VARCHAR}
        </if>
        <if test="zch != null and zch != ''">
            AND zch = #{zch,jdbcType=VARCHAR}
        </if>
        <if test="kslx != null and kslx != ''">
            AND kslx = #{kslx,jdbcType=VARCHAR}
        </if>
        <if test="nf != null and nf != ''">
            AND nf = #{nf,jdbcType=VARCHAR}
        </if>
        <if test="wgsssm != null and wgsssm != ''">
            AND wgsssm = #{wgsssm,jdbcType=VARCHAR}
        </if>
        <if test="xbdm != null and xbdm != ''">
            AND xbdm = #{xbdm,jdbcType=VARCHAR}
        </if>
        <if test="kslbdm != null and kslbdm != ''">
            AND kslbdm = #{kslbdm,jdbcType=VARCHAR}
        </if>
        <if test="sfzh != null and sfzh != ''">
            AND sfzh = #{sfzh,jdbcType=VARCHAR}
        </if>
        <if test="bz != null and bz != ''">
            AND bz = #{bz,jdbcType=VARCHAR}
        </if>
        <if test="zkzh != null and zkzh != ''">
            AND zkzh = #{zkzh,jdbcType=VARCHAR}
        </if>
        <if test="bh != null and bh != ''">
            AND bh = #{bh,jdbcType=VARCHAR}
        </if>
        <if test="jtwgxw != null and jtwgxw != ''">
            AND jtwgxw = #{jtwgxw,jdbcType=VARCHAR}
        </if>
        <if test="wgsjxs != null and wgsjxs != ''">
            AND wgsjxs = #{wgsjxs,jdbcType=VARCHAR}
        </if>
        <if test="wgsjfz != null and wgsjfz != ''">
            AND wgsjfz = #{wgsjfz,jdbcType=VARCHAR}
        </if>
        <if test="kqmc != null and kqmc != ''">
            AND kqmc = #{kqmc,jdbcType=VARCHAR}
        </if>
        <if test="kdmc != null and kdmc != ''">
            AND kdmc = #{kdmc,jdbcType=VARCHAR}
        </if>
        <if test="qrry != null and qrry != ''">
            AND qrry = #{qrry,jdbcType=VARCHAR}
        </if>
        <if test="ksrq != null and ksrq != ''">
            AND ksrq = #{ksrq,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="tjzt != null and tjzt != ''">
            AND tjzt = #{tjzt,jdbcType=VARCHAR}
        </if>
        <if test="ksgljgid != null and ksgljgid != ''">
            AND ksgljgid = #{ksgljgid,jdbcType=VARCHAR}
        </if>
        <if test="sfdccjs != null and sfdccjs != ''">
            AND sfdccjs = #{sfdccjs,jdbcType=VARCHAR}
        </if>
        <if test="kscc != null and kscc != ''">
            AND kscc = #{kscc,jdbcType=VARCHAR}
        </if>
        <if test="ksjh != null and ksjh != ''">
            AND ksjh = #{ksjh,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="bzid != null ">
            bzid = #{bzid,jdbcType=VARCHAR},
        </if>
        <if test="cljdsbh != null ">
            cljdsbh = #{cljdsbh,jdbcType=VARCHAR},
        </if>
        <if test="wjwgclbh != null ">
            wjwgclbh = #{wjwgclbh,jdbcType=VARCHAR},
        </if>
        <if test="ksh != null ">
            ksh = #{ksh,jdbcType=VARCHAR},
        </if>
        <if test="xm != null ">
            xm = #{xm,jdbcType=VARCHAR},
        </if>
        <if test="kmmc != null ">
            kmmc = #{kmmc,jdbcType=VARCHAR},
        </if>
        <if test="kmdm != null ">
            kmdm = #{kmdm,jdbcType=VARCHAR},
        </if>
        <if test="kh != null ">
            kh = #{kh,jdbcType=VARCHAR},
        </if>
        <if test="dm != null ">
            dm = #{dm,jdbcType=VARCHAR},
        </if>
        <if test="sd != null ">
            sd = #{sd,jdbcType=VARCHAR},
        </if>
        <if test="wglxdm != null ">
            wglxdm = #{wglxdm,jdbcType=VARCHAR},
        </if>
        <if test="wgssdm != null ">
            wgssdm = #{wgssdm,jdbcType=VARCHAR},
        </if>
        <if test="wgss != null ">
            wgss = #{wgss,jdbcType=VARCHAR},
        </if>
        <if test="wgcldm != null ">
            wgcldm = #{wgcldm,jdbcType=VARCHAR},
        </if>
        <if test="wgcl != null ">
            wgcl = #{wgcl,jdbcType=VARCHAR},
        </if>
        <if test="kssj != null ">
            kssj = #{kssj,jdbcType=DATE},
        </if>
        <if test="sxw != null ">
            sxw = #{sxw,jdbcType=VARCHAR},
        </if>
        <if test="sz != null ">
            sz = #{sz,jdbcType=BIGINT},
        </if>
        <if test="wz != null ">
            wz = #{wz,jdbcType=BIGINT},
        </if>
        <if test="zr != null ">
            zr = #{zr,jdbcType=BIGINT},
        </if>
        <if test="xcjl != null ">
            xcjl = #{xcjl,jdbcType=BIGINT},
        </if>
        <if test="tl1 != null ">
            tl1 = #{tl1,jdbcType=VARCHAR},
        </if>
        <if test="tl2 != null ">
            tl2 = #{tl2,jdbcType=VARCHAR},
        </if>
        <if test="zch != null ">
            zch = #{zch,jdbcType=VARCHAR},
        </if>
        <if test="kslx != null ">
            kslx = #{kslx,jdbcType=VARCHAR},
        </if>
        <if test="nf != null ">
            nf = #{nf,jdbcType=VARCHAR},
        </if>
        <if test="wgsssm != null ">
            wgsssm = #{wgsssm,jdbcType=VARCHAR},
        </if>
        <if test="xbdm != null ">
            xbdm = #{xbdm,jdbcType=VARCHAR},
        </if>
        <if test="kslbdm != null ">
            kslbdm = #{kslbdm,jdbcType=VARCHAR},
        </if>
        <if test="sfzh != null ">
            sfzh = #{sfzh,jdbcType=VARCHAR},
        </if>
        <if test="bz != null ">
            bz = #{bz,jdbcType=VARCHAR},
        </if>
        <if test="zkzh != null ">
            zkzh = #{zkzh,jdbcType=VARCHAR},
        </if>
        <if test="bh != null ">
            bh = #{bh,jdbcType=VARCHAR},
        </if>
        <if test="jtwgxw != null ">
            jtwgxw = #{jtwgxw,jdbcType=VARCHAR},
        </if>
        <if test="wgsjxs != null ">
            wgsjxs = #{wgsjxs,jdbcType=VARCHAR},
        </if>
        <if test="wgsjfz != null ">
            wgsjfz = #{wgsjfz,jdbcType=VARCHAR},
        </if>
        <if test="kqmc != null ">
            kqmc = #{kqmc,jdbcType=VARCHAR},
        </if>
        <if test="kdmc != null ">
            kdmc = #{kdmc,jdbcType=VARCHAR},
        </if>
        <if test="qrry != null ">
            qrry = #{qrry,jdbcType=VARCHAR},
        </if>
        <if test="ksrq != null ">
            ksrq = #{ksrq,jdbcType=VARCHAR},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="tjzt != null ">
            tjzt = #{tjzt,jdbcType=VARCHAR},
        </if>
        <if test="ksgljgid != null ">
            ksgljgid = #{ksgljgid,jdbcType=VARCHAR},
        </if>
        <if test="sfdccjs != null ">
            sfdccjs = #{sfdccjs,jdbcType=VARCHAR},
        </if>
        <if test="kscc != null ">
            kscc = #{kscc,jdbcType=VARCHAR},
        </if>
        <if test="ksjh != null ">
            ksjh = #{ksjh,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.spxc.model.domain.KdxcWjCkWjwgmd1"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from kdxc_wj_ck_wjwgmd1
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
	
	<update id="update">
		update kdxc_wj_ck_wjwgmd1 set tjzt=#{tjzt} where tjzt=#{tjqzt} and ksgljgid IN
		<foreach collection="list" open="(" close=")" separator="," item="item">
		            #{item}
		</foreach> 
	</update>
	
	<update id="updateByBzid">
		update kdxc_wj_ck_wjwgmd1 set tjzt=#{tjzt} where tjzt=#{tjqzt} and bzid IN
		<foreach collection="list" open="(" close=")" separator="," item="item">
		            #{item}
		</foreach> 
	</update>
	
	<!-- 如果撤销处绝书 ，改变其状态 -->
	<update id="updateByEmmm">
		update kdxc_wj_ck_wjwgmd1 set sfdccjs=#{cjszt} where bzid=#{bzid}
	</update>
	
	<delete id="deleteBybzidList">
		<!-- delete from kdxc_wj_ck_wjwgmd1 where bzid IN-->
		update kdxc_wj_ck_wjwgmd1 set sczt='1' where bzid IN
		<foreach collection="list" open="(" close=")" separator="," item="item">
		            #{item}
		</foreach> 
	</delete>
</mapper>
