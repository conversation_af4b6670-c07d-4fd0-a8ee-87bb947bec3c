package com.xcwlkj.spxc.model.enums;


import com.xcwlkj.base.exception.BusinessException;

/**
 * 子系统
 * 
 * <AUTHOR>
 * @version $Id: SubSysEnum.java, v 0.1 Apr 10, 2020 12:37:16 PM White Wolf Exp $
 */
public enum SubSysEnum {
    
    KSYW("KSYW","考试业务子系统"),
    JYBDJ("JYBDJ","教育部对接子系统"),
    SBYW("SBYW","设备运维子系统"),
    ZBFK("ZBFK","作弊防控子系统"),
    CZSB("HS-GW-ACS","车载设备同步"),
    FACADE("FACADE","FACADE")
    
    ;

    private String code;
    private String desc;
    
    private SubSysEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return this.code;
    }
    
    public String getDesc() {
        return this.desc;
    }
    
    public static SubSysEnum get(String code) {
        for (SubSysEnum c : values()) {
            if (c.getCode().toUpperCase().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
    
}
