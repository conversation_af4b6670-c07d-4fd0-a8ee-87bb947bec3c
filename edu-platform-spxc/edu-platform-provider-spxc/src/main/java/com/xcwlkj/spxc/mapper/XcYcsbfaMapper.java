/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.spxc.model.domain.XcYcsbfa;



/**
 * 巡查-异常上报方案数据库操作
 * <AUTHOR>
 * @version $Id: InitXcYcsbfaMapper.java, v 0.1 2020年05月10日 14时22分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface XcYcsbfaMapper extends MyMapper<XcYcsbfa> {

    /**
	 * 分页查询巡查-异常上报方案
	 * 
	 * @param example
	 * @return
	 */
	List<XcYcsbfa> pageList(XcYcsbfa example);
}
