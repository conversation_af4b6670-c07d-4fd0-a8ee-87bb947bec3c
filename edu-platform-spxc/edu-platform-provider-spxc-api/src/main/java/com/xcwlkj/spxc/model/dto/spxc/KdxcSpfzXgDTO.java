/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.model.dto.spxc;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 03-修改 视频分组信息dto
 * <AUTHOR>
 * @version $Id: KdxcSpfzXgDTO.java, v 0.1 2020年05月16日 11时37分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdxcSpfzXgDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 组编号（不允许修改） */
    @NotBlank(message = "组编号（不允许修改）不能为空")
    private String zbh;
    /** 有效开始时间 */
    @NotBlank(message = "有效开始时间不能为空")
    private String yxkssj;
    /** 有效结束时间 */
    @NotBlank(message = "有效结束时间不能为空")
    private String yxjssj;
    /** 组生成类型（不允许修改） */
    @NotBlank(message = "组生成类型（不允许修改）不能为空")
    private String zlx;
    /** 组关联计划（不允许修改） */
    @NotBlank(message = "组关联计划（不允许修改）不能为空")
    private String zjh;
    /** 组计划名称（不允许修改） */
    @NotBlank(message = "组计划名称（不允许修改）不能为空")
    private String zjhmc;
    /** 优先级（不允许修改） */
    private String yxj;
    /** 抽取比例  0-100 */
    private String cqbl;
    /** 组名称 */
    @NotBlank(message = "组名称不能为空")
    private String zmc;

}
