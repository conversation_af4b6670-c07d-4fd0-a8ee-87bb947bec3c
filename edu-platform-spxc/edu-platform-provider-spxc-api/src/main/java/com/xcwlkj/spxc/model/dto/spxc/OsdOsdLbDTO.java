/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.model.dto.spxc;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 19-查询 考点的OSD结果列表（无用）（移至 运维）dto
 * <AUTHOR>
 * @version $Id: OsdOsdLbDTO.java, v 0.1 2020年05月14日 10时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class OsdOsdLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	

}
