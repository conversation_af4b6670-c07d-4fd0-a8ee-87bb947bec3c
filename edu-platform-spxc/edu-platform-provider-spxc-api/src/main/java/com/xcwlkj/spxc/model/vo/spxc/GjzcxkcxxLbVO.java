/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.model.vo.spxc;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 38-根据指定的关键字，查询考场信息列表vo
 * <AUTHOR>
 * @version $Id: GjzcxkcxxLbVO.java, v 0.1 2020年06月02日 14时15分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GjzcxkcxxLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 通过关键字查询到的考场列表 */
    private List<GjzcxkcLbItemVO> gjzcxkcLb;

}
