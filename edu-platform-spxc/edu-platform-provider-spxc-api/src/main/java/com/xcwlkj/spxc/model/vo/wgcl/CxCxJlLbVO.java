/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.model.vo.wgcl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 13、查询撤销记录vo
 * <AUTHOR>
 * @version $Id: CxCxJlLbVO.java, v 0.1 2020年08月14日 20时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CxCxJlLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 撤销记录集合 */
    private List<CxjlLbItemVO> cxjlLb;
    /** 总条数 */
    private Integer totalRows;

}
