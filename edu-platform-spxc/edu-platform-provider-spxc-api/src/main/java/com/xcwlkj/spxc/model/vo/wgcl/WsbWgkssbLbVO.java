/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.model.vo.wgcl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 19、未上报违规考生查询列表【对比4】vo
 * <AUTHOR>
 * @version $Id: WsbWgkssbLbVO.java, v 0.1 2020年10月26日 20时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class WsbWgkssbLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 未上报违规考生对象列表 */
    private List<WgwjksdxLbItemVO> wsbWgwjksdxLb;
    /** 总条数 */
    private Integer totalRows;

}
