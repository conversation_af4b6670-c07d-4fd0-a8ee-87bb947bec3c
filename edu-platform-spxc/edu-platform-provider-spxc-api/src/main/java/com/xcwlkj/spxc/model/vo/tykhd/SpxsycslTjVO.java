/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.spxc.model.vo.tykhd;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 16-统计 指定考院（考点）监考、考生、镜头异常上报数量vo
 * <AUTHOR>
 * @version $Id: SpxsycslTjVO.java, v 0.1 2020年05月16日 11时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SpxsycslTjVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考生作弊数量总数 */
    private Integer kszbslzs;
    /** 监考人员违纪数量总数 */
    private Integer jkrywjslzs;
    /** 巡视中摄像机异常数量 */
    private Integer xszsxjycslzs;
    /** 考生座次性别标记错误 */
    private Integer kszcbjcw;
    /** 应急事件上报 */
    private Integer yjsjsb;

}
