package com.xcwlkj.gxdj.model.domain.ksyw;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "ks_ksxx_import_exception")
public class KsKsxxImportException implements Serializable {
    /**
     *
     */
    @Id
    @Column(name = "ID")
    private String id;
    /**
     * 考点编号
     */
    @Column(name = "KDBH")
    private String kdbh;
    /**
     * 标准化考点编号
     */
    @Column(name = "BZHKDID")
    private String bzhkdid;
    /**
     * 考试计划编号
     */
    @Column(name = "KSJHBH")
    private String ksjhbh;
    /**
     * 异常描述
     */
    @Column(name = "T_DESC")
    private String tDesc;
    /**
     * 异常类型
     */
    @Column(name = "T_TYPE")
    private String tType;
    /**
     * 异常模式
     */
    @Column(name = "T_MODE")
    private String tMode;
    /**
     * 导出考场考点状态id
     */
    @Column(name = "IMPORT_STATUS_ID")
    private String importStatusId;
    /**
     *
     */
    @Column(name = "IMPORT_FILE_ID")
    private String importFileId;
    /**
     * 任务id
     */
    @Column(name = "IMPORT_TASK_ID")
    private String importTaskId;
    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;
    /**
     * 更新时间
     */
    @Column(name = "UPDATE_TIME")
    private Date updateTime;


}