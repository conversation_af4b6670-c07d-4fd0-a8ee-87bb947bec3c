package com.xcwlkj.gxdj.mapper.ksyw;

import com.xcwlkj.gxdj.mapper.common.CustomMapper;
import com.xcwlkj.gxdj.model.domain.ksyw.KsKsrcxx;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 考生入场数据库操作
 */
@Mapper
@Repository
public interface KsKsrcxxMapper extends CustomMapper<KsKsrcxx> {
    void batchUpdateSbzt(@Param("ids") List<String> ids, @Param("sbzt") String sbzt);

    void batchUpdateTbzt(@Param("ids") List<String> ids, @Param("tbzt") String tbzt);

    List<String> getBzhkdidsByksjh(@Param("ksjhbh") String ksjhbh);

    List<KsKsrcxx> getKsrcxx(@Param("ksjhbh") String ksjhbh, @Param("ccm") String ccm, @Param("kdbh") String kdbh, @Param("kszpscqy") Integer kszpscqy);

    void batchUpdateTbztGxzt(@Param("ids") List<String> primaryKeys, @Param("tbzt") String tbzt, @Param("gxzt") String gxzt);
}
