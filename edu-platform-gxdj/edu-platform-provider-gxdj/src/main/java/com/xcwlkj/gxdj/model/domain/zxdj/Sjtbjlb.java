package com.xcwlkj.gxdj.model.domain.zxdj;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * 数据同步记录表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "sjtbjlb")
public class Sjtbjlb implements Serializable {

    /**
     * 任务ID {表名}_{YYYYMMDD}_{3位顺序号001}
     */
    @Column(name = "ID")
    private String id;
    /**
     * 表名
     */
    @Column(name = "BM")
    private String bm;
    /**
     * 数据条数
     */
    @Column(name = "SJTS")
    private String sjts;
    /**
     * 任务状态  1：未执行  2：执行中 3：已执行
     */
    @Column(name = "RWZT")
    private int rwzt;
    /**
     * 创建时间
     */
    @Column(name = "CJSJ")
    private Date cjsj;
    /**
     * 更新时间
     */
    @Column(name = "GXSJ")
    private Date gxsj;
    /**
     * 批次号
     */
    @Column(name = "PCH")
    private String pch;
}
