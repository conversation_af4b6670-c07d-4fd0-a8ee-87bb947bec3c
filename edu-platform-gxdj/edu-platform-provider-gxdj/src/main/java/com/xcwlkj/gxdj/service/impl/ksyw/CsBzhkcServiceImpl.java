package com.xcwlkj.gxdj.service.impl.ksyw;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.gxdj.mapper.ksyw.CsBzhkcMapper;
import com.xcwlkj.gxdj.mapper.zxdj.ZxdjCsBzhkcMapper;
import com.xcwlkj.gxdj.model.domain.ksyw.CsBzhkc;
import com.xcwlkj.gxdj.model.domain.ksyw.SjtbSjtbjg;
import com.xcwlkj.gxdj.model.domain.ksyw.SjtbSjtbxq;
import com.xcwlkj.gxdj.model.domain.zxdj.ZxdjCsBzhkc;
import com.xcwlkj.gxdj.service.ksyw.CsBzhkcService;
import com.xcwlkj.gxdj.service.sbyw.SbCsgxService;
import com.xcwlkj.gxdj.util.SjtbUtils;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标准化考场服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service("csBzhkcService")
public class CsBzhkcServiceImpl implements CsBzhkcService {

    @Resource
    private CsBzhkcMapper csBzhkcMapper;
    @Resource
    private ZxdjCsBzhkcMapper zxdjCsBzhkcMapper;
    @Resource
    private SbCsgxService sbCsgxService;
    @Resource
    private SjtbUtils sjtbUtils;

    @Override
    public int deleteByExample(Example example) {
        return csBzhkcMapper.deleteByExample(example);
    }

    @Override
    public int insertList(List<CsBzhkc> csBzhkcList) {
        return csBzhkcMapper.insertList(csBzhkcList);
    }

    @Override
    public int insertListSelective(List<CsBzhkc> csBzhkcList) {
        return csBzhkcMapper.insertListSelective(csBzhkcList);
    }

    @Override
    public void bzhkcXz(SjtbSjtbjg sjtbjg, String sjtbxqId) {
        log.info("标准化考场下载");
        SjtbSjtbxq sjtbxq = new SjtbSjtbxq();

        Date start = DateUtil.addMinutes(new Date(), -15);
        try {
            int pageNum = 1, pageSize = 500;
            int total;
            long startTime = System.currentTimeMillis();
            Example example = new Example(ZxdjCsBzhkc.class);
            example.createCriteria().andNotEqualTo("sczt", ScztEnum.DEL.getCode());
            Page<ZxdjCsBzhkc> pageInfo = PageHelper.startPage(pageNum, pageSize);
            List<ZxdjCsBzhkc> list = zxdjCsBzhkcMapper.selectByExample(example);
            long endTime = System.currentTimeMillis();
            log.info("查询{}条标准化考场数据，用时{}毫秒", list.size(), endTime - startTime);
            if (CollectionUtils.isEmpty(list)) {
                log.info("未查到标准化考场数据");
                sjtbUtils.queryResultEmpty(sjtbjg, sjtbxq, "未查到标准化考场数据", null, "COMMON");
                sjtbUtils.updateSjtbxqById(sjtbxq, sjtbxqId);
                return;
            }
            int[] totalArr = {0, 0};
            insertBzhkcList(list, totalArr);
            total = (int) pageInfo.getTotal();
            int pages = pageInfo.getPages();
            while (pages > pageNum) {
                pageNum++;
                startTime = System.currentTimeMillis();
                pageInfo = PageHelper.startPage(pageNum, pageSize);
                list = zxdjCsBzhkcMapper.selectByExample(example);
                endTime = System.currentTimeMillis();
                log.info("查询{}条考试场所信息，用时{}毫秒", list.size(), endTime - startTime);
                insertBzhkcList(list, totalArr);
                pages = pageInfo.getPages();
            }
            int insertTotal = totalArr[0];
            int updateTotal = totalArr[1];
            int iuTotal = insertTotal + updateTotal;
            if (iuTotal < 1) {
                sjtbUtils.insertFailed(sjtbjg, sjtbxq, "标准化考场数据插入失败", null, "COMMON");
            } else {
                sjtbUtils.insertSuccess(sjtbjg, sjtbxq, iuTotal, null, "COMMON");
                try {
                    sjtbxq.setAffectRange(affectRange(iuTotal));
                } catch (Exception e) {
                    e.printStackTrace();
                }
                // 将同步数据以外且无设备的设为删除
                deleteByUpdateTime(start);
            }
            log.info("共{}条标准化考场数据，插入{}条，修改{}条", total, insertTotal, updateTotal);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("标准化考场下载错误：{}", e.toString());
            sjtbUtils.generateException(sjtbjg, sjtbxq, e, null, "COMMON");
        }
        sjtbUtils.updateSjtbxqById(sjtbxq, sjtbxqId);
    }

    private void insertBzhkcList(List<ZxdjCsBzhkc> list, int[] totalArr) {
        if (CollectionUtil.isNotEmpty(list)) {
            List<String> bzhkcidList = list.stream().map(ZxdjCsBzhkc::getBzhkcid).collect(Collectors.toList());
            Example example = new Example(CsBzhkc.class);
            example.selectProperties("bzhkcid");
            example.createCriteria().andIn("bzhkcid", bzhkcidList).andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
            List<CsBzhkc> bzhkcList = csBzhkcMapper.selectByExample(example);
            List<String> kdidList = bzhkcList.stream().map(CsBzhkc::getBzhkcid).collect(Collectors.toList());

            List<CsBzhkc> insertBzhkcList = new ArrayList<>();
            List<String> updateBzhkcList = new ArrayList<>();
            CsBzhkc csBzhkc;
            for (ZxdjCsBzhkc zxdjBzhkc : list) {
                if (kdidList.contains(zxdjBzhkc.getBzhkcid())) {
                    updateBzhkcList.add(zxdjBzhkc.getBzhkcid());
                } else {
                    csBzhkc = new CsBzhkc();
                    BeanUtils.copyProperties(zxdjBzhkc, csBzhkc);
                    csBzhkc.setBzhkcbh(IdGenerateUtil.generateId());
                    csBzhkc.setCreateTime(new Date());
                    csBzhkc.setUpdateTime(new Date());
                    csBzhkc.setShzt("0");
                    csBzhkc.setSczt(ScztEnum.NOTDEL.getCode());
                    sjtbUtils.trimObjPropertySpace(csBzhkc);
                    insertBzhkcList.add(csBzhkc);
                }
            }
            int insertRow = 0, updateRow = 0;
            long startTime = System.currentTimeMillis();
            if (CollectionUtils.isNotEmpty(insertBzhkcList)) {
                insertRow = csBzhkcMapper.insertListSelective(insertBzhkcList);
                totalArr[0] += insertRow;
            }
            if (CollectionUtils.isNotEmpty(updateBzhkcList)) {
                csBzhkc = new CsBzhkc();
                csBzhkc.setUpdateTime(new Date());
                example.clear();
                example.createCriteria().andIn("bzhkcid", updateBzhkcList).andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
                updateRow = csBzhkcMapper.updateByExampleSelective(csBzhkc, example);
                totalArr[1] += updateRow;
            }
            long endTime = System.currentTimeMillis();
            log.info("插入{}条标准化考场数据，修改{}条标准化考场数据，用时{}毫秒", insertRow, updateRow, endTime - startTime);
        }
    }

    private String affectRange(int insertTotal) {
        return "标准化考场(" + insertTotal + ")";
    }

    /**
     * 删除指定时间之前的标准化考场, 有设备信息的不删除
     */
    private void deleteByUpdateTime(Date time) {
        Example emBzhkc = new Example(CsBzhkc.class);
        emBzhkc.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andLessThan("updateTime", time);
        List<CsBzhkc> csBzhkcs = csBzhkcMapper.selectByExample(emBzhkc);
        if (CollectionUtils.isEmpty(csBzhkcs)) {
            log.info("无需删除标准化考场数据");
            return;
        }
        List<String> kcids = csBzhkcs.stream().map(CsBzhkc::getBzhkcid).collect(Collectors.toList());
        List<String> csbhs = sbCsgxService.registerCs(kcids);
        List<CsBzhkc> csBzhkcsDelete = csBzhkcs.stream().filter(csBzhkc -> !csbhs.contains(csBzhkc.getBzhkcid())).collect(Collectors.toList());

        int deleteCount = 0;
        if (csBzhkcsDelete.size() > 0) {
            List<String> bzhkcids = csBzhkcsDelete.stream().map(CsBzhkc::getBzhkcid).collect(Collectors.toList());
            List<List<String>> splitList = sjtbUtils.splitList(bzhkcids, 1000);
            Example emBzhkcDelete = new Example(CsBzhkc.class);
            CsBzhkc bzhkc;
            for (List<String> subList : splitList) {
                emBzhkcDelete.clear();
                emBzhkcDelete.createCriteria().andIn("bzhkcid", subList);
                bzhkc = new CsBzhkc();
                bzhkc.setSczt(ScztEnum.DEL.getCode());
                bzhkc.setUpdateTime(new Date());
                deleteCount += csBzhkcMapper.updateByExampleSelective(bzhkc, emBzhkcDelete);
            }
        }
        log.info("删除{}条标准化考场数据", deleteCount);

        // 由于设备存在不删除的考场
        List<CsBzhkc> bzhkcNotDelete = csBzhkcs.stream().filter(csBzhkc -> csbhs.contains(csBzhkc.getBzhkcid())).collect(Collectors.toList());
        int notDeleteKc = 0;
        if (bzhkcNotDelete.size() > 0) {
            List<String> bzhkcids = bzhkcNotDelete.stream().map(CsBzhkc::getBzhkcid).collect(Collectors.toList());
            List<List<String>> splitList = sjtbUtils.splitList(bzhkcids, 1000);
            Example emBzhkcDelete = new Example(CsBzhkc.class);
            CsBzhkc bzhkc;
            for (List<String> subList : splitList) {
                emBzhkcDelete.clear();
                emBzhkcDelete.createCriteria().andIn("bzhkcid", subList);
                bzhkc = new CsBzhkc();
                bzhkc.setCzkc("-1");  // 设置标记位, 表示需要先删除设备
                bzhkc.setUpdateTime(new Date());
                notDeleteKc += csBzhkcMapper.updateByExampleSelective(bzhkc, emBzhkcDelete);
            }
        }
        log.info("{}条标准化考场数据由于存在设备信息无法删除", notDeleteKc);

    }
}