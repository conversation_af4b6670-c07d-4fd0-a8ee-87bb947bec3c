package com.xcwlkj.gxdj.service.impl;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.gxdj.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

public class BaseServiceImpl< M extends MyMapper<T>, T> implements BaseService<T> {

	@Autowired
	protected M baseMapper;

	@Override
	public int insert(T entity) {
		return baseMapper.insert(entity);
	}

	@Override
	public int updateByPrimaryKey(T entity) {
		return baseMapper.updateByPrimaryKey(entity);
	}

	@Override
	public List<T> selectByExample(Example example) {
		return baseMapper.selectByExample(example);
	}

	@Override
	public T selectByPrimaryKey(String key) {
		return baseMapper.selectByPrimaryKey(key);
	}

	@Override
	public int updateByPrimaryKeySelective(T entity) {
		return baseMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public T selectOneByExample(Example example) {
		return baseMapper.selectOneByExample(example);
	}

	@Override
	public int deleteByExample(Example example) {
		
		return baseMapper.deleteByExample(example);
	}

	@Override
	public int insertList(List<T> list) {
		return baseMapper.insertList(list);
	}

	@Override
	public int updateByExampleSelective(T entity, Example example) {
		return baseMapper.updateByExampleSelective(entity, example);
	}

	@Override
	public int deleteByPrimaryKey(String key) {
		return baseMapper.deleteByPrimaryKey(key);
	}

	@Override
	public int insertSelective(T entity) {
		return baseMapper.insertSelective(entity);
	}
	
}
