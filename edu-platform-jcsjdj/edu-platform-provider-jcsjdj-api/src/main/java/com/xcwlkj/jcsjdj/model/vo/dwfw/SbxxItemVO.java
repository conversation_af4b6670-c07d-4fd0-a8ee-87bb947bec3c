/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.vo.dwfw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 考点设备资产统计vo
 * <AUTHOR>
 * @version $Id: SbxxItemVO.java, v 0.1 2020年09月21日 13时45分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbxxItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考点编号 */
    private String kdbh;
    /** 考点名称 */
    private String kdmc;
    /**  */
    private List<SblbItemVO> sblb;

}
