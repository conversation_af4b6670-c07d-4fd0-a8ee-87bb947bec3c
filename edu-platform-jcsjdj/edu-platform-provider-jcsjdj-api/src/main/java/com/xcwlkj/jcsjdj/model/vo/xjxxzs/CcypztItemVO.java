/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.vo.xjxxzs;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 详情-存储硬盘状态vo
 * <AUTHOR>
 * @version $Id: CcypztItemVO.java, v 0.1 2020年05月20日 15时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CcypztItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备类型 */
    private String sblx;
    /** 设备类型名称 */
    private String sblxmc;
    /** 异常设备数量 */
    private Integer ycsbsl;

}
