/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.osdsz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * osd-设置指令dto
 * <AUTHOR>
 * @version $Id: SbbhlbItemDTO.java, v 0.1 2020年05月22日 14时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbbhlbItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备编号 */
    private String sbbh;
    /** 设备名称 */
    private String sbmc;
    /** osd内容列表 */
    private List<OsdItemDTO> osdList;

}
