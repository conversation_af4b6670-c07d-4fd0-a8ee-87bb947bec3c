/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.gbid;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import java.util.List;
import javax.validation.constraints.NotNull;


/**
 * 【gbid】获取任务执行信息列表dto
 * <AUTHOR>
 * @version $Id: HqRwzxxxLbDTO.java, v 0.1 2021年10月14日 10时43分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqRwzxxxLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 任务编号 */
    @NotBlank(message = "任务编号不能为空")
    private String rwbh;
    /** 任务完成状态列表(可多选) 不传-全部 /0-未开始 / 1-已完成 / 9-完成异常 */
    private List<String> rwwcztParamList;
    /** 第几页 */
    @NotNull(message = "第几页不能为空")
    private Integer pageNum;
    /** 每页显示数 */
    @NotNull(message = "每页显示数不能为空")
    private Integer pageSize;

}
