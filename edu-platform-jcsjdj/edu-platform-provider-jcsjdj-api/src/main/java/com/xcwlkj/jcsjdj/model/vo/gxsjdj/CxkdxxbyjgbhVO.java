/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.vo.gxsjdj;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


/**
 * 根据机构编号查询考点信息vo
 * <AUTHOR>
 * @version $Id: CxkdxxbyjgbhVO.java, v 0.1 2022年03月08日 13时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CxkdxxbyjgbhVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 机构树信息列表 */
    private List<JgsxxLbItemVO> jgsxxLb;

}
