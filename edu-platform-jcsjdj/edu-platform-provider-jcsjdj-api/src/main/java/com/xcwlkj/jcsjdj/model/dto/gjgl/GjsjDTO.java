/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.gjgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;


/**
 * 固件升级dto
 * <AUTHOR>
 * @version $Id: GjsjDTO.java, v 0.1 2020年06月03日 14时09分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GjsjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 固件包编号 */
    @NotBlank(message = "固件包编号不能为空")
    private String gjbbh;
    /** 升级设备列表 */
    @NotNull(message = "升级设备列表不能为空")
    @Valid
    @Size(min = 1)
    private List<SjsblbItemDTO> sjsblb;

}
