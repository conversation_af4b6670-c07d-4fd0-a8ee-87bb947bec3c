/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.gjgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 固件升级结果明细dto
 * <AUTHOR>
 * @version $Id: GjsjjgmxDTO.java, v 0.1 2020年06月06日 12时24分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GjsjjgmxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 升级状态(默认全部) */
    private String sjzt;
    /** 设备sip地址 */
    private String sipdz;
    /** 升级批次号 */
    @NotBlank(message = "升级批次号不能为空")
    private String pch;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 当前页数 */
    @NotNull(message = "当前页数不能为空")
    private Integer pageSize;

}
