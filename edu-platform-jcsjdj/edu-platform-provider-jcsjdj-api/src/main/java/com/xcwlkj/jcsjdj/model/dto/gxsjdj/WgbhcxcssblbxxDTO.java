/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.gxsjdj;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 网关编号查询机构、场所信息及对应的设备信息dto
 * <AUTHOR>
 * @version $Id: WgbhcxcssblbxxDTO.java, v 0.1 2022年03月08日 11时09分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class WgbhcxcssblbxxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 网关编号 */
    @NotBlank(message = "网关编号不能为空")
    private String wgbh;

}
