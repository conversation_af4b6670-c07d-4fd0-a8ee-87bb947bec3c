/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.dwfw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 41 数据同步 - 查询 设备同步机构信息 列表dto
 * <AUTHOR>
 * @version $Id: HqSbtbJgxxLbDTO.java, v 0.1 2020年12月12日 18时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqSbtbJgxxLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 同步机构数据 的 id 列表 */
    @NotNull(message = "同步机构数据 的 id 列表不能为空")
    private List<String> bzidLb;
    /** 机构类别 */
    private String jglb;

}
