/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.service.hystrix;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi;

import com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglkddmExcelDrDTO;
import org.springframework.stereotype.Component;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglksmcCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.SqglksmcCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglkqCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.SqglkqCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglkddmCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.SqglkddmCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.RskdkCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.KssqglCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.KssqglCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.XfmdjdCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.XfmdjdCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkXzDTO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkXgDTO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkScDTO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.GjsqsjdsxfmdtzDTO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.MdxflsjlCxDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.MdxflsjlCxVO;
import com.xcwlkj.jcsjdj.model.dto.sbzcgk.MdxflsjlExcelDcDTO;
import com.xcwlkj.jcsjdj.model.vo.sbzcgk.MdxflsjlExcelDcVO;
import org.springframework.web.multipart.MultipartFile;

/**
 * 
 * <AUTHOR>
 * @version $Id: SbzcgkXcmcFeignHystrix.java, v 0.1 2021年10月20日 10时19分 xcwlkj.com Exp $
 */
@Component
public class SbzcgkXcmcFeignHystrix implements SbzcgkXcmcFeignApi{


    @Override
    public Wrapper<Void> sqglkddmExcelDr(MultipartFile file) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#sqglksmcCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglksmcCxDTO)
     */
    @Override
    public Wrapper<SqglksmcCxVO> sqglksmcCx(SqglksmcCxDTO sqglksmcCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#sqglkqCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglkqCxDTO)
     */
    @Override
    public Wrapper<SqglkqCxVO> sqglkqCx(SqglkqCxDTO sqglkqCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#sqglkddmCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.SqglkddmCxDTO)
     */
    @Override
    public Wrapper<SqglkddmCxVO> sqglkddmCx(SqglkddmCxDTO sqglkddmCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#rskdkCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkCxDTO)
     */
    @Override
    public Wrapper<RskdkCxVO> rskdkCx(RskdkCxDTO rskdkCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#kssqglCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.KssqglCxDTO)
     */
    @Override
    public Wrapper<KssqglCxVO> kssqglCx(KssqglCxDTO kssqglCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#xfmdjdCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.XfmdjdCxDTO)
     */
    @Override
    public Wrapper<XfmdjdCxVO> xfmdjdCx(XfmdjdCxDTO xfmdjdCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#rskdkXz(com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkXzDTO)
     */
    @Override
    public Wrapper<Void> rskdkXz(RskdkXzDTO rskdkXzDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#rskdkXg(com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkXgDTO)
     */
    @Override
    public Wrapper<Void> rskdkXg(RskdkXgDTO rskdkXgDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#rskdkSc(com.xcwlkj.jcsjdj.model.dto.sbzcgk.RskdkScDTO)
     */
    @Override
    public Wrapper<Void> rskdkSc(RskdkScDTO rskdkScDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#gjsqsjdsxfmdtz(com.xcwlkj.jcsjdj.model.dto.sbzcgk.GjsqsjdsxfmdtzDTO)
     */
    @Override
    public Wrapper<Void> gjsqsjdsxfmdtz(GjsqsjdsxfmdtzDTO gjsqsjdsxfmdtzDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#mdxflsjlCx(com.xcwlkj.jcsjdj.model.dto.sbzcgk.MdxflsjlCxDTO)
     */
    @Override
    public Wrapper<MdxflsjlCxVO> mdxflsjlCx(MdxflsjlCxDTO mdxflsjlCxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.jcsjdj.service.SbzcgkXcmcFeignApi#mdxflsjlExcelDc(com.xcwlkj.jcsjdj.model.dto.sbzcgk.MdxflsjlExcelDcDTO)
     */
    @Override
    public Wrapper<MdxflsjlExcelDcVO> mdxflsjlExcelDc(MdxflsjlExcelDcDTO mdxflsjlExcelDcDto) {
        return WrapMapper.error();
    }
}
