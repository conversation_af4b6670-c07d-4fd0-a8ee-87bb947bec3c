/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.sbtb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 08 获取同步的 设备 信息列表dto
 * <AUTHOR>
 * @version $Id: HqtbDevListLbDTO.java, v 0.1 2020年12月03日 14时57分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqtbDevListLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 目标sip */
    @NotBlank(message = "目标sip不能为空")
    private String toSip;
    /** 节点目录 */
    @NotBlank(message = "节点目录不能为空")
    private String requestUri;
    /** 接口版本号，为空时，默认为1.0 */
    private String v;
    /** 目标场所ID(必选),	取值说明	1 当CSID为空值时，查询该机构下所有的设备目录。	2 当CSID不为空时:	2.1 与URI相同时,查询该机构下所有无归属场所的设备目录。	2.2 与URI不相同时,查询该CSID对应的场所下内设备目录。 */
//    @NotBlank(message = "目标场所ID(必选),	取值说明	1 当CSID为空值时，查询该机构下所有的设备目录。	2 当CSID不为空时:	2.1 与URI相同时,查询该机构下所有无归属场所的设备目录。	2.2 与URI不相同时,查询该CSID对应的场所下内设备目录。不能为空")
    private String csid;
    /** 兼容 GB 35114 */
    private String gbid;

}
