/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.dwfw;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 45 - OSD根据考试计划切换dto
 * <AUTHOR>
 * @version $Id: OsdPlanDTO.java, v 0.1 2021年01月03日 20时18分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class OsdPlanDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 默认OSD内容 */
    private String defaultOSD;
    /** 添加计划模式 */
    private String osdmode = "0";
    /**  */
    @NotNull(message = "不能为空")
    private List<OsdplansDTO> osdplans;

}
