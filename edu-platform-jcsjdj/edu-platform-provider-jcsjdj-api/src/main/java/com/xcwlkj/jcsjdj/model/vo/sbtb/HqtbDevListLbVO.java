/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.vo.sbtb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 08 获取同步的 设备 信息列表vo
 * <AUTHOR>
 * @version $Id: HqtbDevListLbVO.java, v 0.1 2020年12月03日 13时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqtbDevListLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 同步的设备列表 */
    private List<TbDevlbItemVO> tbDevListlb;

}
