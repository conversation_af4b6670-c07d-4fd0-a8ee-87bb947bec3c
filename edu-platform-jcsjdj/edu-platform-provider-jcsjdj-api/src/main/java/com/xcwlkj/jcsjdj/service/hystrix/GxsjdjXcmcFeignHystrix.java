/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.service.hystrix;

import com.xcwlkj.jcsjdj.model.dto.gxsjdj.*;
import com.xcwlkj.jcsjdj.model.vo.gxsjdj.CxcssblbxxVO;
import com.xcwlkj.jcsjdj.model.vo.gxsjdj.CxkdxxbyjgbhVO;
import com.xcwlkj.jcsjdj.model.vo.gxsjdj.WgbhcxcssblbxxVO;
import com.xcwlkj.jcsjdj.model.vo.gxsjdj.XlhcxsbxxVO;
import com.xcwlkj.jcsjdj.service.GxsjdjXcmcFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $Id: GxsjdjXcmcFeignHystrix.java, v 0.1 2022年02月21日 15时19分 xcwlkj.com Exp $
 */
@Component
public class GxsjdjXcmcFeignHystrix implements GxsjdjXcmcFeignApi {
    
    /**
     * @see com.xcwlkj.jcsjdj.service.GxsjdjXcmcFeignApi#sbxxsb(com.xcwlkj.jcsjdj.model.dto.gxsjdj.SbxxsbDTO)
     */
    @Override
    public Wrapper<Void> sbxxsb(SbxxsbDTO sbxxsbDto) {
        return WrapMapper.error();
    }
    /**
     * @see com.xcwlkj.jcsjdj.service.GxsjdjXcmcFeignApi#cxcssblbxx(com.xcwlkj.jcsjdj.model.dto.gxsjdj.CxcssblbxxDTO)
     */
    @Override
    public Wrapper<CxcssblbxxVO> cxcssblbxx(CxcssblbxxDTO cxcssblbxxDto) {
        return WrapMapper.error();
    }
    /**
     * @see com.xcwlkj.jcsjdj.service.GxsjdjXcmcFeignApi#wgbhcxcssblbxx(com.xcwlkj.jcsjdj.model.dto.gxsjdj.WgbhcxcssblbxxDTO)
     */
    @Override
    public Wrapper<WgbhcxcssblbxxVO> wgbhcxcssblbxx(WgbhcxcssblbxxDTO wgbhcxcssblbxxDto) {
        return WrapMapper.error();
    }
    /**
     * @see com.xcwlkj.jcsjdj.service.GxsjdjXcmcFeignApi#xlhcxsbxx(com.xcwlkj.jcsjdj.model.dto.gxsjdj.XlhcxsbxxDTO)
     */
    @Override
    public Wrapper<XlhcxsbxxVO> xlhcxsbxx(XlhcxsbxxDTO xlhcxsbxxDto) {
        return WrapMapper.error();
    }
    /**
     * @see com.xcwlkj.jcsjdj.service.GxsjdjXcmcFeignApi#cxkdxxbyjgbh(com.xcwlkj.jcsjdj.model.dto.gxsjdj.CxkdxxbyjgbhDTO)
     */
    @Override
    public Wrapper<CxkdxxbyjgbhVO> cxkdxxbyjgbh(CxkdxxbyjgbhDTO cxkdxxbyjgbhDto) {
        return WrapMapper.error();
    }
}
