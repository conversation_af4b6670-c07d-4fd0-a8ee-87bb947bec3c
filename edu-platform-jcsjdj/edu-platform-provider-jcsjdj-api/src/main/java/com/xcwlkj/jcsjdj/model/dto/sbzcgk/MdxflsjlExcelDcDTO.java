/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.dto.sbzcgk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 7 名单下发历史记录Excel导出dto
 * <AUTHOR>
 * @version $Id: MdxflsjlExcelDcDTO.java, v 0.1 2021年11月01日 15时50分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MdxflsjlExcelDcDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试名称 */
    private String ksmc;
    /** 考区 */
    private String kqmc;
    /** 标准化考点编号 */
    private String bzhkdid;
    /** 考点状态 0 禁止 1允许 */
    private String kdzt;

}
