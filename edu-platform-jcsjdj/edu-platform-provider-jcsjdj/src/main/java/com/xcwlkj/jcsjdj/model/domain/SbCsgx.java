/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * sb_csgx
 * 
 * <AUTHOR>
 * @version $Id: SbCsgx.java, v 0.1 2020年12月16日 17时27分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "sb_csgx")
public class SbCsgx implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 场所编号 */
    @Id
    @Column(name = "csbh")
    private String            csbh;
    /** 设备编号 */
    @Id
    @Column(name = "sbbh")
    private String            sbbh;
    /** 场所名称 */
    @Column(name = "csmc")
    private String            csmc;
    /** 场所类型 */
    @Column(name = "cslx")
    private String            cslx;
    /** 设备名称 */
    @Column(name = "sbmc")
    private String            sbmc;
    /** 设备产品类型 */
    @Column(name = "sblx")
    private String            sblx;
    /** 节点祖先 */
    @Column(name = "jdzx")
    private String            jdzx;
    /** 考试管理机构标识码 */
    @Column(name = "ksgljgid")
    private String            ksgljgid;
    /** 场所所属的机构编号 */
    @Column(name = "ssjgbh")
    private String            ssjgbh;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


