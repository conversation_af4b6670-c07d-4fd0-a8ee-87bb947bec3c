package com.xcwlkj.jcsjdj.model.enums;

/**
 * 固件升级结果枚举
 * <AUTHOR>
 * @date 2020/5/18 20:30
 */
public enum GjsjjgEnum {

    DYSJJKCG("0","调用升级接口成功"),
    DYSJJKSB("1","调用升级接口失败"),
    GJSJSB("2","固件升级失败"),
    GJSJCG("3","固件升级成功");

    private String code;

    private String msg;


    /**
     * @param code 结果码
     * @param msg 描述信息
     */
    GjsjjgEnum(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public static String  getMsgByCode(String code){
        GjsjjgEnum[] gjsjjgEnums = GjsjjgEnum.values();
        for (GjsjjgEnum gjsjjgEnum: gjsjjgEnums) {
            if (gjsjjgEnum.getCode().equals(code)){
                return gjsjjgEnum.getMsg();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
