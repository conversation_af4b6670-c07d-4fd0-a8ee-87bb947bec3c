package com.xcwlkj.jcsjdj.share.planinspection.service;

import com.xcwlkj.jcsjdj.share.planinspection.model.dto.XjDeviceDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/8 17:22
 */
public interface InspectionCenterUnitService {

    /**
     * 巡检处理
     *
     * @param csbhList   场所编号列表
     * @param xjjhbh     巡检计划编号
     * @param zxjlbh     执行记录编号
     * @return
     */
    <T>  String executeTrans(List<String> csbhList, String xjjhbh,String zxjlbh,String jcxbm);
}
