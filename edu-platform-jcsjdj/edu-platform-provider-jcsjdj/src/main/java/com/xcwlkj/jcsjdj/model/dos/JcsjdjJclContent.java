package com.xcwlkj.jcsjdj.model.dos;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;


/**
 * 4.2.3.3  根据任务获取OSD列表 请求实体类
 */
@Data
public class JcsjdjJclContent {

    //机构代码
    @JSONField(name="orgcode")
    private String orgcode;

    //机构识别码
    @JSONField(name="orgidencode")
    private String orgidencode;

    //考试计划编号
    @JSONField(name="examcode")
    private String examcode;

    //考场类别码
    @JSONField(name="crcategorycode")
    private String crcategorycode;

}
