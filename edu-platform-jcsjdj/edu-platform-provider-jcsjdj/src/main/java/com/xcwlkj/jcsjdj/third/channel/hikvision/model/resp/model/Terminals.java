package com.xcwlkj.jcsjdj.third.channel.hikvision.model.resp.model;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;

@Data
public class Terminals {
	/**会场名称*/
    @JSONField(name="name")
    private String name;
    /**会场在线状态，0-未连接，1-已连接*/
    @JSONField(name="connectionState")
    private int connectionState;
    /**会场code*/
    @JSONField(name="indexCode")
    private String indexCode;
}
