/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.service;

import com.xcwlkj.jcsjdj.model.domain.SbJcNvr;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;


/**
 * nvr版本检测服务
 * <AUTHOR>
 * @version $Id: SbJcNvrService.java, v 0.1 2021年10月12日 11时27分 xcwlkj.com Exp $
 */
@Service
public interface SbJcNvrService  {

    List<SbJcNvr> selectByExample(Example example);

    void deleteAllSbJcNvr();
}