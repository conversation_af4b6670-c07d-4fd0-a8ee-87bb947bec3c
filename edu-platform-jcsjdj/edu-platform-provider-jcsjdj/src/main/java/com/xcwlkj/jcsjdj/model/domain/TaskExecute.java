/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jcsjdj.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 任务执行明细
 * 
 * <AUTHOR>
 * @version $Id: TaskExecute.java, v 0.1 2020年05月29日 18时20分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "task_execute")
public class TaskExecute implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 编号 */
    @Id
    @Column(name = "nbr")
    private String            nbr;
    /** 执行任务定义编号 */
    @Column(name = "task_define_nbr")
    private String            taskDefineNbr;
    /** 注册日期 */
    @Column(name = "reg_date")
    private Date            regDate;
    /** 注册时间 */
    @Column(name = "reg_time")
    private Date            regTime;
    /** 任务名 */
    @Column(name = "task_name")
    private String            taskName;
    /** 任务类型 */
    @Column(name = "task_type")
    private String            taskType;
    /** 执行服务名 */
    @Column(name = "service_name")
    private String            serviceName;
    /** 失败重试次数 */
    @Column(name = "fail_repeat_num")
    private Integer            failRepeatNum;
    /** 是否重复任务 */
    @Column(name = "is_repeat_task")
    private String            isRepeatTask;
    /** 重复间隔（秒） */
    @Column(name = "repeat_interval")
    private Integer            repeatInterval;
    /** 持续时长（秒） */
    @Column(name = "continue_time")
    private Integer            continueTime;
    /** 任务参数 */
    @Column(name = "task_param")
    private String            taskParam;
    /** 任务参数扩展 */
    @Column(name = "task_param_ext")
    private String            taskParamExt;
    /** 同任务优先级 ,999最高   100最低，默认600 */
    @Column(name = "priority_same_task")
    private Integer            prioritySameTask;
    /** 下一次执行时间 */
    @Column(name = "next_exec_time")
    private Date            nextExecTime;
    /** 开始执行时间 */
    @Column(name = "start_time")
    private Date            startTime;
    /** 结束执行时间 */
    @Column(name = "end_time")
    private Date            endTime;
    /** 当前执行时间 */
    @Column(name = "exec_time")
    private Date            execTime;
    /** 执行结果码 */
    @Column(name = "exec_result_code")
    private String            execResultCode;
    /** 执行结果摘要 */
    @Column(name = "exec_result_desc")
    private String            execResultDesc;
    /** 状态 */
    @Column(name = "status")
    private String            status;
    /** 备注1 */
    @Column(name = "bak1")
    private String            bak1;
    /** 备注2 */
    @Column(name = "bak2")
    private String            bak2;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 修改时间 */
    @Column(name = "update_time")
    private Date            updateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


