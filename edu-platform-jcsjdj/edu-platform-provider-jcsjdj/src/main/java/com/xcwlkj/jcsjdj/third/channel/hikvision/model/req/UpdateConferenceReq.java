package com.xcwlkj.jcsjdj.third.channel.hikvision.model.req;

import java.util.List;

import com.alibaba.fastjson.annotation.JSONField;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 新增会议
 * 
 * <AUTHOR>
 * @version $Id: AddConferenceReq.java, v 0.1 May 22, 2020 5:55:53 PM White Wolf Exp $
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class UpdateConferenceReq  extends BaseHikvisionServiceReq{
	
	/** 会议标题，长度 1~30 */
	@JSONField(name="title")
    private String title;
    /** 会议类型，枚举：byNow 即时会议，byTime 预约会议 */
	@JSONField(name="type")
    private String type;
    /** 会议开始时间，格式：yyyy-MM-dd HH:mm 注：type=byTime 或 regular 时必填 */
	@JSONField(name="startTime")
    private String startTime;
    /** 会议时长(分钟)，范围 30~1440 */
	@JSONField(name="durationTime")
    private long durationTime;
    /** 画面模式 */
	@JSONField(name="mode")
    private String mode;
    /** 会议方数，默认 8 */
	@JSONField(name="memberNum")
    private int memberNum;
    /** 会议使用 MCU 的 indexCode */
	@JSONField(name="mcuIndexCode")
    private String mcuIndexCode;
    /** 会议内容，长度 0~250 */
	@JSONField(name="content")
    private String content;
    /** 会场成员 indexCode 集合 */
	@JSONField(name="terminalIndexCodes")
    private List<String> terminalIndexCodes;
}
