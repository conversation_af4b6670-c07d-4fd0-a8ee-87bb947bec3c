package com.xcwlkj.jcsjdj.third.channel.matrix.sckmodel;

import com.xcwlkj.model.enums.BoolEnum;

/**
 * 矩阵服务请求枚举
 * 
 * <AUTHOR> Wolf
 * @version $Id: MediaServiceReqInfoEnum.java, v 0.1 Apr 21, 2020 3:51:33 PM White Wolf Exp $
 */
public enum MatrixServiceReqInfoEnum {

	GetLogin("LoginCatalogDeviceReq","/HISOMEAPI/userMgnt/loginMgnt",false,"登录"),
	GetXsqHm("XsqHmxxCatalogDeviceReq","/HISOMEAPI/matrixVideoWall/disMonitorInfo",true,"显示器画面"),
    GetSslXl("SslXlCatalogDeviceReq","/HISOMEAPI/matrixVideoWall/disMonitorInfo/setup",true,"实时流序列"),
    ;
	/** 请求类名 */
	private String reqClassName;
	/** url */
	private String url;
	/** 是否需要登录*/
	private boolean isLogin;
	/** description */
	private String description;

	/** code */

	/**
	 * 私有构造方法
	 * 
	 * @param code
	 * @param description
	 */
    private MatrixServiceReqInfoEnum(String reqClassName, String url,boolean isLogin, String description) {
		this.reqClassName = reqClassName;
		this.url = url;
		this.isLogin = isLogin;
		this.description = description;
	}

	/**
	 * 
	 * @param code
	 * @return {@link BoolEnum} 实例
	 */
	public static MatrixServiceReqInfoEnum find(String reqClassName) {
		for (MatrixServiceReqInfoEnum frs : MatrixServiceReqInfoEnum.values()) {
			if (frs.getReqClassName().toUpperCase().equals(reqClassName.toUpperCase())) {
				return frs;
			}
		}
        return null;
	}

	public String getUrl() {
		return url;
	}


	public String getReqClassName() {
		return reqClassName;
	}

	/**
	 * Getter method for property <tt>description</tt>.
	 * 
	 * @return property value of description
	 */
	public String getDescription() {
		return description;
	}

    public boolean isLogin() {
        return isLogin;
    }
	
	


}
