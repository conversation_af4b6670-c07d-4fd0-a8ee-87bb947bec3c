package com.xcwlkj.jcsjdj.third.channel.sip.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.xcwlkj.jcsjdj.third.channel.sip.model.req.model.TbQueryOrgCatalog;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *   机构信息查询
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class TbOrgCatalogReq extends BaseSipServiceReq{
    //目标节点的SIP地址
    @JSONField(name = "Query")
    private TbQueryOrgCatalog Query;
}