<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.jcsjdj.mapper.SbFtpsjzlMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.jcsjdj.model.domain.SbFtpsjzl">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="qymc" jdbcType="VARCHAR" property="qymc" />
        <result column="qybh" jdbcType="VARCHAR" property="qybh" />
        <result column="jgmc" jdbcType="VARCHAR" property="jgmc" />
        <result column="jgbh" jdbcType="VARCHAR" property="jgbh" />
        <result column="ip" jdbcType="VARCHAR" property="ip" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="dqzt" jdbcType="VARCHAR" property="dqzt" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="version" jdbcType="VARCHAR" property="version" />
        <result column="sjqbbh" jdbcType="VARCHAR" property="sjqbbh" />
        <result column="sjzt" jdbcType="VARCHAR" property="sjzt" />
        <result column="uploaded" jdbcType="BIGINT" property="uploaded" />
        <result column="totalsize" jdbcType="BIGINT" property="totalsize" />
        <result column="sjrwbh" jdbcType="VARCHAR" property="sjrwbh" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        id,
        qymc,
        qybh,
        jgmc,
        jgbh,
        ip,
        create_time,
        update_time,
        dqzt,
        sczt,
        version,
        sjqbbh,
        sjzt,
        uploaded,
        totalsize,
        sjrwbh

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="id != null and id != ''">
            AND id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="qymc != null and qymc != ''">
            AND qymc = #{qymc,jdbcType=VARCHAR}
        </if>
        <if test="qybh != null and qybh != ''">
            AND qybh = #{qybh,jdbcType=VARCHAR}
        </if>
        <if test="jgmc != null and jgmc != ''">
            AND jgmc = #{jgmc,jdbcType=VARCHAR}
        </if>
        <if test="jgbh != null and jgbh != ''">
            AND jgbh = #{jgbh,jdbcType=VARCHAR}
        </if>
        <if test="ip != null and ip != ''">
            AND ip = #{ip,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="dqzt != null and dqzt != ''">
            AND dqzt = #{dqzt,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="version != null and version != ''">
            AND version = #{version,jdbcType=VARCHAR}
        </if>
        <if test="sjqbbh != null and sjqbbh != ''">
            AND sjqbbh = #{sjqbbh,jdbcType=VARCHAR}
        </if>
        <if test="sjzt != null and sjzt != ''">
            AND sjzt = #{sjzt,jdbcType=VARCHAR}
        </if>
        <if test="uploaded != null and uploaded != ''">
            AND uploaded = #{uploaded,jdbcType=VARCHAR}
        </if>
        <if test="totalsize != null and totalsize != ''">
            AND totalsize = #{totalsize,jdbcType=VARCHAR}
        </if>
        <if test="sjrwbh != null and sjrwbh != ''">
            AND sjrwbh = #{sjrwbh,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="id != null ">
            id = #{id,jdbcType=VARCHAR},
        </if>
        <if test="qymc != null ">
            qymc = #{qymc,jdbcType=VARCHAR},
        </if>
        <if test="qybh != null ">
            qybh = #{qybh,jdbcType=VARCHAR},
        </if>
        <if test="jgmc != null ">
            jgmc = #{jgmc,jdbcType=VARCHAR},
        </if>
        <if test="jgbh != null ">
            jgbh = #{jgbh,jdbcType=VARCHAR},
        </if>
        <if test="ip != null ">
            ip = #{ip,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="dqzt != null ">
            dqzt = #{dqzt,jdbcType=VARCHAR},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="version != null ">
            version = #{version,jdbcType=VARCHAR},
        </if>
        <if test="sjqbbh != null ">
            sjqbbh = #{sjqbbh,jdbcType=VARCHAR}
        </if>
        <if test="sjzt != null ">
            sjzt = #{sjzt,jdbcType=VARCHAR}
        </if>
        <if test="uploaded != null ">
            uploaded = #{uploaded,jdbcType=VARCHAR}
        </if>
        <if test="totalsize != null ">
            totalsize = #{totalsize,jdbcType=VARCHAR}
        </if>
        <if test="sjrwbh != null ">
            sjrwbh = #{sjrwbh,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.jcsjdj.model.domain.SbFtpsjzl"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from sb_ftpsjzl
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>

    <update id="deleteAllSbFtpsjzl">
        update sb_ftpsjzl set sczt = '1' where sczt = '0'
    </update>

    <!-- 批量插入   -->
    <insert id="insertSjzlList" parameterType="java.util.List">
        INSERT INTO sb_ftpsjzl
            (
                id,
                qymc,
                qybh,
                jgmc,
                jgbh,
                ip,
                create_time,
                update_time,
                dqzt,
                sczt,
                version,
                sjqbbh,
                sjzt,
                uploaded,
                totalsize,
                sjrwbh
            )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.qymc},
            #{item.qybh},
            #{item.jgmc},
            #{item.jgbh},
            #{item.ip},
            #{item.createTime},
            #{item.updateTime},
            #{item.dqzt},
            #{item.sczt},
            #{item.version},
            #{item.sjqbbh},
            #{item.sjzt},
            #{item.uploaded},
            #{item.totalsize},
            #{item.sjrwbh}
            )
        </foreach>

    </insert>

    <select id="selectOneByIP" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sb_ftpsjzl
        where ip = #{ip} and sczt = '0'
    </select>

    <update id="updateSbFtpsjzl" parameterType="com.xcwlkj.jcsjdj.model.domain.SbFtpsjzl">
        update sb_ftpsjzl set
        uploaded = #{uploaded}
        where ip =#{ip} and sczt = '0'
    </update>

</mapper>
