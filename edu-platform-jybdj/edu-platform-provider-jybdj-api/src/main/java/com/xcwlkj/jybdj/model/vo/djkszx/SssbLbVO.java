/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.model.vo.djkszx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 17 设施设备-获取列表【待定】vo
 * <AUTHOR>
 * @version $Id: SssbLbVO.java, v 0.1 2020年06月15日 20时28分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SssbLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备信息同步列表 */
    private List<SbxxItemVO> sbxxList;
    /** 总条数 */
    private Integer totalRows;

}
