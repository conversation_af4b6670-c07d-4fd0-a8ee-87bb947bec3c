/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.model.dto.djkszx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 36 考点库-考点信息-设定上报或不报dto
 * <AUTHOR>
 * @version $Id: KdkdxxScDTO.java, v 0.1 2020年06月15日 20时30分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdkdxxScDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考点标志码 */
    @NotBlank(message = "考点标志码不能为空")
    private String kdbzid;
    /** 是否要上报当前的数据   0-不上报    1-要上报 */
    @NotBlank(message = "是否要上报当前的数据   0-不上报    1-要上报不能为空")
    private String sbzt;

}
