/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 工作人员
 * 
 * <AUTHOR>
 * @version $Id: CsGzry.java, v 0.1 2020年08月19日 10时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "cs_gzry")
public class CsGzry implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 场所人员编号 */
    @Id
    @Column(name = "csrybh")
    private String            csrybh;
    /** 姓名 */
    @Column(name = "csryxm")
    private String            csryxm;
    /** 手机号 */
    @Column(name = "csrysjh")
    private String            csrysjh;
    /**  */
    @Column(name = "csbh")
    private String            csbh;
    /** 关联场所名称 */
    @Column(name = "csmc")
    private String            csmc;
    /** 关联场所类型 */
    @Column(name = "clcslx")
    private String            clcslx;
    /** 岗位名 */
    @Column(name = "gwm")
    private String            gwm;
    /** 岗位编码 */
    @Column(name = "gwbm")
    private String            gwbm;
    /** 性别 */
    @Column(name = "csryxb")
    private String            csryxb;
    /** 学历 */
    @Column(name = "csryxl")
    private String            csryxl;
    /** 职称 */
    @Column(name = "csryzc")
    private String            csryzc;
    /** 照片 */
    @Column(name = "csryzp")
    private String            csryzp;
    /** 出生日期 */
    @Column(name = "csrycsrq")
    private String            csrycsrq;
    /** 身份证号 */
    @Column(name = "csrysfzh")
    private String            csrysfzh;
    /** 开始工作时间 */
    @Column(name = "ksgzsj")
    private String            ksgzsj;
    /** 考试项目名称 */
    @Column(name = "ksxmmc")
    private String            ksxmmc;
    /** 考试项目编码 */
    @Column(name = "ksxmbm")
    private String            ksxmbm;
    /** 编制类型 */
    @Column(name = "bzlx")
    private String            bzlx;
    /** 删除状态 */
    @Column(name = "sczt")
    private String            sczt;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;
    /** 上报状态 */
    @Column(name = "sbzt")
    private String            sbzt;
    /** 上报时间 */
    @Column(name = "sbsj")
    private Date            sbsj;
    /** 同步状态 */
    @Column(name = "tbzt")
    private String            tbzt;
    /** 更新状态 */
    @Column(name = "gxzt")
    private String            gxzt;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


