/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.mapper.jybdj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.jybdj.model.domain.CsBzhkc;



/**
 * 标准化考场数据库操作
 * <AUTHOR>
 * @version $Id: InitCsBzhkcMapper.java, v 0.1 2020年04月19日 12时58分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface CsBzhkcMapper extends MyMapper<CsBzhkc> {

    /**
	 * 分页查询标准化考场
	 * 
	 * @param example
	 * @return
	 */
	List<CsBzhkc> pageList(CsBzhkc example);

	void deleteAll();

	int insertBatchList(List<CsBzhkc> csBzhkcs);
}
