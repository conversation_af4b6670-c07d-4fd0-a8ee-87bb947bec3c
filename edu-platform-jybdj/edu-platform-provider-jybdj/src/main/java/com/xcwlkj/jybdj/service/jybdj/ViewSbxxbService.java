/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.service.jybdj;

import java.util.List;

import org.springframework.stereotype.Service;

import com.xcwlkj.jybdj.model.domain.ViewSbxxb;

import tk.mybatis.mapper.entity.Example;



/**
 * 视图-设备信息表服务
 * <AUTHOR>
 * @version $Id: ViewSbxxbService.java, v 0.1 2020年06月02日 20时49分 xcwlkj.com Exp $
 */
@Service
public interface ViewSbxxbService  {

    /**
     * 根据条件查询
     */
    List<ViewSbxxb> selectByExample(Example example);
}