/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 考试管理机构
 * 
 * <AUTHOR>
 * @version $Id: CsKsgljg.java, v 0.1 2020年08月19日 10时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "cs_ksgljg")
public class CsKsgljg implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /**  */
    @Id
    @Column(name = "ksgljgid")
    private String            ksgljgid;
    /** 节点祖先 */
    @Column(name = "jdzx")
    private String            jdzx;
    /** 上级考试管理机构码 */
    @Column(name = "sjgljgid")
    private String            sjgljgid;
    /** 名称 */
    @Column(name = "ksgljgmc")
    private String            ksgljgmc;
    /** 简称 */
    @Column(name = "ksgljgjc")
    private String            ksgljgjc;
    /** 所在省码 */
    @Column(name = "ksszsfm")
    private String            ksszsfm;
    /** 所在省 */
    @Column(name = "ksszsf")
    private String            ksszsf;
    /** 所在市码 */
    @Column(name = "ksszsqm")
    private String            ksszsqm;
    /** 所在市 */
    @Column(name = "ksszsq")
    private String            ksszsq;
    /** 所在区县码 */
    @Column(name = "ksszqxm")
    private String            ksszqxm;
    /** 所在区县 */
    @Column(name = "ksszqx")
    private String            ksszqx;
    /** 地址 */
    @Column(name = "ksgljgdz")
    private String            ksgljgdz;
    /** 经度 */
    @Column(name = "ksgljgjd")
    private BigDecimal            ksgljgjd;
    /** 纬度 */
    @Column(name = "ksgljgwd")
    private BigDecimal            ksgljgwd;
    /** 管辖范围行政规划码 */
    @Column(name = "gxfwxzqhm")
    private String            gxfwxzqhm;
    /** 负责人姓名 */
    @Column(name = "fzrxm")
    private String            fzrxm;
    /** 负责人电话 */
    @Column(name = "fzrdh")
    private String            fzrdh;
    /** 负责人编号 */
    @Column(name = "fzrbh")
    private String            fzrbh;
    /** 联系人姓名 */
    @Column(name = "lxrxm")
    private String            lxrxm;
    /** 联系人电话 */
    @Column(name = "lxrdh")
    private String            lxrdh;
    /** 联系人编号 */
    @Column(name = "lxrbh")
    private String            lxrbh;
    /** 技术负责人姓名 */
    @Column(name = "jsfzrxm")
    private String            jsfzrxm;
    /** 技术负责人电话 */
    @Column(name = "jsfzrdh")
    private String            jsfzrdh;
    /** 技术负责人编号 */
    @Column(name = "jsfzrbh")
    private String            jsfzrbh;
    /** sip地址 */
    @Column(name = "sipdz")
    private String            sipdz;
    /** 删除状态 */
    @Column(name = "sczt")
    private String            sczt;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;
    /** 上报状态 */
    @Column(name = "sbzt")
    private String            sbzt;
    /** 上报时间 */
    @Column(name = "sbsj")
    private Date            sbsj;
    /** 同步状态 */
    @Column(name = "tbzt")
    private String            tbzt;
    /** 更新状态 */
    @Column(name = "gxzt")
    private String            gxzt;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


