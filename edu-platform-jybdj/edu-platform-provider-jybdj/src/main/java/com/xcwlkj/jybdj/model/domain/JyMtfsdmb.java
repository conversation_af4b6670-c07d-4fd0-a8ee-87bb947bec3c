/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 教育部-命题方式代码表
 * 
 * <AUTHOR>
 * @version $Id: JyMtfsdmb.java, v 0.1 2020年04月29日 15时25分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "jy_mtfsdmb")
public class JyMtfsdmb implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 命题方式码 */
    @Id
    @Column(name = "dm")
    private String            dm;
    /** 命题方式 */
    @Column(name = "mc")
    private String            mc;
    /** 说明 */
    @Column(name = "sm")
    private String            sm;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


