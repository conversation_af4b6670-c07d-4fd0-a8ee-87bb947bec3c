/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.mapper.zxdj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.jybdj.model.domain.JyCzsbsxjazfwdmb;



/**
 * 教育部-车载设备摄像机安置方位代码表数据库操作
 * <AUTHOR>
 * @version $Id: InitJyCzsbsxjazfwdmbMapper.java, v 0.1 2020年04月29日 15时22分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface JyCzsbsxjazfwdmbMapper extends MyMapper<JyCzsbsxjazfwdmb> {

    /**
	 * 分页查询教育部-车载设备摄像机安置方位代码表
	 * 
	 * @param example
	 * @return
	 */
	List<JyCzsbsxjazfwdmb> pageList(JyCzsbsxjazfwdmb example);
}
