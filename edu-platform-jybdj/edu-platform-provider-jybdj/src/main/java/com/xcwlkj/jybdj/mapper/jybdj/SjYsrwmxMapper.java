/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.jybdj.mapper.jybdj;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.jybdj.model.domain.SjYsrwmx;



/**
 * 试卷-运送任务明细数据库操作
 * <AUTHOR>
 * @version $Id: InitSjYsrwmxMapper.java, v 0.1 2020年06月09日 19时37分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface SjYsrwmxMapper extends MyMapper<SjYsrwmx> {

    /**
	 * 分页查询试卷-运送任务明细
	 * 
	 * @param example
	 * @return
	 */
	List<SjYsrwmx> pageList(SjYsrwmx example);

	/**
	 * 清空sj_ysrwmx表数据
	 */
	void truncateSjysrwmx();

	/**
	 * 批量添加
	 */
	int insertSjysrwmxList(List<SjYsrwmx> sjYsrwmxList);
}
