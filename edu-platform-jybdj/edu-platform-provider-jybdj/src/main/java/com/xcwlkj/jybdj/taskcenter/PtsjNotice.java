package com.xcwlkj.jybdj.taskcenter;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.core.util.SpringBeanUtil;
import com.xcwlkj.model.enums.GeneralMsgTopic;
import com.xcwlkj.model.enums.PtsjTbczmEnum;
import com.xcwlkj.model.msg.PtsjGeneralModel;
import com.xcwlkj.msgque.model.domain.XcMsgModel;
import com.xcwlkj.msgque.service.AbstractRouteMsgExecutor;

import lombok.extern.slf4j.Slf4j;

/**
 * 统一的数据同步，单表的增删改
 * 
 * <AUTHOR> Wolf
 * @version $Id: PtsjNotice.java, v 0.1 Apr 15, 2020 2:53:54 PM White Wolf Exp $
 */
@Service("ptsjNotice")
@Slf4j
public class PtsjNotice extends AbstractRouteMsgExecutor {

    @Autowired
    private SpringBeanUtil springBeanUtil;

    @SuppressWarnings({ "static-access", "rawtypes", "unchecked" })
    @Override
    public void exec(XcMsgModel model) {
        log.info("收到同步数据：" + model.getMsgParam());
        PtsjGeneralModel generalModel = JSONObject.parseObject(model.getMsgParam(),
            PtsjGeneralModel.class);

        try {
            String className = "com.xcwlkj.jybdj.mapper.jybdj." + generalModel.getMapperName();

            Class mapperClass = Class.forName(className);

            Object mapperObject = springBeanUtil.getBean(mapperClass);
            Method[] methods = mapperClass.getMethods();
            for (Method method : methods) {
                if (method.getName().equals(generalModel.getOperateCode())) {
                    try {
                        if (generalModel.getOperateCode().equals(PtsjTbczmEnum.DELETEEXAMPLE.getCode())) {
                            Class exampleClass = method.getParameterTypes()[0];
                            method.invoke(mapperObject, 
                                JSONObject.parseObject(generalModel.getExampleDate(),
                                    exampleClass));
                        }else {
                            Class parameterClass = method.getParameterTypes()[0];

                            JSONObject paramData = JSONObject.parseObject(generalModel.getParamData());
                            //同步是修改操作时gxzt设置为1(已更新);新增操作gxzt设置为0(未更新)
                            if (generalModel.getOperateCode().contains("update")) {
                                paramData.put("gxzt", "1");
                            } else if (generalModel.getOperateCode().contains("insert")) {
                                paramData.put("gxzt", "0");
                            }

                            if (generalModel.getOperateCode().contains("Example")) {
                                Class exampleClass = method.getParameterTypes()[1];
                                method.invoke(mapperObject, paramData.toJavaObject(parameterClass),
                                    JSONObject.parseObject(generalModel.getExampleDate(),
                                        exampleClass));
                            } else {
                                method.invoke(mapperObject, paramData.toJavaObject(parameterClass));
                            }
                        }
                        

                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (IllegalArgumentException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public String getMsgTopic() {
        return GeneralMsgTopic.PTYWSJTB.getCode();
    }

    @Override
    public String getMsgChannel() {
        return null;
    }

}
