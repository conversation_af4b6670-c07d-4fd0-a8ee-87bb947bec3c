<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.jybdj.mapper.zxdj.JySjysrwlxdmbMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.jybdj.model.domain.JySjysrwlxdmb">
        <id column="dm" jdbcType="CHAR" property="dm" />
        <result column="mc" jdbcType="CHAR" property="mc" />
        <result column="sm" jdbcType="CHAR" property="sm" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        dm,
        mc,
        sm

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="dm != null and dm != ''">
            AND dm = #{dm,jdbcType=CHAR}
        </if>
        <if test="mc != null and mc != ''">
            AND mc = #{mc,jdbcType=CHAR}
        </if>
        <if test="sm != null and sm != ''">
            AND sm = #{sm,jdbcType=CHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="dm != null ">
            dm = #{dm,jdbcType=CHAR},
        </if>
        <if test="mc != null ">
            mc = #{mc,jdbcType=CHAR},
        </if>
        <if test="sm != null ">
            sm = #{sm,jdbcType=CHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.jybdj.model.domain.JySjysrwlxdmb"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from jy_sjysrwlxdmb
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
