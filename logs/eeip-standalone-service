2025-07-25 10:00:32.896  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6cc4ae92] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:33.414  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-25 10:00:35.596  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
2025-07-25 10:00:35.630  WARN [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2025-07-25 10:00:35.632  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.standalone.StandaloneApplication     : The following profiles are active: alone
2025-07-25 10:00:43.168  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 10:00:43.172  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 10:00:43.463  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 270ms. Found 0 Redis repository interfaces.
2025-07-25 10:00:43.601  WARN [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-25 10:00:44.105  WARN [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-25 10:00:44.766  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=31fb2835-41d9-360e-af2d-43ef507370ed
2025-07-25 10:00:44.795  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-25 10:00:44.800  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-25 10:00:44.814  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-25 10:00:44.922  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$ada28b2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:44.922  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$66d4d05b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.056  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$466e43ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.507  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$50aaab95] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.563  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$2c4d8912] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.623  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$686ebab8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.688  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.742  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.748  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$bb9c66c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:45.813  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6cc4ae92] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 10:00:47.706  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8888 (http)
2025-07-25 10:00:48.080  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 12420 ms
2025-07-25 10:00:51.613  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.b.a.e.web.ServletEndpointRegistrar   : Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
2025-07-25 10:00:51.713  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 10:00:51.823  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 10:00:51.944  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 10:00:51.949  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-25 10:00:51.949  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-25 10:00:51.949  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-25 10:00:51.949  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-25 10:00:53.126  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-25 10:00:56.571 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-25 10:00:56.573  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 10:00:58.585  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-25 10:00:58.624  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-25 10:00:59.563  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-25 10:00:59.698 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@38cc8baa],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@672035c7],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@367f3fe5],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@351e16c],]
2025-07-25 10:00:59.698 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-25 10:01:13.292  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-25 10:01:13.302  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-25 10:01:14.968  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-25 10:01:15.258  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 开始检查平台类型和初始化上级平台DFS客户端...
2025-07-25 10:01:15.948 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 10:01:16.008 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: defaultPlat(String)
2025-07-25 10:01:16.053 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 10:01:16.063  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 当前平台类型为：HISOME，开始初始化上级平台DFS客户端
2025-07-25 10:01:16.063 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 10:01:16.063 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerUrl(String)
2025-07-25 10:01:16.298 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 10:01:16.298 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 10:01:16.298 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerChannel(String)
2025-07-25 10:01:16.308 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 10:01:16.308 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 10:01:16.308 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServiceAppId(String)
2025-07-25 10:01:16.313 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 10:01:16.318 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 10:01:16.318 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServiceAppSecret(String)
2025-07-25 10:01:16.323 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 10:01:16.345  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 上级平台DFS客户端初始化成功
2025-07-25 10:01:16.345  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 上级平台DFS客户端自动初始化完成
2025-07-25 10:01:18.433  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-25 10:01:18.433 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-25 10:01:18.433 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-25 10:01:18.443 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 10
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-25 10:01:18.503  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-25 10:01:18.506  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-25 10:01:18.506  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-25 10:01:18.508  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]
2025-07-25 10:01:18.518  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-25 10:01:23.415  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-25 10:01:23.415  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-25 10:01:23.415  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-25 10:01:23.460  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-25 10:01:25.231  WARN [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-25 10:01:26.720  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-25 10:01:29.211  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-25 10:01:29.216  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-25 10:01:29.216  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.handler.serviceActivator
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.mqttOutbound.serviceActivator
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-25 10:01:29.218  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-25 10:01:29.284  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-25 10:01:33.278  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
2025-07-25 10:01:33.871  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-25 10:01:33.871  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-25 10:01:34.050  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8888 (http) with context path ''
2025-07-25 10:01:34.052  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8888
2025-07-25 10:01:34.348  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.standalone.StandaloneApplication     : Started StandaloneApplication in 63.287 seconds (JVM running for 64.541)
2025-07-25 10:01:34.413  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-25 10:01:34.413  WARN [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-alone.yml
2025-07-25 10:01:34.418  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-25 10:01:34.773 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-25 10:01:34.788 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-25 10:01:34.788 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-25 10:01:34.799 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-25 10:01:34.804 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-25 10:01:34.804 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-25 10:01:34.818 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 295
2025-07-25 10:01:34.828  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-25 10:01:34.828  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-25 10:01:34.832  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
2025-07-25 10:01:34.888 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-25 10:01:35.708  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-25 10:01:35.714  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-25 10:01:35.717  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-25 10:01:35.717  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 10:01:35.717  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 10:01:35.738  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-25 10:01:36.313  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-25 10:01:36.313  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-25 10:01:36.319 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-25 10:01:36.324 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-25 10:01:36.331 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-25 10:01:36.340 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-25 10:01:36.340 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:36.349 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-25 10:01:37.013 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-25 10:01:37.013 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:37.018 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-25 10:01:37.166 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-25 10:01:37.166 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:37.173 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-25 10:01:38.353 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-25 10:01:38.353 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.363 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-25 10:01:38.405 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-25 10:01:38.405 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.412 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-25 10:01:38.471 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-25 10:01:38.471 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.478 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-25 10:01:38.548 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-25 10:01:38.548 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.560 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-25 10:01:38.798 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-25 10:01:38.798 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.802 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-25 10:01:38.858 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-25 10:01:38.858 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.866 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-25 10:01:38.963 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-25 10:01:38.963 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 10:01:38.970 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-25 10:01:39.002  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-25 10:01:39.002  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-25 10:01:39.002  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 10:01:39.003  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-25 10:01:39.003  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-25 10:01:39.014 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-25 10:01:39.017 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-25 10:01:39.011(Timestamp), 1(Integer)
2025-07-25 10:01:39.036 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-25 10:01:39.036  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-25 10:01:39.036  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-25 10:01:39.036  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-25 10:01:39.106 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.108 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)
2025-07-25 10:01:39.115 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.117 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.117 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-25 10:01:39.124 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.124 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.124 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-25 10:01:39.129 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.133 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.133 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-25 10:01:39.138 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.138 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.138 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-25 10:01:39.148 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.148 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.148 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-25 10:01:39.153 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.153 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.153 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-25 10:01:39.163 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.163 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.163 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-25 10:01:39.170 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.170 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.170 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-25 10:01:39.178 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.179 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.179 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-25 10:01:39.184 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.184 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.184 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)
2025-07-25 10:01:39.194 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.194 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.194 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)
2025-07-25 10:01:39.200 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.200 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.200 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)
2025-07-25 10:01:39.209 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.209 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.209 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)
2025-07-25 10:01:39.215 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.215 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 10:01:39.218 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-25 10:01:39.232 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-25 10:01:39.232 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-25 10:01:39.232 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-25 10:01:39.246 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-25 10:01:39.246 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-25 10:01:39.246 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-25 10:01:39.258 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-25 10:01:39.258 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-25 10:01:39.258 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-25 10:01:39.278 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-25 10:01:39.278 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 10:01:39.278 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-25 10:01:39.295 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-25 10:01:39.295 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 10:01:39.295 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-25 10:01:39.309 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-25 10:01:39.309 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 10:01:39.309 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-25 10:01:39.320 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-25 10:01:39.320 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 10:01:39.320 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-25 10:01:39.336 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-25 10:01:39.340 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-25 10:01:39.340 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-25 10:01:39.349 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-25 10:01:39.349 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.349 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-25 10:01:39.359 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.359 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.359 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-25 10:01:39.366 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.368 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.368 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-25 10:01:39.375 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.375 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.375 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-25 10:01:39.383 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.383 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-25 10:01:39.383 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-25 10:01:39.395 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-25 10:01:39.398 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.398 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-25 10:01:39.406 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.408 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-25 10:01:39.409 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-25 10:01:39.426 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-25 10:01:39.426  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-25 10:01:39.426 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.426 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-25 10:01:39.431 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.431 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.431 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)
2025-07-25 10:01:39.441 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.441 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 10:01:39.441 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)
2025-07-25 10:01:39.448 DEBUG [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 10:01:39.448  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-25 10:01:39.448  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-25 10:01:39.448  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 10:01:41.894  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] .m.i.MqttPahoMessageDrivenChannelAdapter : stopped inbound
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] ProxyFactoryBean$MethodInvocationGateway : stopped mqttGateway
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.gateway.GatewayProxyFactoryBean    : stopped mqttGateway
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 0 subscriber(s).
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped _org.springframework.integration.errorLogger
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 0 subscriber(s).
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.handler.serviceActivator
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s).
2025-07-25 10:01:45.942  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.i.endpoint.EventDrivenConsumer       : stopped mqttConfig.mqttOutbound.serviceActivator
2025-07-25 10:01:48.098  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.s.quartz.SchedulerFactoryBean        : Shutting down Quartz Scheduler
2025-07-25 10:01:48.099  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.s.c.ThreadPoolTaskScheduler          : Shutting down ExecutorService 'taskScheduler'
2025-07-25 10:01:48.099  INFO [,,,] [eeip-standalone-service,,,,] 21556 --- [      Thread-23] o.s.s.concurrent.ThreadPoolTaskExecutor  : Shutting down ExecutorService
2025-07-25 14:38:19.201  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a16acc24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:19.657  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-25 14:38:21.790  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
2025-07-25 14:38:21.791  WARN [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2025-07-25 14:38:21.793  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.standalone.StandaloneApplication     : The following profiles are active: alone
2025-07-25 14:38:27.673  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-25 14:38:27.677  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 14:38:27.918  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 221ms. Found 0 Redis repository interfaces.
2025-07-25 14:38:28.092  WARN [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format.
2025-07-25 14:38:28.303  WARN [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.boot.actuate.endpoint.EndpointId     : Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format.
2025-07-25 14:38:28.794  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=39628f2d-6ced-3ca8-8d4d-09da33aaccab
2025-07-25 14:38:28.820  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-25 14:38:28.828  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created.
2025-07-25 14:38:28.843  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-25 14:38:28.960  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$e248a8bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:28.961  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$9b7aeded] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.055  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$7b14614c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.455  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8550c927] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.506  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$60f3a6a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.557  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$9d14d84a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.606  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.661  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.676  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$f0428456] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:29.729  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a16acc24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:38:30.815  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8888 (http)
2025-07-25 14:38:31.013  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.web.context.ContextLoader            : Root WebApplicationContext: initialization completed in 9195 ms
2025-07-25 14:38:33.957  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.b.a.e.web.ServletEndpointRegistrar   : Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint
2025-07-25 14:38:34.058  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 14:38:34.173  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 14:38:34.212  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 14:38:34.217  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsServerUrl=http://************:8811
2025-07-25 14:38:34.217  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsChannel=ZJKSZHPT
2025-07-25 14:38:34.217  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182
2025-07-25 14:38:34.217  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.xcwlkj.standalone.config.XcDfsConfig   : ###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55
2025-07-25 14:38:35.089  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.interceptor.SqlLogInterceptor   : [打印SQL拦截器创建]noticeTime=5.0秒
2025-07-25 14:38:37.654 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.c.c.AsyncTaskExecutorConfiguration   : Creating Async Task Executor
2025-07-25 14:38:37.656  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 14:38:39.142  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : SMS Bean IAcsClient Start
2025-07-25 14:38:39.155  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.pubc.config.SmsConfig         : 加载SMS Bean IAcsClient OK
2025-07-25 14:38:46.315  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化####
2025-07-25 14:38:46.324  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.identityverify.config.XcDfsConfig    : ####文件系统初始化成功####
2025-07-25 14:38:47.630  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.taskcenter.DefaultHandleFactory    : 配置线程池工作线程数量[16]
2025-07-25 14:38:47.752  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 开始检查平台类型和初始化上级平台DFS客户端...
2025-07-25 14:38:48.024 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 14:38:48.040 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: defaultPlat(String)
2025-07-25 14:38:48.056 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 14:38:48.061  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 当前平台类型为：HISOME，开始初始化上级平台DFS客户端
2025-07-25 14:38:48.063 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 14:38:48.063 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerUrl(String)
2025-07-25 14:38:48.071 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 14:38:48.072 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 14:38:48.072 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServerChannel(String)
2025-07-25 14:38:48.081 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 14:38:48.082 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 14:38:48.083 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServiceAppId(String)
2025-07-25 14:38:48.089 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 14:38:48.091 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) 
2025-07-25 14:38:48.091 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : ==> Parameters: HISOME_fileServiceAppSecret(String)
2025-07-25 14:38:48.099 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.J.selectOneByExample             : <==      Total: 1
2025-07-25 14:38:48.110  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 上级平台DFS客户端初始化成功
2025-07-25 14:38:48.110  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.handler.UpperHsDfsHandler          : 上级平台DFS客户端自动初始化完成
2025-07-25 14:38:49.388  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动中....
2025-07-25 14:38:49.389 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job 
2025-07-25 14:38:49.389 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : ==> Parameters: 
2025-07-25 14:38:49.398 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.ScheduleJobMapper.selectAll      : <==      Total: 10
2025-07-25 14:38:49.414  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[teskTask-jobTest111]
2025-07-25 14:38:49.414  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]
2025-07-25 14:38:49.415  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]
2025-07-25 14:38:49.415  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]
2025-07-25 14:38:49.416  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]
2025-07-25 14:38:49.416  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[sbxxReportTaskService-设备信息上报]
2025-07-25 14:38:49.417  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]
2025-07-25 14:38:49.417  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]
2025-07-25 14:38:49.418  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]
2025-07-25 14:38:49.418  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]
2025-07-25 14:38:49.421  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.s.impl.ScheduleJobServiceImpl      : ###定时任务初始化启动完成
2025-07-25 14:38:50.737  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.c.ThreadPoolTaskScheduler          : Initializing ExecutorService 'taskScheduler'
2025-07-25 14:38:51.096 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@14aba843],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@1e8acc51],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@426f5329],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@21c92b38],]
2025-07-25 14:38:51.097 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .FeignHystrixConcurrencyStrategyIntellif : Registering Sleuth Hystrix Concurrency Strategy.
2025-07-25 14:38:55.864  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]
2025-07-25 14:38:55.865  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]
2025-07-25 14:38:55.865  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.standalone.config.MqttConfig  : ######监听MQTT主题[/+/+/event/+]
2025-07-25 14:38:55.891  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.xcwlkj.standalone.config.XxlJobConfig  : >>>>>>>>>>> xxl-job config init.
2025-07-25 14:38:57.022  WARN [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.b.a.f.FreeMarkerAutoConfiguration    : Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)
2025-07-25 14:38:58.219  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 21 endpoint(s) beneath base path '/actuator'
2025-07-25 14:38:59.655  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-25 14:38:59.656  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application-1.errorChannel' has 1 subscriber(s).
2025-07-25 14:38:59.656  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started _org.springframework.integration.errorLogger
2025-07-25 14:38:59.656  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel
2025-07-25 14:38:59.656  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s).
2025-07-25 14:38:59.656  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.mqttOutbound.serviceActivator
2025-07-25 14:38:59.657  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel
2025-07-25 14:38:59.657  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.integration.channel.ExecutorChannel  : Channel 'application-1.mqttInputChannel' has 1 subscriber(s).
2025-07-25 14:38:59.657  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.endpoint.EventDrivenConsumer       : started mqttConfig.handler.serviceActivator
2025-07-25 14:38:59.657  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] ProxyFactoryBean$MethodInvocationGateway : started mqttGateway
2025-07-25 14:38:59.657  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.i.gateway.GatewayProxyFactoryBean    : started mqttGateway
2025-07-25 14:38:59.685  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.c.n.eureka.InstanceInfoFactory       : Setting initial instance status as: STARTING
2025-07-25 14:39:02.534  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.c.n.e.s.EurekaServiceRegistry        : Registering application EEIP-STANDALONE-SERVICE with eureka with status UP
2025-07-25 14:39:02.949  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .m.i.MqttPahoMessageDrivenChannelAdapter : started inbound
2025-07-25 14:39:02.949  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.quartz.SchedulerFactoryBean        : Starting Quartz Scheduler now
2025-07-25 14:39:03.053  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8888 (http) with context path ''
2025-07-25 14:39:03.054  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .s.c.n.e.s.EurekaAutoServiceRegistration : Updating port to 8888
2025-07-25 14:39:03.315  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.standalone.StandaloneApplication     : Started StandaloneApplication in 45.917 seconds (JVM running for 46.418)
2025-07-25 14:39:03.343  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息
2025-07-25 14:39:03.343  WARN [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.util.YmlUtil                  : 读取外部文件失败D:\code\HS_CUEEIP\trunk\provider/config/application-alone.yml
2025-07-25 14:39:03.348  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.util.YmlUtil                  : 开始从application-alone.yml加载配置信息【成功】
2025-07-25 14:39:03.399 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.cache.AbstractRedisDataCache  : [PkgDataFileServices]cleanup cache finished
2025-07-25 14:39:03.404 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) 
2025-07-25 14:39:03.404 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: -1(Integer), 0(Integer), 1(Integer)
2025-07-25 14:39:03.418 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-25 14:39:03.419 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.clearDoing                     : ==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end 
2025-07-25 14:39:03.419 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.clearDoing                     : ==> Parameters: 
2025-07-25 14:39:03.432 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.clearDoing                     : <==    Updates: 295
2025-07-25 14:39:03.436  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，init. workerId=0,datacenterId=0
2025-07-25 14:39:03.437  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SnowflakeIdGenerator，finish<<<<<<<<<<<<<
2025-07-25 14:39:03.437  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service
2025-07-25 14:39:03.452 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.c.z.r.z.ZookeeperRegistryCenter      : Elastic job: zookeeper registry center init, server lists is: **************:2181.
2025-07-25 14:39:03.783  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###ZookeeperInitRunner，finish<<<<<<<<<<<<<
2025-07-25 14:39:03.786  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####开始缓存商户配置信息####
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.b.c.BasicinfoBusiCacheInitConfig     : ####缓存商户配置信息缓存成功####
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####开始缓存配置信息####
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####缓存配置信息缓存成功####
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化####
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] com.xcwlkj.biz.config.BizInitConfig      : ####消息队列初始化成功####
2025-07-25 14:39:03.787  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####开始缓存公共服务配置信息####
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.pubc.config.PubcBusiCacheInitConfig  : ####缓存公共服务配置信息缓存成功####
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####开始缓存听评课配置信息####
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####缓存听评课配置信息缓存成功####
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化####
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.e.c.EvaluationBusiCacheInitConfig    : ####消息队列初始化成功####
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 14:39:03.788  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Init##
2025-07-25 14:39:03.802  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####开始缓存校级身份核验平台配置信息####
2025-07-25 14:39:04.392  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####服务器序列号为CENCGW100_SIV1c9f717de4acfc99
2025-07-25 14:39:04.392  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化####
2025-07-25 14:39:04.397 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) 
2025-07-25 14:39:04.397 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : ==> Parameters: 1(String)
2025-07-25 14:39:04.406 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbPzbMapper.selectByExample      : <==      Total: 10
2025-07-25 14:39:04.409 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_mzdmb order by DM asc 
2025-07-25 14:39:04.409 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:04.419 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 58
2025-07-25 14:39:05.071 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc 
2025-07-25 14:39:05.071 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:05.078 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 12
2025-07-25 14:39:05.219 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc 
2025-07-25 14:39:05.219 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:05.229 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 106
2025-07-25 14:39:06.378  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [      Thread-25] o.s.c.n.e.s.EurekaServiceRegistry        : Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN
2025-07-25 14:39:06.407 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc 
2025-07-25 14:39:06.407 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:06.415 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-25 14:39:06.457 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc 
2025-07-25 14:39:06.458 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:06.465 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 5
2025-07-25 14:39:06.529 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjlxmb order by DM asc 
2025-07-25 14:39:06.530 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:06.536 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 3
2025-07-25 14:39:06.585 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc 
2025-07-25 14:39:06.585 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:06.594 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 20
2025-07-25 14:39:06.830 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc 
2025-07-25 14:39:06.830 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:06.838 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 4
2025-07-25 14:39:06.891 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc 
2025-07-25 14:39:06.891 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:06.898 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 8
2025-07-25 14:39:06.996 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc 
2025-07-25 14:39:06.997 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : ==> Parameters: 
2025-07-25 14:39:07.003 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.MbxxsjMapper.queryByTableName    : <==      Total: 2
2025-07-25 14:39:07.033  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####码表数据缓存初始化成功####
2025-07-25 14:39:07.033  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化####
2025-07-25 14:39:07.034  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] o.s.s.concurrent.ThreadPoolTaskExecutor  : Initializing ExecutorService
2025-07-25 14:39:07.035  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####延迟队列初始化成功####
2025-07-25 14:39:07.035  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化####
2025-07-25 14:39:07.041 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) 
2025-07-25 14:39:07.042 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.updateByExampleSelective       : ==> Parameters: 2(Integer), 2025-07-25 14:39:07.036(Timestamp), 1(Integer)
2025-07-25 14:39:07.055 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.K.updateByExampleSelective       : <==    Updates: 0
2025-07-25 14:39:07.056  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据下发任务初始化成功####
2025-07-25 14:39:07.056  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化####
2025-07-25 14:39:07.056  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : dbName:[eeip_alone]
2025-07-25 14:39:07.118 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.119 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)
2025-07-25 14:39:07.126 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.127 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.127 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)
2025-07-25 14:39:07.134 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.135 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.135 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)
2025-07-25 14:39:07.141 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.142 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.142 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)
2025-07-25 14:39:07.149 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.150 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.150 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)
2025-07-25 14:39:07.157 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.157 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.158 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)
2025-07-25 14:39:07.164 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.164 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.164 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)
2025-07-25 14:39:07.171 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.171 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.171 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)
2025-07-25 14:39:07.177 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.178 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.178 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)
2025-07-25 14:39:07.184 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.185 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.185 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)
2025-07-25 14:39:07.191 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.192 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.192 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)
2025-07-25 14:39:07.199 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.199 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.200 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)
2025-07-25 14:39:07.206 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.207 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.207 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)
2025-07-25 14:39:07.214 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.214 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.214 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)
2025-07-25 14:39:07.221 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.222 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 14:39:07.222 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : ==> Parameters: 
2025-07-25 14:39:07.235 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createCsDzgh    : <==    Updates: 0
2025-07-25 14:39:07.236 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-25 14:39:07.236 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : ==> Parameters: 
2025-07-25 14:39:07.249 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbIpdfp   : <==    Updates: 0
2025-07-25 14:39:07.250 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 
2025-07-25 14:39:07.250 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : ==> Parameters: 
2025-07-25 14:39:07.262 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbSbjcb   : <==    Updates: 0
2025-07-25 14:39:07.264 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' 
2025-07-25 14:39:07.264 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : ==> Parameters: 
2025-07-25 14:39:07.276 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createKsBmxxKstzz    : <==    Updates: 0
2025-07-25 14:39:07.277 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 14:39:07.277 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : ==> Parameters: 
2025-07-25 14:39:07.290 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createKsYdsbSbkszs   : <==    Updates: 0
2025-07-25 14:39:07.291 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 14:39:07.291 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : ==> Parameters: 
2025-07-25 14:39:07.304 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizCsxxExt     : <==    Updates: 0
2025-07-25 14:39:07.304 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 14:39:07.305 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : ==> Parameters: 
2025-07-25 14:39:07.318 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTask    : <==    Updates: 0
2025-07-25 14:39:07.319 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 
2025-07-25 14:39:07.319 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : ==> Parameters: 
2025-07-25 14:39:07.331 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.createBizFsgjTaskJg  : <==    Updates: 0
2025-07-25 14:39:07.333 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) 
2025-07-25 14:39:07.334 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : ==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)
2025-07-25 14:39:07.341 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.filterTables    : <==      Total: 2
2025-07-25 14:39:07.342 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.343 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)
2025-07-25 14:39:07.350 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.351 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.351 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)
2025-07-25 14:39:07.357 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.357 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.357 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)
2025-07-25 14:39:07.364 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.364 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.364 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)
2025-07-25 14:39:07.371 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.372 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' 
2025-07-25 14:39:07.373 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : ==> Parameters: 
2025-07-25 14:39:07.385 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.createSbsj      : <==    Updates: 0
2025-07-25 14:39:07.386 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.386 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)
2025-07-25 14:39:07.393 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.394 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; 
2025-07-25 14:39:07.394 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : ==> Parameters: 同步状态(String)
2025-07-25 14:39:07.413 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.mapper.UtilsMapper.alterColumn     : <==    Updates: 0
2025-07-25 14:39:07.413  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.service.impl.UtilsServiceImpl      : 数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更
2025-07-25 14:39:07.414 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.414 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)
2025-07-25 14:39:07.421 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.421 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.422 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)
2025-07-25 14:39:07.428 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.428 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? 
2025-07-25 14:39:07.429 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : ==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)
2025-07-25 14:39:07.436 DEBUG [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.i.m.UtilsMapper.findColumnExist      : <==      Total: 1
2025-07-25 14:39:07.436  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####数据库变更初始化成功####
2025-07-25 14:39:07.436  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] .x.i.c.IdentityverifyBusiCacheInitConfig : ####缓存校级身份核验平台配置信息缓存成功####
2025-07-25 14:39:07.436  INFO [,,,] [eeip-standalone-service,,,,] 28776 --- [           main] c.x.core.config.ZookeeperInitRunner      : ###SystemCache Succ<<<<<<<<<<<<<##
2025-07-25 14:50:53.632  INFO [,,,] [eeip-standalone-service,,,,] 34012 --- [           main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f9f7ce77] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-25 14:50:54.091  INFO [,,,] [eeip-standalone-service,,,,] 34012 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Fetching config from server at : http://localhost:8888
2025-07-25 14:50:56.234  INFO [,,,] [eeip-standalone-service,,,,] 34012 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available
2025-07-25 14:50:56.235  WARN [,,,] [eeip-standalone-service,,,,] 34012 --- [           main] c.c.c.ConfigServicePropertySourceLocator : Could not locate PropertySource: I/O error on GET request for "http://localhost:8888/eeip-standalone-service/default": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect
2025-07-25 14:50:56.239  INFO [,,,] [eeip-standalone-service,,,,] 34012 --- [           main] c.x.standalone.StandaloneApplication     : The following profiles are active: alone
