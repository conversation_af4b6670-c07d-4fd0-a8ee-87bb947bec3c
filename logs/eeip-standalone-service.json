{"@timestamp":"2025-07-25T02:00:32.896Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6cc4ae92] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:33.414Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-25T02:00:35.596Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-25T02:00:35.630Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-25T02:00:35.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-25T02:00:43.168Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-25T02:00:43.172Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-25T02:00:43.463Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 270ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-25T02:00:43.601Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-25T02:00:44.105Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-25T02:00:44.766Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=31fb2835-41d9-360e-af2d-43ef507370ed"}
{"@timestamp":"2025-07-25T02:00:44.795Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-25T02:00:44.800Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-25T02:00:44.814Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-25T02:00:44.922Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$ada28b2b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:44.922Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$66d4d05b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.056Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$466e43ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.507Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$50aaab95] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.563Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$2c4d8912] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.623Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$686ebab8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.688Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.742Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.748Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$bb9c66c4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:45.813Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$6cc4ae92] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T02:00:47.706Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-25T02:00:48.080Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 12420 ms"}
{"@timestamp":"2025-07-25T02:00:51.613Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-25T02:00:51.713Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T02:00:51.823Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T02:00:51.944Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T02:00:51.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T02:00:51.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T02:00:51.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T02:00:51.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T02:00:53.126Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T02:00:56.571Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T02:00:56.573Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T02:00:58.585Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T02:00:58.624Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T02:00:59.563Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-25T02:00:59.698Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@38cc8baa],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@672035c7],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@367f3fe5],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@351e16c],]"}
{"@timestamp":"2025-07-25T02:00:59.698Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T02:01:13.292Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T02:01:13.302Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T02:01:14.968Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T02:01:15.258Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T02:01:15.948Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:16.008Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T02:01:16.053Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:16.063Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T02:01:16.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:16.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T02:01:16.298Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:16.298Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:16.298Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T02:01:16.308Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:16.308Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:16.308Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T02:01:16.313Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:16.318Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:16.318Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T02:01:16.323Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:16.345Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T02:01:16.345Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T02:01:18.433Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-25T02:01:18.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-25T02:01:18.433Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:18.443Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-25T02:01:18.503Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-25T02:01:18.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-25T02:01:18.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-25T02:01:18.508Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]"}
{"@timestamp":"2025-07-25T02:01:18.518Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-25T02:01:23.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-25T02:01:23.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-25T02:01:23.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-25T02:01:23.460Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-25T02:01:25.231Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-25T02:01:26.720Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-25T02:01:29.211Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-25T02:01:29.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-25T02:01:29.216Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-25T02:01:29.218Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-25T02:01:29.284Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-25T02:01:33.278Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-25T02:01:33.871Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-25T02:01:33.871Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-25T02:01:34.050Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-25T02:01:34.052Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-25T02:01:34.348Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 63.287 seconds (JVM running for 64.541)"}
{"@timestamp":"2025-07-25T02:01:34.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-25T02:01:34.413Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-alone.yml"}
{"@timestamp":"2025-07-25T02:01:34.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-25T02:01:34.773Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-25T02:01:34.788Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-25T02:01:34.788Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-25T02:01:34.799Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:34.804Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-25T02:01:34.804Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:34.818Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 295"}
{"@timestamp":"2025-07-25T02:01:34.828Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-25T02:01:34.828Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-25T02:01:34.832Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-25T02:01:34.888Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-25T02:01:35.708Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-25T02:01:35.714Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-25T02:01:35.717Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-25T02:01:35.717Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T02:01:35.717Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T02:01:35.738Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-25T02:01:36.313Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-25T02:01:36.313Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-25T02:01:36.319Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:36.324Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-25T02:01:36.331Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-25T02:01:36.340Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:36.340Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:36.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-25T02:01:37.013Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:37.013Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:37.018Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-25T02:01:37.166Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-25T02:01:37.166Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:37.173Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-25T02:01:38.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:38.353Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.363Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-25T02:01:38.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-25T02:01:38.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.412Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-25T02:01:38.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:38.471Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.478Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-25T02:01:38.548Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-25T02:01:38.548Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.560Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-25T02:01:38.798Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:38.798Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.802Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-25T02:01:38.858Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:38.858Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.866Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-25T02:01:38.963Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-25T02:01:38.963Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:38.970Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-25T02:01:39.002Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-25T02:01:39.002Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-25T02:01:39.002Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T02:01:39.003Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-25T02:01:39.003Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-25T02:01:39.014Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-25T02:01:39.017Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-25 10:01:39.011(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-25T02:01:39.036Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.036Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-25T02:01:39.036Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-25T02:01:39.036Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-25T02:01:39.106Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.108Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-25T02:01:39.115Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.117Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.117Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-25T02:01:39.124Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.124Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.124Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-25T02:01:39.129Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.133Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.133Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-25T02:01:39.138Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.138Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.138Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-25T02:01:39.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.148Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-25T02:01:39.153Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.153Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.153Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-25T02:01:39.163Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.163Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.163Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-25T02:01:39.170Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.170Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.170Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-25T02:01:39.178Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.179Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.179Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-25T02:01:39.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-25T02:01:39.194Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.194Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.194Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-25T02:01:39.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-25T02:01:39.209Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.209Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.209Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-25T02:01:39.215Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.215Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T02:01:39.218Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.232Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.232Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-25T02:01:39.232Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.246Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.246Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-25T02:01:39.246Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.258Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.258Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-25T02:01:39.258Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.278Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.278Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T02:01:39.278Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T02:01:39.295Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.309Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.309Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T02:01:39.309Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.320Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.320Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T02:01:39.320Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.336Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.340Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-25T02:01:39.340Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-25T02:01:39.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-25T02:01:39.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.349Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-25T02:01:39.359Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.359Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.359Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-25T02:01:39.366Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.368Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-25T02:01:39.375Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.375Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.375Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-25T02:01:39.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-25T02:01:39.383Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T02:01:39.395Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-25T02:01:39.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.408Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-25T02:01:39.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-25T02:01:39.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T02:01:39.426Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-25T02:01:39.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.426Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-25T02:01:39.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-25T02:01:39.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T02:01:39.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-25T02:01:39.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T02:01:39.448Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-25T02:01:39.448Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-25T02:01:39.448Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T02:01:41.894Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"stopped inbound"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"stopped mqttGateway"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Removing {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 0 subscriber(s)."}
{"@timestamp":"2025-07-25T02:01:45.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"stopped mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-25T02:01:48.098Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Shutting down Quartz Scheduler"}
{"@timestamp":"2025-07-25T02:01:48.099Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Shutting down ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-25T02:01:48.099Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"21556","thread":"Thread-23","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Shutting down ExecutorService"}
{"@timestamp":"2025-07-25T06:38:19.201Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a16acc24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:19.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-25T06:38:21.790Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-25T06:38:21.791Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-25T06:38:21.793Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
{"@timestamp":"2025-07-25T06:38:27.673Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Multiple Spring Data modules found, entering strict repository configuration mode!"}
{"@timestamp":"2025-07-25T06:38:27.677Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Bootstrapping Spring Data Redis repositories in DEFAULT mode."}
{"@timestamp":"2025-07-25T06:38:27.918Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.d.r.c.RepositoryConfigurationDelegate","rest":"Finished Spring Data repository scanning in 221ms. Found 0 Redis repository interfaces."}
{"@timestamp":"2025-07-25T06:38:28.092Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'service-registry' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-25T06:38:28.303Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.boot.actuate.endpoint.EndpointId","rest":"Endpoint ID 'hystrix.stream' contains invalid characters, please migrate to a valid format."}
{"@timestamp":"2025-07-25T06:38:28.794Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.cloud.context.scope.GenericScope","rest":"BeanFactory id=39628f2d-6ced-3ca8-8d4d-09da33aaccab"}
{"@timestamp":"2025-07-25T06:38:28.820Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created."}
{"@timestamp":"2025-07-25T06:38:28.828Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'taskScheduler' has been explicitly defined. Therefore, a default ThreadPoolTaskScheduler will be created."}
{"@timestamp":"2025-07-25T06:38:28.843Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor","rest":"No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created."}
{"@timestamp":"2025-07-25T06:38:28.960Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'configProperties' of type [com.xcwlkj.core.config.properties.ConfigProperties$$EnhancerBySpringCGLIB$$e248a8bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:28.961Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'asyncTaskExecutorConfiguration' of type [com.xcwlkj.core.config.AsyncTaskExecutorConfiguration$$EnhancerBySpringCGLIB$$9b7aeded] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.055Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.retry.annotation.RetryConfiguration' of type [org.springframework.retry.annotation.RetryConfiguration$$EnhancerBySpringCGLIB$$7b14614c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.455Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$8550c927] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'redisConfig' of type [com.xcwlkj.standalone.config.RedisConfig$$EnhancerBySpringCGLIB$$60f3a6a4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.557Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'appConfig' of type [com.xcwlkj.standalone.config.AppConfig$$EnhancerBySpringCGLIB$$9d14d84a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.606Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'integrationDisposableAutoCreatedBeans' of type [org.springframework.integration.config.annotation.Disposables] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.661Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'com.xcwlkj.biz.core.feignclient.FeignClientConfig' of type [com.xcwlkj.biz.core.feignclient.FeignClientConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.676Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.integration.config.IntegrationManagementConfiguration' of type [org.springframework.integration.config.IntegrationManagementConfiguration$$EnhancerBySpringCGLIB$$f0428456] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:29.729Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a16acc24] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:38:30.815Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat initialized with port(s): 8888 (http)"}
{"@timestamp":"2025-07-25T06:38:31.013Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.web.context.ContextLoader","rest":"Root WebApplicationContext: initialization completed in 9195 ms"}
{"@timestamp":"2025-07-25T06:38:33.957Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.b.a.e.web.ServletEndpointRegistrar","rest":"Registered '/actuator/hystrix.stream' to hystrix.stream-actuator-endpoint"}
{"@timestamp":"2025-07-25T06:38:34.058Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T06:38:34.173Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T06:38:34.212Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T06:38:34.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T06:38:34.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T06:38:34.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T06:38:34.217Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T06:38:35.089Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T06:38:37.654Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T06:38:37.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T06:38:39.142Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T06:38:39.155Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T06:38:46.315Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T06:38:46.324Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T06:38:47.630Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T06:38:47.752Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T06:38:48.024Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:38:48.040Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T06:38:48.056Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:38:48.061Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T06:38:48.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:38:48.063Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T06:38:48.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:38:48.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:38:48.072Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T06:38:48.081Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:38:48.082Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:38:48.083Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T06:38:48.089Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:38:48.091Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:38:48.091Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T06:38:48.099Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:38:48.110Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T06:38:48.110Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T06:38:49.388Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-25T06:38:49.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-25T06:38:49.389Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:38:49.398Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-25T06:38:49.414Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-25T06:38:49.414Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-25T06:38:49.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-25T06:38:49.415Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-25T06:38:49.416Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-25T06:38:49.416Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-25T06:38:49.417Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-25T06:38:49.417Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-25T06:38:49.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-25T06:38:49.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]"}
{"@timestamp":"2025-07-25T06:38:49.421Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-25T06:38:50.737Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskScheduler","rest":"Initializing ExecutorService 'taskScheduler'"}
{"@timestamp":"2025-07-25T06:38:51.096Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@14aba843],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@1e8acc51],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@426f5329],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@21c92b38],]"}
{"@timestamp":"2025-07-25T06:38:51.097Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T06:38:55.864Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/function/invoke]"}
{"@timestamp":"2025-07-25T06:38:55.865Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/CENCGW100_S/CENCGW100_SIV1c9f717de4acfc99/token/get/reply]"}
{"@timestamp":"2025-07-25T06:38:55.865Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.standalone.config.MqttConfig","rest":"######监听MQTT主题[/+/+/event/+]"}
{"@timestamp":"2025-07-25T06:38:55.891Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.standalone.config.XxlJobConfig","rest":">>>>>>>>>>> xxl-job config init."}
{"@timestamp":"2025-07-25T06:38:57.022Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-25T06:38:58.219Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.b.a.e.web.EndpointLinksResolver","rest":"Exposing 21 endpoint(s) beneath base path '/actuator'"}
{"@timestamp":"2025-07-25T06:38:59.655Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel"}
{"@timestamp":"2025-07-25T06:38:59.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.channel.PublishSubscribeChannel","rest":"Channel 'application-1.errorChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-25T06:38:59.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started _org.springframework.integration.errorLogger"}
{"@timestamp":"2025-07-25T06:38:59.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.mqttOutbound.serviceActivator} as a subscriber to the 'mqttOutboundChannel' channel"}
{"@timestamp":"2025-07-25T06:38:59.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttOutboundChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-25T06:38:59.656Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.mqttOutbound.serviceActivator"}
{"@timestamp":"2025-07-25T06:38:59.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"Adding {message-handler:mqttConfig.handler.serviceActivator} as a subscriber to the 'mqttInputChannel' channel"}
{"@timestamp":"2025-07-25T06:38:59.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.integration.channel.ExecutorChannel","rest":"Channel 'application-1.mqttInputChannel' has 1 subscriber(s)."}
{"@timestamp":"2025-07-25T06:38:59.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.endpoint.EventDrivenConsumer","rest":"started mqttConfig.handler.serviceActivator"}
{"@timestamp":"2025-07-25T06:38:59.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.g.GatewayProxyFactoryBean$MethodInvocationGateway","rest":"started mqttGateway"}
{"@timestamp":"2025-07-25T06:38:59.657Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.gateway.GatewayProxyFactoryBean","rest":"started mqttGateway"}
{"@timestamp":"2025-07-25T06:38:59.685Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.netflix.eureka.InstanceInfoFactory","rest":"Setting initial instance status as: STARTING"}
{"@timestamp":"2025-07-25T06:39:02.534Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Registering application EEIP-STANDALONE-SERVICE with eureka with status UP"}
{"@timestamp":"2025-07-25T06:39:02.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.i.m.i.MqttPahoMessageDrivenChannelAdapter","rest":"started inbound"}
{"@timestamp":"2025-07-25T06:39:02.949Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.quartz.SchedulerFactoryBean","rest":"Starting Quartz Scheduler now"}
{"@timestamp":"2025-07-25T06:39:03.053Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.b.w.embedded.tomcat.TomcatWebServer","rest":"Tomcat started on port(s): 8888 (http) with context path ''"}
{"@timestamp":"2025-07-25T06:39:03.054Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.c.n.e.s.EurekaAutoServiceRegistration","rest":"Updating port to 8888"}
{"@timestamp":"2025-07-25T06:39:03.315Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"Started StandaloneApplication in 45.917 seconds (JVM running for 46.418)"}
{"@timestamp":"2025-07-25T06:39:03.343Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息"}
{"@timestamp":"2025-07-25T06:39:03.343Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"读取外部文件失败D:\\code\\HS_CUEEIP\\trunk\\provider/config/application-alone.yml"}
{"@timestamp":"2025-07-25T06:39:03.348Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.util.YmlUtil","rest":"开始从application-alone.yml加载配置信息【成功】"}
{"@timestamp":"2025-07-25T06:39:03.399Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.cache.AbstractRedisDataCache","rest":"[PkgDataFileServices]cleanup cache finished"}
{"@timestamp":"2025-07-25T06:39:03.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_pkg_task SET complete = ? WHERE ( ( complete in ( ? , ? ) ) ) "}
{"@timestamp":"2025-07-25T06:39:03.404Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: -1(Integer), 0(Integer), 1(Integer)"}
{"@timestamp":"2025-07-25T06:39:03.418Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:03.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==>  Preparing: UPDATE ks_kssj_pkg_status SET kssjb_qk = case when kssjb_qk = 1 then -10 else kssjb_qk end, jkryjbsjb_qk = case when jkryjbsjb_qk = 1 then -10 else jkryjbsjb_qk end, jkrybpsjb_qk = case when jkrybpsjb_qk = 1 then -10 else jkrybpsjb_qk end, pzsjb_qk = case when pzsjb_qk = 1 then -10 else pzsjb_qk end, kszpsjb_qk = case when kszpsjb_qk = 1 then -10 else kszpsjb_qk end, jkryzpsjb_qk = case when jkryzpsjb_qk = 1 then -10 else jkryzpsjb_qk end "}
{"@timestamp":"2025-07-25T06:39:03.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:03.432Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.KsKssjPkgStatusMapper.clearDoing","rest":"<==    Updates: 295"}
{"@timestamp":"2025-07-25T06:39:03.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-25T06:39:03.437Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-25T06:39:03.437Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:8888, applicationName=eeip-standalone-service"}
{"@timestamp":"2025-07-25T06:39:03.452Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.c.z.r.z.ZookeeperRegistryCenter","rest":"Elastic job: zookeeper registry center init, server lists is: **************:2181."}
{"@timestamp":"2025-07-25T06:39:03.783Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-25T06:39:03.786Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####开始缓存商户配置信息####"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.b.c.BasicinfoBusiCacheInitConfig","rest":"####缓存商户配置信息缓存成功####"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####开始缓存配置信息####"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####缓存配置信息缓存成功####"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"com.xcwlkj.biz.config.BizInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-25T06:39:03.787Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####开始缓存公共服务配置信息####"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.pubc.config.PubcBusiCacheInitConfig","rest":"####缓存公共服务配置信息缓存成功####"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####开始缓存听评课配置信息####"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####缓存听评课配置信息缓存成功####"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化####"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.e.c.EvaluationBusiCacheInitConfig","rest":"####消息队列初始化成功####"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T06:39:03.788Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Init##"}
{"@timestamp":"2025-07-25T06:39:03.802Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####开始缓存校级身份核验平台配置信息####"}
{"@timestamp":"2025-07-25T06:39:04.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####服务器序列号为CENCGW100_SIV1c9f717de4acfc99"}
{"@timestamp":"2025-07-25T06:39:04.392Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化####"}
{"@timestamp":"2025-07-25T06:39:04.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==>  Preparing: SELECT mbywmc , zjzd , pxzd FROM mb_pzb WHERE ( ( sfqy = ? ) ) "}
{"@timestamp":"2025-07-25T06:39:04.397Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"==> Parameters: 1(String)"}
{"@timestamp":"2025-07-25T06:39:04.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.MbPzbMapper.selectByExample","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-25T06:39:04.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_mzdmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:04.409Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:04.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 58"}
{"@timestamp":"2025-07-25T06:39:05.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from gj_sfzjdmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:05.071Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:05.078Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 12"}
{"@timestamp":"2025-07-25T06:39:05.219Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_kskmdmb order by XM_DM,KM_DM asc "}
{"@timestamp":"2025-07-25T06:39:05.219Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:05.229Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 106"}
{"@timestamp":"2025-07-25T06:39:06.378Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"Thread-25","class":"o.s.c.n.e.s.EurekaServiceRegistry","rest":"Unregistering application EEIP-STANDALONE-SERVICE with eureka with status DOWN"}
{"@timestamp":"2025-07-25T06:39:06.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwlxmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:06.407Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:06.415Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-25T06:39:06.457Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_rygwzzmb order by SSGWM,DM asc "}
{"@timestamp":"2025-07-25T06:39:06.458Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:06.465Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 5"}
{"@timestamp":"2025-07-25T06:39:06.529Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjlxmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:06.530Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:06.536Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 3"}
{"@timestamp":"2025-07-25T06:39:06.585Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_wjxmmb order by LX_DM,XM_DM asc "}
{"@timestamp":"2025-07-25T06:39:06.585Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:06.594Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 20"}
{"@timestamp":"2025-07-25T06:39:06.830Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zcqswzdmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:06.830Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:06.838Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 4"}
{"@timestamp":"2025-07-25T06:39:06.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwbjfsdmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:06.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:06.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 8"}
{"@timestamp":"2025-07-25T06:39:06.996Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==>  Preparing: SELECT * from jy_zwplfsdmb order by DM asc "}
{"@timestamp":"2025-07-25T06:39:06.997Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.003Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.MbxxsjMapper.queryByTableName","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-25T06:39:07.033Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####码表数据缓存初始化成功####"}
{"@timestamp":"2025-07-25T06:39:07.033Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化####"}
{"@timestamp":"2025-07-25T06:39:07.034Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"o.s.s.concurrent.ThreadPoolTaskExecutor","rest":"Initializing ExecutorService"}
{"@timestamp":"2025-07-25T06:39:07.035Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####延迟队列初始化成功####"}
{"@timestamp":"2025-07-25T06:39:07.035Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化####"}
{"@timestamp":"2025-07-25T06:39:07.041Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==>  Preparing: UPDATE ks_kssj_distribute_task SET complete = ?,complete_time = ? WHERE ( ( complete = ? ) ) "}
{"@timestamp":"2025-07-25T06:39:07.042Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"==> Parameters: 2(Integer), 2025-07-25 14:39:07.036(Timestamp), 1(Integer)"}
{"@timestamp":"2025-07-25T06:39:07.055Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.K.updateByExampleSelective","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.056Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据下发任务初始化成功####"}
{"@timestamp":"2025-07-25T06:39:07.056Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化####"}
{"@timestamp":"2025-07-25T06:39:07.056Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"dbName:[eeip_alone]"}
{"@timestamp":"2025-07-25T06:39:07.118Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.119Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), cjlx(String)"}
{"@timestamp":"2025-07-25T06:39:07.126Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.127Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), qydsxf(String)"}
{"@timestamp":"2025-07-25T06:39:07.134Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.135Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfsj(String)"}
{"@timestamp":"2025-07-25T06:39:07.141Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.142Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.142Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dsxfzt(String)"}
{"@timestamp":"2025-07-25T06:39:07.149Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.150Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.150Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkrybpsj(String)"}
{"@timestamp":"2025-07-25T06:39:07.157Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.157Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.158Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), pack_jkryjcsj(String)"}
{"@timestamp":"2025-07-25T06:39:07.164Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.164Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.164Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), jkqdrs(String)"}
{"@timestamp":"2025-07-25T06:39:07.171Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.171Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.171Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksjh(String), dbms(String)"}
{"@timestamp":"2025-07-25T06:39:07.177Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.178Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.178Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), jklsxh(String)"}
{"@timestamp":"2025-07-25T06:39:07.184Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.185Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.185Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), xsd(String)"}
{"@timestamp":"2025-07-25T06:39:07.191Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.192Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.192Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbzt(String)"}
{"@timestamp":"2025-07-25T06:39:07.199Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.199Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.200Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), sbsj(String)"}
{"@timestamp":"2025-07-25T06:39:07.206Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.207Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.207Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-25T06:39:07.214Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.214Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.214Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_jkry_rcxx(String), rgyzjg(String)"}
{"@timestamp":"2025-07-25T06:39:07.221Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.222Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `cs_dzgh` ( `id` varchar(32) NOT NULL, `cslx` varchar(32) DEFAULT NULL COMMENT 'KD(考点) KC(考场)', `type` varchar(32) DEFAULT NULL COMMENT '类型KCWG(考场网关)', `classify` varchar(32) DEFAULT NULL COMMENT '分类DEVCFG(设备配置)', `key` varchar(32) DEFAULT NULL COMMENT 'key值（IP:ip地址,NETMASK:掩码,GATEWAY:网关IP地址,IPRANGE:ip范围,PCNUM:主机数）', `value` varchar(128) DEFAULT NULL COMMENT 'value值', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `status` decimal(10,0) DEFAULT NULL COMMENT '1-启用 0-不启用', `sort` decimal(10,0) DEFAULT NULL COMMENT '排序', `jsh` varchar(32) DEFAULT NULL COMMENT '教室号', PRIMARY KEY (`id`), KEY `index_dzgh_1` (`jsh`,`value`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T06:39:07.222Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.235Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createCsDzgh","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.236Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_ipdfp` ( `id` varchar(32) NOT NULL, `ipstart` varchar(32) DEFAULT NULL COMMENT 'IP起', `ipend` varchar(32) DEFAULT NULL COMMENT 'IP尾', `zwym` varchar(32) DEFAULT NULL COMMENT '子网掩码', `wg` varchar(32) DEFAULT NULL COMMENT '网关', `create_time` datetime DEFAULT NULL, `update_time` datetime DEFAULT NULL, PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-25T06:39:07.236Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.249Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbIpdfp","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `sb_sbjcb` ( `id` varchar(32) NOT NULL, `sbxxbh` varchar(32) DEFAULT NULL COMMENT '设备信息编号', `ip` varchar(32) DEFAULT NULL COMMENT 'ip', `xlh` varchar(128) DEFAULT NULL COMMENT '序列号', `detect_type` varchar(10) DEFAULT NULL COMMENT '检测类型1-ping 2-mqtt', `detect_result` varchar(10) DEFAULT NULL COMMENT '检测结果1-成功 -1001-ping失败 -2001-mqtt超时 -9999-其他错误', `detect_desc` varchar(255) DEFAULT NULL COMMENT '检测描述', `detect_exception` text COMMENT '检测异常', `detect_time` timestamp NULL DEFAULT NULL COMMENT '检测时间', PRIMARY KEY (`id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 "}
{"@timestamp":"2025-07-25T06:39:07.250Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.262Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbSbjcb","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.264Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_bmxx_kstzz` ( `ksid` varchar(32) NOT NULL COMMENT '考生唯一标志号', `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划标志号', `ksh` varchar(20) DEFAULT NULL COMMENT '考生号', `sfzjhm` varchar(20) NOT NULL COMMENT '身份证件号', `kszpid` varchar(128) DEFAULT NULL COMMENT '考生照片ID', `tzzzt` varchar(2) DEFAULT NULL COMMENT '0-未获取到特征值；1-已获取到特征值；2-获取特征值失败', `tzzid` varchar(128) DEFAULT NULL COMMENT '特征值ID', `tzzhqsj` datetime DEFAULT NULL COMMENT '特征值获取时间', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `tzzfs` varchar(1) DEFAULT NULL COMMENT '特征值方式', `bmh` varchar(32) DEFAULT NULL COMMENT '报名号', PRIMARY KEY (`ksid`) USING BTREE ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='考生报名特征值' "}
{"@timestamp":"2025-07-25T06:39:07.264Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.276Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createKsBmxxKstzz","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.277Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==>  Preparing: CREATE TABLE IF NOT EXISTS `ks_ydsb_sbkszs` ( `id` varchar(32) NOT NULL, `ksjhbh` varchar(32) NOT NULL COMMENT '考试计划编号', `sbxlh` varchar(64) NOT NULL COMMENT '设备序列号', `sbsj` datetime DEFAULT NULL COMMENT '上报时间', `kdmc` varchar(128) DEFAULT NULL COMMENT '考点名称', `sbkszs` int(10) DEFAULT NULL COMMENT '上报考生总数=入场人数+缺考人数', `create_time` datetime DEFAULT NULL COMMENT '创建时间', `update_time` datetime DEFAULT NULL COMMENT '更新时间', `ccm` varchar(10) DEFAULT NULL COMMENT '场次码', `kcbh` varchar(32) DEFAULT NULL COMMENT '考场编号', `ljkcbh` varchar(32) DEFAULT NULL COMMENT '逻辑考场编号', `sblx` varchar(10) DEFAULT NULL COMMENT '上报类型 BZ-标准 FB-非标', PRIMARY KEY (`id`), KEY `ks_ydsb_sbkszs__index_1` (`ksjhbh`,`ccm`,`sbxlh`,`kcbh`,`ljkcbh`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T06:39:07.277Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.290Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createKsYdsbSbkszs","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.291Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==>  Preparing: create table if not exists biz_csxx_ext ( CSBH varchar(64) not null comment '场所编号' primary key, SBXLH varchar(128) null comment '设备序列号', YDZD_APP_VERSION varchar(128) null comment '移动终端app版本', ZHSBSJ datetime null comment '最后上报时间', XFZT int null comment '0-失败 1-下发中 2-成功' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T06:39:07.291Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.304Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizCsxxExt","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.304Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==>  Preparing: create table if not exists biz_fsgj_task ( ID varchar(32) not null primary key, NAME varchar(128) null comment '任务名称', T_TYPE varchar(10) null comment '任务类型 1-移动终端APP', T_PROGRESS varchar(10) null comment '进度', COMPLETE int(10) null comment '完成状态 -1-失败 0-开始 1-进行中 2-完成', T_PROGRESS_DESC varchar(255) null comment '任务描述', T_PARAM longtext null comment '任务参数' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T06:39:07.305Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.318Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTask","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.319Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==>  Preparing: create table if not exists biz_fsgj_task_jg ( ID varchar(32) not null primary key, TASK_ID varchar(32) null comment '任务id', CSBH varchar(64) null comment '场所编号', SBXLH varchar(64) null comment '设备序列号' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 "}
{"@timestamp":"2025-07-25T06:39:07.319Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.331Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.m.UtilsMapper.createBizFsgjTaskJg","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.333Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==>  Preparing: select table_name from information_schema.tables where table_schema= ? AND table_name in ( ? , ? ) "}
{"@timestamp":"2025-07-25T06:39:07.334Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"==> Parameters: eeip_alone(String), biz_send_command(String), biz_send_command_dev(String)"}
{"@timestamp":"2025-07-25T06:39:07.341Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.filterTables","rest":"<==      Total: 2"}
{"@timestamp":"2025-07-25T06:39:07.342Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.343Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), osbb(String)"}
{"@timestamp":"2025-07-25T06:39:07.350Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.351Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.351Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_enable(String)"}
{"@timestamp":"2025-07-25T06:39:07.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.357Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_source(String)"}
{"@timestamp":"2025-07-25T06:39:07.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.364Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), sb_sbxx(String), ntp_interval(String)"}
{"@timestamp":"2025-07-25T06:39:07.371Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==>  Preparing: create table if not exists sb_sbsj ( id varchar(32) charset utf8 not null primary key, sbxlh varchar(128) charset utf8 not null comment '设备序列号', sblx varchar(32) charset utf8 not null comment 'kdwg考点网关 kcwg考场网关 ydzd移动终端', event_type varchar(16) charset utf8 not null comment 'ONLINE-上线事件 USB-usb事件 WIFI-wifi事件 YDZDBATTERY-移动终端电池事件', event_time datetime not null comment '事件时间', csbh varchar(32) charset utf8 null comment '场所编号', event_detail text charset utf8 null comment '事件详情', create_time datetime null comment '创建时间', update_time datetime null comment '更新时间', status varchar(4) charset utf8 null comment '状态', `repeat` varchar(4) charset utf8 null comment '是否重复', repeat_count int(10) null comment '重复次数 ', `desc` text charset utf8 null comment '描述', param1 varchar(64) charset utf8 null comment '扩展1', param2 varchar(64) charset utf8 null comment '扩展2', param3 varchar(64) charset utf8 null comment '扩展3', event_time_start datetime null comment '事件开始时间', event_time_end datetime null comment '事件结束时间' ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备事件' "}
{"@timestamp":"2025-07-25T06:39:07.373Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:39:07.385Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.createSbsj","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.386Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), tbzt(String)"}
{"@timestamp":"2025-07-25T06:39:07.393Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==>  Preparing: ALTER TABLE ks_ksrcxx modify COLUMN tbzt varchar(2) COMMENT ?; "}
{"@timestamp":"2025-07-25T06:39:07.394Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"==> Parameters: 同步状态(String)"}
{"@timestamp":"2025-07-25T06:39:07.413Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.alterColumn","rest":"<==    Updates: 0"}
{"@timestamp":"2025-07-25T06:39:07.413Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.service.impl.UtilsServiceImpl","rest":"数据库dbName:[eeip_alone] tbName:[ks_ksrcxx] columName:[tbzt]变更"}
{"@timestamp":"2025-07-25T06:39:07.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.414Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), xsd(String)"}
{"@timestamp":"2025-07-25T06:39:07.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.422Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_sfzp(String)"}
{"@timestamp":"2025-07-25T06:39:07.428Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.428Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==>  Preparing: SELECT COUNT(1) FROM information_schema. COLUMNS WHERE table_schema = ? AND table_name = ? AND column_name= ? "}
{"@timestamp":"2025-07-25T06:39:07.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"==> Parameters: eeip_alone(String), ks_ksrcxx(String), sc_rlzp(String)"}
{"@timestamp":"2025-07-25T06:39:07.436Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.mapper.UtilsMapper.findColumnExist","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:39:07.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####数据库变更初始化成功####"}
{"@timestamp":"2025-07-25T06:39:07.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.x.i.c.IdentityverifyBusiCacheInitConfig","rest":"####缓存校级身份核验平台配置信息缓存成功####"}
{"@timestamp":"2025-07-25T06:39:07.436Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"28776","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SystemCache Succ<<<<<<<<<<<<<##"}
{"@timestamp":"2025-07-25T06:50:53.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34012","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$f9f7ce77] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:50:54.091Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34012","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Fetching config from server at : http://localhost:8888"}
{"@timestamp":"2025-07-25T06:50:56.234Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34012","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Connect Timeout Exception on Url - http://localhost:8888. Will be trying the next url if available"}
{"@timestamp":"2025-07-25T06:50:56.235Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34012","thread":"main","class":"o.s.c.c.c.ConfigServicePropertySourceLocator","rest":"Could not locate PropertySource: I/O error on GET request for \"http://localhost:8888/eeip-standalone-service/default\": Connection refused: connect; nested exception is java.net.ConnectException: Connection refused: connect"}
{"@timestamp":"2025-07-25T06:50:56.239Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34012","thread":"main","class":"c.x.standalone.StandaloneApplication","rest":"The following profiles are active: alone"}
