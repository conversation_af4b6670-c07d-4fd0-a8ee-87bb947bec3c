package com.xcwlkj.service;

import java.util.Map;

/**
 * 试卷跟踪缓存服务
 * 
 * <AUTHOR>
 * @version $Id: SjgzCacheOperateService.java, v 0.1 2021年3月22日 下午3:09:58 Xu Exp $
 */
public interface SjgzCacheOperateService {
    
    /**
     * 缓存查询
     * 
     * @param key
     * @return
     */
    Map<Object, Object> query(String key);
    
    /**
     * 更新缓存记录
     * 
     * @param key
     * @param value
     */
    void update(String key, Map<String, Object> value);
    
}
