package com.xcwlkj.model.enums;


/**
 * ipc格式码
 * <AUTHOR> <PERSON>
 * @date 2020/5/18 20:30
 */
public enum OsdIpcEnum {

	TXT("0","TXT"),
    TIME("1","TIME"),
    JSN("0","禁使能"),
    SN("1","使能"),
	;

    private String code;

    private String msg;


    /**
     * @param code 结果码
     * @param msg 描述信息
     */
    OsdIpcEnum(String code, String msg) {
        this.msg = msg;
        this.code = code;
    }

    public static boolean vildateCode(String code){
        OsdIpcEnum[] sjtbJcjgEnums = OsdIpcEnum.values();
        for (OsdIpcEnum sjtbJcjgEnum: sjtbJcjgEnums) {
            if (sjtbJcjgEnum.getCode().equals(code)){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    public String getMsg() {
        return msg;
    }

    public String getCode() {
        return code;
    }
}
