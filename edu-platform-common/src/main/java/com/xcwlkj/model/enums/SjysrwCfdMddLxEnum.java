package com.xcwlkj.model.enums;

import com.xcwlkj.base.exception.BusinessException;


/**
 * 试卷运送任务出发地/目的地类型枚举
 * 
 * <AUTHOR>
 * @version $Id: SjysrwztEnum.java, v 0.1 2020年4月20日 下午2:48:20 Xu Exp $
 */
public enum SjysrwCfdMddLxEnum {
	
    BMS("bms","保密室"),
	BZHKD("bzhkd","标准化考点"),
    YSC("ysc","印刷厂"),
    SMD("smd","扫描点");
    

	private String code;
	private String desc;
	
	private SjysrwCfdMddLxEnum(String code, String desc) {
		this.code = code;
		this.desc = desc;
	}
	
	public String getCode() {
		return this.code;
	}
	
	public String getDesc() {
		return this.desc;
	}
	
	public static SjysrwCfdMddLxEnum get(String code) {
		for (SjysrwCfdMddLxEnum c : values()) {
			if (c.getCode().equals(code.toUpperCase())) {
				return c;
			}
		}
		throw new BusinessException("无此枚举，枚举值："+code);
	}
}
