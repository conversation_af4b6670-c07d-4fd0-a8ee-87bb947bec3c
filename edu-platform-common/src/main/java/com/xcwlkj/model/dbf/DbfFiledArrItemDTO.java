/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.model.dbf;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 输出dbf文件dto
 * <AUTHOR>
 * @version $Id: DbfFiledArrItemDTO.java, v 0.1 2020年03月18日 10时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DbfFiledArrItemDTO  implements Serializable {
    
    /** 序列ID */
    private static final long serialVersionUID = 1L;
    
    /** 字段名称 */
    @NotBlank(message = "字段名称不能为空")
    private String filedName;
    /** 长度 */
    @NotNull(message = "长度不能为空")
    private Integer length;

}