/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.sbjc.model.vo.sbjcquery;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 设备状态检测vo
 * <AUTHOR>
 * @version $Id: DevDetectQueryItemVO.java, v 0.1 2023年04月23日 18时46分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DevDetectQueryItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    private String key;
    /** 数据库id */
    private String id;
    /** 1-成功 非1 都为失败 */
    private String result;
    /** 检测描述 */
    private String detectDesc;
    /** 检测异常 */
    private String detectException;
    /** 检测时间 */
    private String detectTime;

}
