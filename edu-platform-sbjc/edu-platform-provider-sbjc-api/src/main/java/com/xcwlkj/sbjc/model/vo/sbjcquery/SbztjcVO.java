/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.sbjc.model.vo.sbjcquery;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 设备状态检测vo
 * <AUTHOR>
 * @version $Id: SbztjcVO.java, v 0.1 2023年04月23日 18时46分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbztjcVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    private List<DevDetectQueryItemVO> devDetectQueryList;

}
