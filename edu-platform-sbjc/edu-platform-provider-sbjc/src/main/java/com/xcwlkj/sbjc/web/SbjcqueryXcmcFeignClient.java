/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.sbjc.web;

import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.sbjc.model.dto.sbjcquery.SblxztCLDTO;
import com.xcwlkj.sbjc.model.dto.sbjcquery.SbztjcDTO;
import com.xcwlkj.sbjc.model.vo.sbjcquery.DevDetectQueryItemVO;
import com.xcwlkj.sbjc.model.vo.sbjcquery.SbztjcVO;
import com.xcwlkj.sbjc.service.DevDetectService;
import com.xcwlkj.sbjc.service.SbjcqueryXcmcFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Sbjcquery接口
 * <AUTHOR>
 * @version $Id: SbjcqueryXcmcFeignClient.java, v 0.1 2023年04月23日 18时46分 xcwlkj.com Exp $
 */
@RestController
public class SbjcqueryXcmcFeignClient extends BaseFeignClient implements SbjcqueryXcmcFeignApi{


	@Resource
	private DevDetectService devDetectService;
    
    /** 
     * @see com.xcwlkj.sbjc.service.SbjcqueryXcmcFeignApi#sbztjc(com.xcwlkj.sbjc.model.dto.sbjcquery.SbztjcDTO)
     */
	@Override
	public Wrapper<SbztjcVO> sbztjc(@RequestBody @Validated SbztjcDTO dto) {
		logger.info("设备状态检测SbztjcDTO={}", dto);
		long start = System.currentTimeMillis();
		SbztjcVO result = new SbztjcVO();
		List<DevDetectQueryItemVO> itemVOList = devDetectService.queryByDevList(dto);
		result.setDevDetectQueryList(itemVOList);
		long cost = System.currentTimeMillis()-start;
		logger.info("sbztjc - 设备状态检测. [OK] SbztjcVO={} 花费=[{}]ms", result,cost);
		return WrapMapper.ok(result);
	}


    /** 
     * @see com.xcwlkj.sbjc.service.SbjcqueryXcmcFeignApi#sblxztCL(com.xcwlkj.sbjc.model.dto.sbjcquery.SblxztCLDTO)
     */
	@Override
	public Wrapper<Void> sblxztCL(@RequestBody @Validated SblxztCLDTO dto) {
		logger.info("设备离线状态处理SblxztCLDTO={}", dto);
		devDetectService.deleteExpireData();
		long start = System.currentTimeMillis();
		devDetectService.queryOffLineWgDevAndSaveAndUpdateSbxxb();
		long cost = System.currentTimeMillis()-start;
		logger.info("sblxztCL - 设备离线状态处理. 花费=[{}]ms [OK] ",cost);
		return WrapMapper.ok();
	}
}



