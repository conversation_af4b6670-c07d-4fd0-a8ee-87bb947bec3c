package com.xcwlkj.sbjc.config;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

@Configuration
@MapperScan(basePackages = "com.xcwlkj.sbjc.mapper.ksyw", sqlSessionTemplateRef = "ksywSqlSessionTemplate")
public class KsywDataSourceConfig {
    
    @Bean(name = "ksywSqlSessionFactory")
    public SqlSessionFactory testSqlSessionFactory(@Qualifier("ksywDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "ksywTransactionManager")
    public DataSourceTransactionManager testTransactionsManager(@Qualifier("ksywDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean(name = "ksywSqlSessionTemplate")
    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("ksywSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }
}
