/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjsb.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.xcwlkj.sjsb.model.domain.MbZdxxb;

import tk.mybatis.mapper.entity.Example;



/**
 * 码表_字段信息表服务
 * <AUTHOR>
 * @version $Id: MbZdxxbService.java, v 0.1 2021年02月02日 13时57分 xcwlkj.com Exp $
 */
@Service
public interface MbZdxxbService  {

	List<MbZdxxb> selectByExample(Example example);
	
	int updateByExampleSelective(MbZdxxb record, Example example);
	
	int insertSelective(MbZdxxb record);
	
	int deleteByExample(Example example);
	
	void batchInsert(List<String> zdywmcList,String mbywmc);
}