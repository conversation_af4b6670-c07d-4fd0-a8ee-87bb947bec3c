/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjsb.model.vo.sjsb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 8.业务表信息查询vo
 * <AUTHOR>
 * @version $Id: YwbxxLbItemVO.java, v 0.1 2021年02月05日 19时47分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class YwbxxLbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 业务名称 */
    private String ywmc;
    /** 业务表中文名称 */
    private String ywbzwmc;
    /** 业务表英文名称 */
    private String ywbywmc;
    /** 启用状态 0未启用/1已启用 */
    private String qyzt;
    /** 建表时间 */
    private String jbsj;
    /** 业务类型简介 */
    private String ywlxjj;

}
