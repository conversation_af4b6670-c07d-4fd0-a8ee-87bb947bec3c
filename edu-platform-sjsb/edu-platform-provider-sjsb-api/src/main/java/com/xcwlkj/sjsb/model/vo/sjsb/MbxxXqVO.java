/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjsb.model.vo.sjsb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.List;


/**
 * 3.码表信息详情vo
 * <AUTHOR>
 * @version $Id: MbxxXqVO.java, v 0.1 2021年02月26日 11时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MbxxXqVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 码表字段名称列表 */
    private List<MbzdmcLbItemVO> mbzdmcLb;
    /** 码表行数据列表 */
    private List<HashMap<String, String>> mbHsjLb;
    /** 数据总条数 */
    private Integer totalRows;
    /** 码表英文名称 */
    private String mbywmc;
    /** 码表中文名称 */
    private String mbzwmc;

}
