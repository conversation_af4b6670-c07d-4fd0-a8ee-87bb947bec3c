/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.sjsb.config;

import static org.springframework.beans.factory.config.BeanDefinition.SCOPE_PROTOTYPE;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignAutoConfiguration;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Scope;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;

import feign.Feign;
import feign.Logger;
import feign.codec.Decoder;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import okhttp3.ConnectionPool;

/**
 * 默认情况下，Feign采用的是JDK的HttpURLConnection,所以整体性能并不高，使用okhttp3来做客户端连接
 * json 使用fastjson
 * <AUTHOR>
 * @version id:SjsbFeignClientMSConfig.java, v 0.1 2018-02-07 18:05 danfeng.zhou Exp $$
 */
@Configuration
@ConditionalOnClass(Feign.class)
@AutoConfigureBefore(FeignAutoConfiguration.class)
public class SjsbFeignClientMSConfig {
    
    /** 默认读超时值 */
    public static final long       CLIENT_READ_TIMEOUT_VALUE    = 60;
    /** 默认读超时单位 */
    public static final TimeUnit   CLIENT_READ_TIMEOUT_UNIT     = TimeUnit.SECONDS;
    /** 默认连接超时值 */
    public static final long       CLIENT_CONNECT_TIMEOUT_VALUE = 60;
    /** 默认连接超时单位 */
    public static final TimeUnit   CLIENT_CONNECT_TIMEOUT_UNIT  = TimeUnit.SECONDS;
    /** 默认写超时 */
    public static final long       CLIENT_WRITE_TIMEOUT_VALUE   = 120;
    /** 默认写超时单位 */
    public static final TimeUnit   CLIENT_WRITE_TIMEOUT_UNIT    = TimeUnit.SECONDS;
    
    /**
     * OkHttpClient客户端连接配置
     * @return
     */
    @Bean
    public okhttp3.OkHttpClient okHttpClient() {
      return new okhttp3.OkHttpClient.Builder()
          .readTimeout(CLIENT_READ_TIMEOUT_VALUE, CLIENT_READ_TIMEOUT_UNIT)
          .connectTimeout(CLIENT_CONNECT_TIMEOUT_VALUE, CLIENT_CONNECT_TIMEOUT_UNIT)
          .writeTimeout(CLIENT_WRITE_TIMEOUT_VALUE, CLIENT_WRITE_TIMEOUT_UNIT)
          .connectionPool(new ConnectionPool())
          .build();
    }
    
    @Bean
    public Decoder feignDecoder() {
      return new ResponseEntityDecoder(new SpringDecoder(feignHttpMessageConverter()));
    }

    @Bean
    @Primary
    @Scope(SCOPE_PROTOTYPE)
    public Encoder feignEncoder() {
        return new SpringFormEncoder(new SpringEncoder(feignHttpMessageConverter()));
    }
    
    /**
     * feign http消息转换器
     * @return
     */
    private ObjectFactory<HttpMessageConverters> feignHttpMessageConverter() {
        final HttpMessageConverters httpMessageConverters =
                new HttpMessageConverters(getFastJsonConverter());
        return () -> httpMessageConverters;
    }
    
    /**
     * fastJson http消息转换器
     * 
     * @return
     */
    private FastJsonHttpMessageConverter getFastJsonConverter() {
        FastJsonHttpMessageConverter converter = 
                new FastJsonHttpMessageConverter();
        List<MediaType> supportedMediaTypes = new ArrayList<MediaType>();
        MediaType mediaTypeJson = 
                MediaType.valueOf(MediaType.APPLICATION_JSON_UTF8_VALUE);
        supportedMediaTypes.add(mediaTypeJson);
        converter.setSupportedMediaTypes(supportedMediaTypes);
        FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(
                SerializerFeature.DisableCircularReferenceDetect);
        converter.setFastJsonConfig(config);
        return converter;
    }

    @Bean
    public Logger.Level feignLogger() {
        return Logger.Level.FULL;
    }
}
