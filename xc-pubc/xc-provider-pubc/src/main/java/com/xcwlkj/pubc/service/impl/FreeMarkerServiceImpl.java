/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.service.impl;

import java.io.IOException;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import com.xcwlkj.pubc.service.FreeMarkerService;
import com.xcwlkj.util.ObjectUtil;
import com.google.common.base.Preconditions;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;

/**
 * 
 * <AUTHOR>
 * @version $Id: FreeMarkerServiceImpl.java, v 0.1 2018年8月28日 下午5:54:26 danfeng.zhou Exp $
 */
@Service
public class FreeMarkerServiceImpl implements FreeMarkerService {

    @Resource
    private Configuration configuration;

    @Override
    public String getTemplate(Map<String, Object> map, String templateLocation) throws IOException, TemplateException {
        Preconditions.checkArgument(ObjectUtil.isNotEmpty(templateLocation), "模板不能为空");
        Template t = configuration.getTemplate(templateLocation, "UTF-8");
        return FreeMarkerTemplateUtils.processTemplateIntoString(t, map);
    }
}
