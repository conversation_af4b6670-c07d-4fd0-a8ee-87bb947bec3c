package com.xcwlkj.pubc.service;

import com.xcwlkj.pubc.model.dto.mlog.RuleListDTO;
import com.xcwlkj.pubc.model.dto.mlog.RuleAddDTO;
import com.xcwlkj.pubc.model.dto.mlog.RuleDeleteDTO;
import com.xcwlkj.pubc.model.dto.mlog.ListDTO;
import com.xcwlkj.pubc.model.vo.mlog.ListVO;
import com.xcwlkj.pubc.model.dto.mlog.RuleEditDTO;
import com.xcwlkj.pubc.model.dto.mlog.DetailDTO;
import com.xcwlkj.pubc.model.vo.mlog.DetailVO;
import com.xcwlkj.pubc.model.dto.mlog.NoteDTO;
import com.xcwlkj.pubc.model.vo.mlog.NoteVO;
import com.xcwlkj.pubc.model.vo.mlog.RuleListVO;

public interface MLogService {
	/**
	 * 日志触发规则列表查询
	 * @param dto
	 * @return
	 */
	RuleListVO ruleList(RuleListDTO dto);
	/**
	 * 日志触发规则添加
	 * @param dto
	 * @return
	 */
	void ruleAdd(RuleAddDTO dto);
	/**
	 * 日志触发规则删除
	 * @param dto
	 * @return
	 */
	void ruleDelete(RuleDeleteDTO dto);
	/**
	 * 管理台操作日志查询
	 * @param dto
	 * @return
	 */
	ListVO logList(ListDTO dto);
	/**
	 * 日志触发规则编辑
	 * @param dto
	 * @return
	 */
	void ruleEdit(RuleEditDTO dto);
	/**
	 * 日志触发规则详情查询
	 * @param dto
	 * @return
	 */
	DetailVO detail(DetailDTO dto);
	/**
	 * 管理台操作日志详情
	 * @param dto
	 * @return
	 */
	NoteVO note(NoteDTO dto);
}
