/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.pubc.facade.manager;

import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.console.model.dto.user.*;
import com.xcwlkj.console.model.req.uac.ResetUserPwdReqModel;
import com.xcwlkj.console.model.req.uac.*;
import com.xcwlkj.console.model.resp.uac.ResetUserPwdRespModel;
import com.xcwlkj.console.model.resp.uac.*;
import com.xcwlkj.console.model.vo.ssoAuth.LoginVO;
import com.xcwlkj.console.model.vo.ssoAuth.QueryUserMangerListVO;
import com.xcwlkj.console.model.vo.ssoAuth.UserDetailVO;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.model.enums.ChannelEnum;
import com.xcwlkj.pubc.model.dto.user.LogoutDTO;
import com.xcwlkj.pubc.model.dto.user.OpenAccountDTO;
import com.xcwlkj.pubc.model.dto.user.UserExportDTO;
import com.xcwlkj.pubc.model.enums.UserTypeEnum;
import com.xcwlkj.pubc.model.req.user.*;
import com.xcwlkj.pubc.model.resp.user.*;
import com.xcwlkj.pubc.model.vo.user.UserExportVO;
import com.xcwlkj.pubc.model.vo.user.UserImportVO;
import com.xcwlkj.pubc.service.SsoService;
import com.xcwlkj.pubc.service.UserService;
import com.xcwlkj.pubc.util.VerifyUtil;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.UUIDUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import javax.validation.Valid;

/**
 * 
 * <AUTHOR>
 * @version $Id: UserController.java, v 0.1 2018年11月05日 上午11:05:12 xcwlkj.com
 *          Exp $
 */
@Slf4j
@RestController
@RequestMapping(value = "/manager/pubc", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class MUserController extends BaseController {

    @Resource
    private UserService userService;
    @Resource
	private SsoService ssoService;
	@Resource
	private VerifyUtil verifyUtil;
	@Resource
	private RedisUtil redisUtil;

    @Value("${user.defaultPassword}")
	private String defaultPassword;

	public static String CONST_CAS_USERNAME = "const_cas_username";

	/**
	 * 用户表查询列表
	 * 
	 * @param reqModel
	 * @return PageInfo
	 */
	@Permission("user:queryUserListWithPage")
	@PostMapping(value = "/user/queryUserListWithPage")
	public Wrapper<QueryUserListWithPageRespModel> queryUserListWithPage(
			@RequestBody QueryUserListWithPageReqModel reqModel) {
		logger.info("用户表查询列表 QueryUserListWithPageReqModel={}", reqModel);
		QueryUserListWithPageRespModel respModel = new QueryUserListWithPageRespModel();
		QueryUserDTO userDto = new QueryUserDTO();
		userDto.setPageNum(reqModel.getPageNum());
		userDto.setPageSize(reqModel.getPageSize());
		userDto.setDepartmentId(reqModel.getDepartmentId());
		userDto.setUserName(reqModel.getUserName());
		userDto.setMobile(reqModel.getMobile());
		QueryUserMangerListVO result = userService.queryUserrListWithPage(userDto);
		respModel.setUserManagerSummaryVOList(result.getUserSummaryVOList());
		respModel.setTotalRows(result.getTotalRows());
		logger.info("用户表查询列表 QueryUserListWithPageRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 根据用户id号查询用户的详情
	 * 
	 * @param reqModel
	 * @return Integer
	 */
	@PostMapping(value = "/user/queryUserDetailById")
	public Wrapper<QueryUserDetailByIdRespModel> queryUserDetailById(
			@RequestBody QueryUserDetailByIdReqModel reqModel) {
		logger.info("根据用户id号查询用户的详情 QueryUserDetailByIdReqModel={}", reqModel);
		QueryUserDetailByIdRespModel respModel = new QueryUserDetailByIdRespModel();
		UserDetailVO result = userService.queryUserDetailById(reqModel.getUserId());
		respModel.setUserDetailVO(result);
		logger.info("根据用户id号查询用户的详情 QueryUserDetailByIdRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 用户表新增
	 * 
	 * @param reqModel
	 * @return Integer
	 */
	@Permission("user:saveUser")
	@PostMapping(value = "/user/saveUser")
	public Wrapper<SaveUserRespModel> saveUser(@Valid @RequestBody SaveUserReqModel reqModel) {
		logger.info("用户表新增 SaveUserReqModel={}", reqModel);
		SaveUserRespModel respModel = new SaveUserRespModel();
		if (reqModel.getId() == null) {
			SaveUserDTO userDto = new SaveUserDTO();
			BeanUtil.copyProperties(reqModel, userDto);
			// 为空时默认对用户类型和工号赋值
			if(userDto.getUserType() == null){
				userDto.setUserType(UserTypeEnum.TEACHER.getCode());// 默认为教师
			}
			if(StringUtils.isEmpty(userDto.getGh()) || "0".equals(userDto.getGh())){
				userDto.setGh(reqModel.getUserName());// 默认为登录名
			}
			userService.saveUser(userDto);
		} else {
			ModifyUserDTO modifyUserDto = new ModifyUserDTO();
			BeanUtil.copyProperties(reqModel, modifyUserDto);
			modifyUserDto.setId(reqModel.getId());
			userService.modifyUserById(modifyUserDto);
		}
		logger.info("用户表新增 SaveUserRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 重置默认密码
	 * 
	 * @return Integer
	 */
	@Permission("user:resetUserDefaultPwd")
	@PostMapping(value = "/user/resetUserDefaultPwd")
	public Wrapper<ResetUserDefaultPwdRespModel> resetUserDefaultPwd(
			@RequestBody ResetUserDefaultPwdReqModel reqModel) {
		logger.info("重置默认密码 ResetUserDefaultPwdReqModel={}", reqModel);
		ResetUserDefaultPwdRespModel respModel = new ResetUserDefaultPwdRespModel();
		ResetPwdByUserIdDTO resetPwdByUserIdDTO = new ResetPwdByUserIdDTO();
		resetPwdByUserIdDTO.setUserId(reqModel.getUserId());
		logger.info("重置为默认密码 defaultPassword="+defaultPassword);
		resetPwdByUserIdDTO.setNewPassword(defaultPassword);
		userService.resetPwdByUserId(resetPwdByUserIdDTO);
		logger.info("重置默认密码 ResetUserDefaultPwdRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 用户修改密码
	 * 
	 * @return Integer
	 */
//	@Permission("system:user")
	@PostMapping(value = "/user/resetUserPwd")
	public Wrapper<ResetUserPwdRespModel> resetUserPwd(@RequestBody ResetUserPwdReqModel reqModel) {
		logger.info("用户修改密码 ResetUserPwdReqModel={}", reqModel);
		ResetUserPwdRespModel respModel = new ResetUserPwdRespModel();
		ResetPwdDTO resetPwdDTO = new ResetPwdDTO();
		BeanUtil.copyProperties(reqModel, resetPwdDTO);
		userService.resetPwd(resetPwdDTO);
		logger.info("用户修改密码 ResetUserPwdRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 用户表删除
	 * 
	 * @param reqModel
	 * @return Integer
	 */
	@Permission("user:deleteUserById")
	@PostMapping(value = "/user/deleteUserById")
	public Wrapper<DeleteUserByIdRespModel> deleteUserById(@RequestBody DeleteUserByIdReqModel reqModel) {
		logger.info("用户表删除 DeleteUserByIdReqModel={}", reqModel);
		DeleteUserByIdRespModel respModel = new DeleteUserByIdRespModel();
		userService.deleteUserById(reqModel.getUserIds());
		logger.info("用户表删除 DeleteUserByIdRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 验证码获取
	 *
	 * @return
	 */
	@PostMapping(value = "/user/verifyCode")
	public Wrapper<VerifyCodeRespModel> generarte(@RequestBody VerifyCodeReqModel reqModel) {
		logger.info("验证码获取 VerifyCodeReqModel={}",reqModel);
		VerifyCodeRespModel respModel = new VerifyCodeRespModel();
		String code = verifyUtil.generate(4);
		String uuid = UUIDUtil.getUUID();
		redisUtil.set(uuid,code);
		respModel.setCode(code);
		respModel.setUuid(uuid);
		logger.info("验证码获取 VerifyCodeRespModel={}", respModel);
		return WrapMapper.ok(reqModel,respModel);
	}
	
	/**
	 * 用户登录
	 * 
	 * @param reqModel
	 * @return
	 */
	@PostMapping(value = "/user/login")
	public Wrapper<ManagerUserLoginRespModel> login(HttpServletRequest request, HttpServletResponse response,
                                                    @Validated @RequestBody ManagerUserLoginReqModel reqModel) {
		logger.info("用户登录 ManagerUserLoginReqModel={}", reqModel);
		// valid login
		LoginDTO loginDto = new LoginDTO();
		loginDto.setUserName(reqModel.getUserName());
		loginDto.setPassword(reqModel.getPassword());
		loginDto.setChannel(reqModel.getTransChannel());
		loginDto.setVerifyCode(reqModel.getVerifyCode());
		loginDto.setUuid(reqModel.getUuid());
		LoginVO loginVO = userService.login(loginDto);
		ManagerUserLoginRespModel respModel = new ManagerUserLoginRespModel();
		// 4、return sessionId
		respModel.setSessionId(loginVO.getSessionId());
		respModel.setRoleId(loginVO.getCurrRoleId());
		respModel.setRealName(loginVO.getRealName());
		logger.info("用户登录 ManagerUserLoginRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 修改用户状态
	 * 
	 * @param reqModel
	 * @return
	 */
	@Permission("user:modifyUserStatus")
	@PostMapping(value = "/user/modifyUserStatus")
	public Wrapper<ModifyUserStatusRespModel> modifyUserStatus(@RequestBody ModifyUserStatusReqModel reqModel) {
		logger.info("修改用户状态 ModifyUserStatusReqModel={}", reqModel);
		ModifyUserStatusRespModel respModel = new ModifyUserStatusRespModel();
		ModifyUserDTO modifyUserDto = new ModifyUserDTO();
		modifyUserDto.setId(reqModel.getUserId());
		modifyUserDto.setAccountNonLocked(reqModel.getAccountNonLocked());
		userService.modifyUserProfile(modifyUserDto);
		logger.info("修改用户状态 ModifyUserStatusRespModel={}", respModel);
		return WrapMapper.ok(reqModel, respModel);
	}
	
	   /**
    * 退出登录
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/user/logout")
    public Wrapper<LogoutRespModel> logout(@RequestBody LogoutReqModel reqModel) {
		logger.info("收到请求开始：[退出登录][/manager/pubc/user/logout]reqModel:"+reqModel.toString());
		LogoutDTO dto = new LogoutDTO();

        userService.logout(dto);
        LogoutRespModel respModel = new LogoutRespModel();

		logger.info("处理请求结束：[退出登录][/manager/pubc/user/logout]reqModel:"+ reqModel
			+",respModel:"+ respModel);
        return WrapMapper.ok(reqModel, respModel);		
    }
   /**
    * 学生和老师开通账户
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/user/openAccount")
    public Wrapper<OpenAccountRespModel> openAccount(@Validated @RequestBody OpenAccountReqModel reqModel) {
		log.info("收到请求开始：[学生和老师开通账户][/manager/pubc/user/openAccount]reqModel:"+reqModel.toString());
		OpenAccountDTO dto = new OpenAccountDTO();
        BeanUtil.copyProperties(reqModel, dto);
        userService.openAccount(dto);
        OpenAccountRespModel respModel = new OpenAccountRespModel();

		log.info("处理请求结束：[学生和老师开通账户][/manager/pubc/user/openAccount]reqModel:"+ reqModel
			+",respModel:"+ respModel);
        return WrapMapper.ok(reqModel, respModel);		
    }

	/**
	 * 单点登录
	 * @param reqModel
	 * @return
	 */
	@PostMapping(value = "/user/ssoLogin")
	public Wrapper<SsoLoginRespModel> ssoLogin(@Validated @RequestBody SsoLoginReqModel reqModel) {
		log.info("收到请求开始：[单点登录][/manager/pubc/user/ssoLogin]reqModel:"+reqModel.toString());

		LoginVO login = ssoService.login(reqModel.getUsername(), reqModel.getPassword());
		SsoLoginRespModel respModel = new SsoLoginRespModel();
		respModel.setSessionId(login.getSessionId());
		respModel.setRealName(login.getRealName());
		respModel.setCurrRoleId(login.getCurrRoleId());
		respModel.setGh(login.getGh());

		log.info("处理请求结束：[单点登录][/manager/pubc/user/ssoLogin]reqModel:"+ reqModel
				+",respModel:"+ respModel);
		return WrapMapper.ok(reqModel, respModel);
	}

	/**
	 * 获取初始密码
	 * @param reqModel
	 * @return
	 */
	@Permission("user:getDefaultPassword")
	@PostMapping(value = "/user/getDefaultPassword")
	public Wrapper<GetDefaultPasswordRespModel> getDefaultPassword(@Validated @RequestBody GetDefaultPasswordReqModel reqModel) {
		log.info("收到请求开始：[获取初始密码][/manager/pubc/user/getDefaultPassword]reqModel:"+reqModel.toString());

		GetDefaultPasswordRespModel respModel = new GetDefaultPasswordRespModel();
		respModel.setDefaultPassword(defaultPassword);

		log.info("处理请求结束：[获取初始密码][/manager/pubc/user/getDefaultPassword]reqModel:"+ reqModel
				+",respModel:"+ respModel);
		return WrapMapper.ok(reqModel, respModel);
	}


	@GetMapping("/casTicketCheck")
	@Deprecated
	public Wrapper<LoginVO> casTicketCheck(@RequestParam(value = "ticket", required = false) String ticket, HttpServletRequest request){

		log.info("casTicketCheck...");
		LoginVO result = new LoginVO();
		HttpSession session = request.getSession(false);

		String username = (session != null) ? (String) session.getAttribute(CONST_CAS_USERNAME):null;

		result = userService.loginNoPassword(username, 0, ChannelEnum.XCMC);
		result.setGh(username);

		log.info("casTicketCheck, {}", result);
		return WrapMapper.ok(result);
	}
    
    /**
     * 黄河交通学院SSO单点登录（PC端）
     *
     * @param reqModel
     * @return
     */
    @PostMapping(value = "/user/hhjtSsoLogin")
    public Wrapper<SsoLoginRespModel> hhjtSsoLogin(@Validated @RequestBody HhjtSsoLoginReqModel reqModel) {
        log.info("收到请求开始：[单点登录][/manager/pubc/user/hhjtSsoLogin:"+reqModel.toString());
        
        LoginVO login = ssoService.hhjtLogin(reqModel.getTicket());
        SsoLoginRespModel respModel = new SsoLoginRespModel();
        respModel.setSessionId(login.getSessionId());
        respModel.setRealName(login.getRealName());
        respModel.setCurrRoleId(login.getCurrRoleId());
        respModel.setGh(login.getGh());
        
        log.info("处理请求结束：[单点登录][/manager/pubc/user/hhjtSsoLogin]reqModel:"+ reqModel
                +",respModel:"+ respModel);
        return WrapMapper.ok(reqModel, respModel);
    }   /**
    * 用户导入
    * @return
    */
    @PostMapping(value = "/user/userImport", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Wrapper<UserImportRespModel> userImport(@RequestPart("file")MultipartFile file) {
		log.info("收到请求开始：[用户导入][/manager/pubc/user/userImport]");

		UserImportVO result = userService.userImport(file);
		UserImportRespModel respModel = new UserImportRespModel();
		respModel.setFailList(result.getFailList());

		log.info("处理请求结束：[用户导入][/manager/pubc/user/userImport]");
        return WrapMapper.ok(respModel);
    }
   /**
    * 用户导出
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/user/userExport")
    public Wrapper<UserExportRespModel> userExport(@Validated @RequestBody UserExportReqModel reqModel) {
		log.info("收到请求开始：[用户导出][/manager/pubc/user/userExport]reqModel:"+reqModel.toString());
		UserExportDTO dto = new UserExportDTO();
        dto.setUserName(reqModel.getUserName());
        dto.setMobile(reqModel.getMobile());
        dto.setDepartmentId(reqModel.getDepartmentId());
        UserExportVO result = userService.userExport(dto);
        UserExportRespModel respModel = new UserExportRespModel();
        respModel.setFilePath(result.getFilePath());
		log.info("处理请求结束：[用户导出][/manager/pubc/user/userExport]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);		
    }

}
