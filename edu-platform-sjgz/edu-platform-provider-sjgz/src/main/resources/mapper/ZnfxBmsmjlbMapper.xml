<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.sjgz.mapper.ZnfxBmsmjlbMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.sjgz.model.domain.ZnfxBmsmjlb">
        <id column="bmsbh" jdbcType="VARCHAR" property="bmsbh" />
        <id column="mjid" jdbcType="VARCHAR" property="mjid" />
        <id column="mjwz" jdbcType="VARCHAR" property="mjwz" />
        <result column="bmsmc" jdbcType="VARCHAR" property="bmsmc" />
        <result column="bmszxjd" jdbcType="VARCHAR" property="bmszxjd" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="creat_time" jdbcType="TIMESTAMP" property="creatTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        bmsbh,
        mjid,
        mjwz,
        bmsmc,
        bmszxjd,
        sczt,
        creat_time,
        update_time

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="bmsbh != null and bmsbh != ''">
            AND bmsbh = #{bmsbh,jdbcType=VARCHAR}
        </if>
        <if test="mjid != null and mjid != ''">
            AND mjid = #{mjid,jdbcType=VARCHAR}
        </if>
        <if test="mjwz != null and mjwz != ''">
            AND mjwz = #{mjwz,jdbcType=VARCHAR}
        </if>
        <if test="bmsmc != null and bmsmc != ''">
            AND bmsmc = #{bmsmc,jdbcType=VARCHAR}
        </if>
        <if test="bmszxjd != null and bmszxjd != ''">
            AND bmszxjd = #{bmszxjd,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="creatTime != null and creatTime != ''">
            AND creat_time = #{creatTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="bmsbh != null ">
            bmsbh = #{bmsbh,jdbcType=VARCHAR},
        </if>
        <if test="mjid != null ">
            mjid = #{mjid,jdbcType=VARCHAR},
        </if>
        <if test="mjwz != null ">
            mjwz = #{mjwz,jdbcType=VARCHAR},
        </if>
        <if test="bmsmc != null ">
            bmsmc = #{bmsmc,jdbcType=VARCHAR},
        </if>
        <if test="bmszxjd != null ">
            bmszxjd = #{bmszxjd,jdbcType=VARCHAR},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="creatTime != null ">
            creat_time = #{creatTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.sjgz.model.domain.ZnfxBmsmjlb"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from znfx_bmsmjlb
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
