/**
 * xcwlkj.com Inc.
 * Copyright (c) 2021-2031 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 非领卷时间段
 * 
 * <AUTHOR>
 * @version $Id: SjgzFljsjd.java, v 0.1 2021年05月30日 10时16分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "sjgz_fljsjd")
public class SjgzFljsjd implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 时间段编号 */
    @Id
    @Column(name = "sjdbh")
    private String            sjdbh;
    /** 开始时间 */
    @Column(name = "kssj")
    private Date            kssj;
    /** 结束时间 */
    @Column(name = "jssj")
    private Date            jssj;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


