/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.model.enums.SubSysEnum;
import com.xcwlkj.service.SjgzCacheOperateService;
import com.xcwlkj.sjgz.exceptions.SjgzBusiException;
import com.xcwlkj.sjgz.mapper.SjgzDzbqcspzMapper;
import com.xcwlkj.sjgz.model.domain.SjgzDzbqcspz;
import com.xcwlkj.sjgz.model.domain.SjgzSbsbxxjl;
import com.xcwlkj.sjgz.model.dos.DzbqpzydDO;
import com.xcwlkj.sjgz.model.dto.jrptcx.GetStatusDTO;
import com.xcwlkj.sjgz.model.dto.sbpz.DzbqItemDTO;
import com.xcwlkj.sjgz.model.dto.sbpz.PzdzbqcsDTO;
import com.xcwlkj.sjgz.model.enums.*;
import com.xcwlkj.sjgz.model.vo.jrptcx.GetStatusVO;
import com.xcwlkj.sjgz.service.SjgzDzbqcspzService;
import com.xcwlkj.sjgz.service.SjgzDzbqsbService;
import com.xcwlkj.sjgz.service.SjgzSbsbxxjlService;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 电子标签参数配置服务
 * <AUTHOR>
 * @version $Id: SjgzDzbqcspzServiceImpl.java, v 0.1 2020年11月27日 13时58分 xcwlkj.com Exp $
 */
@Service("sjgzDzbqcspzService")
public class SjgzDzbqcspzServiceImpl implements SjgzDzbqcspzService {

    @Resource
    private SjgzDzbqcspzMapper      modelMapper;

    @Resource
    private SjgzDzbqsbService       bqsbService;

    @Resource
    private SjgzCacheOperateService cacheService;

    @Resource
    private SjgzSbsbxxjlService     sbsbxxService;

    @Override
    public void setDzbqcs(PzdzbqcsDTO dto) {

        /** 20202-11-30 设置参数后不通知设备，等待设备查询 **/
        List<DzbqItemDTO> dzbqItemDTOs = dto.getDzbqList();
        for (DzbqItemDTO dzbqItemDTO : dzbqItemDTOs) {
            SjgzDzbqcspz dzbqcspz = modelMapper.selectByPrimaryKey(dzbqItemDTO.getDzbqbh());
            SjgzDzbqcspz xzDzbqcspz = new SjgzDzbqcspz();
            xzDzbqcspz.setDzbqbh(dzbqItemDTO.getDzbqbh());
            xzDzbqcspz.setId(dzbqItemDTO.getId());
            xzDzbqcspz.setMqttfwqip(dto.getMqttfwqip());
            xzDzbqcspz.setYdyz(dto.getYdyz());
            xzDzbqcspz.setCat1xwjg(dto.getCat1xwjg());
            xzDzbqcspz.setCat1kqhsbjg(dto.getCat1kqhsbjg());
            xzDzbqcspz.setLygbjg(dto.getLygbjg());
            xzDzbqcspz.setLyfsgl(dto.getLyfsgl());
            xzDzbqcspz.setAdcjcjg(dto.getAdcjcjg());
            xzDzbqcspz.setPzydjg(DzbqpzztEnum.DDHQPZ.getCode());
            if (BeanUtil.isEmpty(dzbqcspz)) {
                xzDzbqcspz.setCreateTime(DateUtil.getCurrentDT());
                modelMapper.insertSelective(xzDzbqcspz);
            } else {
                xzDzbqcspz.setUpdateTime(DateUtil.getCurrentDT());
                modelMapper.updateByPrimaryKeySelective(xzDzbqcspz);
            }
        }
    }

    @Override
    public void updateDzbqpzydjg(DzbqpzydDO dzbqpzydDO) {
        SjgzDzbqcspz dzbqcspz = new SjgzDzbqcspz();
        dzbqcspz.setUpdateTime(DateUtil.getCurrentDT());
        if (dzbqpzydDO.getOptCode().equals(SbsbxxsxsmEnum.PZCG.getCode())) {
            dzbqcspz.setPzydjg(DzbqpzztEnum.PZCG.getCode());
        } else if (dzbqpzydDO.getOptCode().equals(SbsbxxsxsmEnum.PZSB.getCode())) {
            dzbqcspz.setPzydjg(DzbqpzztEnum.PZSB.getCode());
        }
        Example example = new Example(SjgzDzbqcspz.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("xxid", dzbqpzydDO.getMsgId());
        criteria.andEqualTo("id", dzbqpzydDO.getTagId());
        modelMapper.updateByExampleSelective(dzbqcspz, example);

    }

    @Override
    public GetStatusVO cxbqzt(GetStatusDTO dto) {
        GetStatusVO vo = new GetStatusVO();
        if (!bqsbService.yzsbsfhf(dto.getTagID())) {
            throw new SjgzBusiException("不存在该设备！");
        }
        Date currentTime = DateUtil.getCurrentDT();

        String key = SubSysEnum.SJGZ.getCode() + ":" + SjgzCacheEnum.DZBQDL.getCode() + ":"
                     + dto.getTagID();
        Map<Object, Object> bqdlMap = cacheService.query(key);
        String bjm = "0";
        String gnssdl = "0";
        String lydl = "0";
        String sfbz = "T";
        String cat1mkzt = "0";
        if (!bqdlMap.isEmpty()) {
            bjm = bqdlMap.get(DzbqsbCacheEnum.BJM.getCode()).toString();
            gnssdl = bqdlMap.get(DzbqsbCacheEnum.GNSSDL.getCode()).toString();
            lydl = bqdlMap.get(DzbqsbCacheEnum.LYDL.getCode()).toString();
            sfbz = bqdlMap.get(DzbqsbCacheEnum.SFBZ.getCode()).toString();
            cat1mkzt = bqdlMap.get(DzbqsbCacheEnum.CAT1MKZT.getCode()).toString();
        }
        SjgzSbsbxxjl tag = new SjgzSbsbxxjl();
        tag.setXxjlbh(IdGenerateUtil.generateId());
        tag.setSbid(dto.getTagID());
        tag.setSblx(SbsbsblxEnum.DZBQSB.getCode());
        tag.setSd("");
        tag.setBjm(bjm);
        tag.setGnssdl(gnssdl);
        tag.setLydl(lydl);
        tag.setSfbz(sfbz);
        tag.setJrptsbsj(currentTime);
        tag.setSbcssjsj(currentTime);
        tag.setPtbcsjsj(currentTime);
        tag.setCat1mkzt(Integer.parseInt(cat1mkzt) + "");
        tag.setSfyxdw(ScztEnum.DEL.getCode());
        tag.setDwlx(SbdwlxEnum.GET.getCode());
        sbsbxxService.insert(tag);

        vo.setTrackType(dto.getTrackType());
        vo.setTagID(dto.getTagID());
        vo.setMessageID(dto.getMessageID());
        if (bqsbService.cxdzbqgpskqzt(dto.getTagID())) {
            vo.setStatus(SbsbxxsxsmEnum.KQZT.getCode());
        } else {
            vo.setStatus(SbsbxxsxsmEnum.GBZT.getCode());
        }
        //获取标签配置参数
        Example example = new Example(SjgzDzbqcspz.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", dto.getTagID());
        List<SjgzDzbqcspz> pzcsList = modelMapper.selectByExample(example);
        if (pzcsList.size() > 0
            && pzcsList.get(0).getPzydjg().equals(DzbqpzztEnum.DDHQPZ.getCode())) {
            SjgzDzbqcspz dzbqcspz = pzcsList.get(0);
            vo.setConfType(SbsbxxsxsmEnum.SETPZ.getCode());
            vo.setMqttIp(dzbqcspz.getMqttfwqip());
            vo.setMotionThreshold(dzbqcspz.getYdyz());
            vo.setCat1AskInterval(dzbqcspz.getCat1xwjg());
            vo.setCat1ReportInterval(dzbqcspz.getCat1kqhsbjg());
            vo.setBluetoothBroadcastInterval(dzbqcspz.getLygbjg());
            vo.setBluetoothTransmitPower(dzbqcspz.getLyfsgl());
            vo.setAdcDetectionInterval(dzbqcspz.getAdcjcjg());
            dzbqcspz.setXxid(dto.getMessageID());
            dzbqcspz.setPzydjg(DzbqpzztEnum.WXY.getCode());
            dzbqcspz.setUpdateTime(currentTime);
            modelMapper.updateByPrimaryKeySelective(dzbqcspz);
        }
        return vo;
    }

}