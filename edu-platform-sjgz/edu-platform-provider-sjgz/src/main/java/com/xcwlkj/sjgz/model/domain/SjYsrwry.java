/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * 运送任务人员
 * 
 * <AUTHOR>
 * @version $Id: SjYsrwry.java, v 0.1 2020年06月19日 17时03分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "sj_ysrwry")
public class SjYsrwry implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 任务人员编号 */
    @Id
    @Column(name = "rwrybh")
    private String            rwrybh;
    /** 姓名 */
    @Column(name = "ysxm")
    private String            ysxm;
    /** 手机号 */
    @Column(name = "yssjh")
    private String            yssjh;
    /** 岗位名 */
    @Column(name = "gwm")
    private String            gwm;
    /** 岗位编码 */
    @Column(name = "gwbm")
    private String            gwbm;
    /** 性别 */
    @Column(name = "csryxb")
    private String            csryxb;
    /** 照片 */
    @Column(name = "csryzp")
    private String            csryzp;
    /** 出生日期 */
    @Column(name = "csrycsrq")
    private String            csrycsrq;
    /** 身份证号 */
    @Column(name = "csrysfzh")
    private String            csrysfzh;
    /** 在岗情况 */
    @Column(name = "zgqk")
    private String            zgqk;
    /** 创建机构名称 */
    @Column(name = "cjjgmc")
    private String            cjjgmc;
    /** 创建机构编号 */
    @Column(name = "cjjgbh")
    private String            cjjgbh;
    /** 创建机构节点祖先 */
    @Column(name = "cjjgjdzx")
    private String            cjjgjdzx;
    /** 删除状态 */
    @Column(name = "sczt")
    private String            sczt;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


