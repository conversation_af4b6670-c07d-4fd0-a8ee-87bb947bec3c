package com.xcwlkj.sjgz.model.dos;


import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 电子标签参数配置DO
 * 
 * <AUTHOR>
 * @version $Id: DzbqcspzDO.java, v 0.1 2020年11月27日 下午2:38:41 Xu Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DzbqSbxxDo  implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 设备 */
    private String sbid;
    /** gnss电量 */
    private String gnssdl;
    /** 蓝牙电量 */
    private String lydl;
    /** 报警码 */
    private String bjm;
    /** cat1模块状态 */
    private String cat1mkzt;
    /** 速度 */
    private String sd;
    /** 是否有效定位 */
    private String sfyxdw;
    /** 经度 */
    private BigDecimal jd;
    /** 纬度 */
    private BigDecimal wd;
    /** 设备产生数据时间 */
    private String sbcssjsj;
    
}
