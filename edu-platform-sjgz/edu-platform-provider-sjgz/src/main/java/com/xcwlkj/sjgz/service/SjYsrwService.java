/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.xcwlkj.sjgz.model.domain.SjYsrw;

import tk.mybatis.mapper.entity.Example;

/**
 * 试卷运送任务服务
 * <AUTHOR>
 * @version $Id: SjYsrwService.java, v 0.1 2020年04月16日 17时48分 xcwlkj.com Exp $
 */
@Service
public interface SjYsrwService {

    /**
     * 试卷运送任务 添加
     * 
     * @param ysrw
     */
    void insert(SjYsrw ysrw);

    /**
     * 试卷运送任务 根据主键修改
     * 
     * @param ysrw
     */
    void updateByKey(SjYsrw ysrw);

    /**
     * 试卷运送任务 根据类型修改
     * 
     * @param ysrw
     * @param example
     */
    void updateByExample(SjYsrw ysrw, Example example);

    /**
     * 试卷运送任务 根据主键查询
     * 
     * @param ysrwbh
     * @return
     */
    SjYsrw selectByKey(String ysrwbh);

    /**
     * 试卷运送任务 根据类型查询
     * 
     * @param example
     * @return
     */
    List<SjYsrw> selectByExample(Example example);
}