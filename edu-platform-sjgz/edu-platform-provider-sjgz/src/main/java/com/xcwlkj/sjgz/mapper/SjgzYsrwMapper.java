/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.sjgz.model.domain.SjgzYsrw;



/**
 * 运送任务数据库操作
 * <AUTHOR>
 * @version $Id: InitSjgzYsrwMapper.java, v 0.1 2020年11月30日 17时09分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface SjgzYsrwMapper extends MyMapper<SjgzYsrw> {

    /**
	 * 分页查询运送任务
	 * 
	 * @param example
	 * @return
	 */
	List<SjgzYsrw> pageList(SjgzYsrw example);
	
	/**
	 * 根据机构编号查询
	 * 
	 * @param ksjhbh
	 * @param jgbh
	 * @param rwmc
	 * @param rwwczt
	 * @param inList 任务编号在列表中
	 * @param notInList 任务编号不在列表中
	 * @param ysjhbh
	 * @return
	 */
    List<SjgzYsrw> selectByJgbh(@Param("ksjhbh") String ksjhbh, @Param("jgbh") String jgbh,
                              @Param("rwmc") String rwmc, @Param("rwwczt") String rwwczt,
                              @Param("inList") List<String> inList, @Param("notInList") List<String> notInList,
                              @Param("ysjhbh") String ysjhbh);
    
    /**
     * 查询设备关联任务所属运送计划下所有任务
     * 
     * @param sbid
     * @param sblx
     * @return
     */
    List<SjgzYsrw> selectBySbgl(@Param("sbid") String sbid, @Param("sblx") String sblx);
    
    /**
     * 运送任务编号
     * 
     * @param ysrwbh
     * @return
     */
    String selectKsjhbhByRwbh(@Param("ysrwbh") String ysrwbh);
}
