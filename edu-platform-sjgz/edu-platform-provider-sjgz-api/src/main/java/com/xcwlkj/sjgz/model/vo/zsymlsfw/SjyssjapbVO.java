/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.vo.zsymlsfw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 试卷运送时间安排表vo
 * <AUTHOR>
 * @version $Id: SjyssjapbVO.java, v 0.1 2021年04月12日 16时58分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SjyssjapbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 运送时间安排列表 */
    private List<YssjapItemVO> yssjapList;

}
