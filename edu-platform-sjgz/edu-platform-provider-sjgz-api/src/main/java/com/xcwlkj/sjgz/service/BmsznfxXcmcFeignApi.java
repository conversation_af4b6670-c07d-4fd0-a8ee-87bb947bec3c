/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjgz.service;

import com.xcwlkj.core.annotation.mock.YapiMock;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


import com.xcwlkj.sjgz.service.hystrix.BmsznfxXcmcFeignHystrix;
import com.xcwlkj.sjgz.model.dto.bmsznfx.CxbmslbDTO;
import com.xcwlkj.sjgz.model.vo.bmsznfx.CxbmslbVO;
import com.xcwlkj.sjgz.model.dto.bmsznfx.ZnfxcxbjxxDTO;
import com.xcwlkj.sjgz.model.vo.bmsznfx.ZnfxcxbjxxVO;
import com.xcwlkj.sjgz.model.dto.bmsznfx.DcznfxbbDTO;
import com.xcwlkj.sjgz.model.vo.bmsznfx.DcznfxbbVO;
import com.xcwlkj.sjgz.model.dto.bmsznfx.CxbmsrzDTO;
import com.xcwlkj.sjgz.model.vo.bmsznfx.CxbmsrzVO;
import com.xcwlkj.sjgz.model.dto.bmsznfx.DcbmsrzlbDTO;
import com.xcwlkj.sjgz.model.vo.bmsznfx.DcbmsrzlbVO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * 
 * <AUTHOR>
 * @version $Id: BmsznfxXcmcFeignApi.java, v 0.1 2021年06月03日 18时26分 xcwlkj.com Exp $
 */
@FeignClient(value = "sjgz-service", fallback = BmsznfxXcmcFeignHystrix.class)
public interface BmsznfxXcmcFeignApi {

   
	/**
	 * 查询保密室列表
	 * @param cxbmslbDto
	 * @return
	 */
	@YapiMock(projectId="152", returnClass = CxbmslbVO.class)
    @PostMapping(value = "/sjgz/bmsznfx/cxbmslb")
    Wrapper<CxbmslbVO> cxbmslb(@RequestBody CxbmslbDTO cxbmslbDto);
	/**
	 * 智能分析查询报警信息
	 * @param znfxcxbjxxDto
	 * @return
	 */
	@YapiMock(projectId="152", returnClass = ZnfxcxbjxxVO.class)
    @PostMapping(value = "/sjgz/bmsznfx/znfxcxbjxx")
    Wrapper<ZnfxcxbjxxVO> znfxcxbjxx(@RequestBody ZnfxcxbjxxDTO znfxcxbjxxDto);
	/**
	 * 导出智能分析报表
	 * @param dcznfxbbDto
	 * @return
	 */
	@YapiMock(projectId="152", returnClass = DcznfxbbVO.class)
    @PostMapping(value = "/sjgz/bmsznfx/dcznfxbb")
    Wrapper<DcznfxbbVO> dcznfxbb(@RequestBody DcznfxbbDTO dcznfxbbDto);
	/**
	 * 查询保密室日志
	 * @param cxbmsrzDto
	 * @return
	 */
	@YapiMock(projectId="152", returnClass = CxbmsrzVO.class)
    @PostMapping(value = "/sjgz/bmsznfx/cxbmsrz")
    Wrapper<CxbmsrzVO> cxbmsrz(@RequestBody CxbmsrzDTO cxbmsrzDto);
	/**
	 * 导出保密室日志报表
	 * @param dcbmsrzlbDto
	 * @return
	 */
	@YapiMock(projectId="152", returnClass = DcbmsrzlbVO.class)
    @PostMapping(value = "/sjgz/bmsznfx/dcbmsrzlb")
    Wrapper<DcbmsrzlbVO> dcbmsrzlb(@RequestBody DcbmsrzlbDTO dcbmsrzlbDto);
}




