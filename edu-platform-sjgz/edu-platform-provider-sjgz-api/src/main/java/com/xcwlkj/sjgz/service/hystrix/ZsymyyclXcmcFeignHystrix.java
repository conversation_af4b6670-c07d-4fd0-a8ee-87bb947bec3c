/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjgz.service.hystrix;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.sjgz.service.ZsymyyclXcmcFeignApi;

import com.xcwlkj.sjgz.model.dto.zsymyycl.SjyycllbDTO;
import com.xcwlkj.sjgz.model.vo.zsymyycl.SjyycllbVO;
import com.xcwlkj.sjgz.model.dto.zsymyycl.SjyyclrzDTO;
import com.xcwlkj.sjgz.model.vo.zsymyycl.SjyyclrzVO;
import com.xcwlkj.sjgz.model.dto.zsymyycl.CxyyclbjslDTO;
import com.xcwlkj.sjgz.model.vo.zsymyycl.CxyyclbjslVO;
import com.xcwlkj.sjgz.model.dto.zsymyycl.CxqybmslbDTO;
import com.xcwlkj.sjgz.model.vo.zsymyycl.CxqybmslbVO;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * @version $Id: ZsymyyclXcmcFeignHystrix.java, v 0.1 2021年04月19日 13时53分 xcwlkj.com Exp $
 */
@Component
public class ZsymyyclXcmcFeignHystrix implements ZsymyyclXcmcFeignApi{

    /** 
     * @see com.xcwlkj.sjgz.service.ZsymyyclXcmcFeignApi#sjyycllb(com.xcwlkj.sjgz.model.dto.zsymyycl.SjyycllbDTO)
     */
    @Override
    public Wrapper<SjyycllbVO> sjyycllb(SjyycllbDTO sjyycllbDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.sjgz.service.ZsymyyclXcmcFeignApi#sjyyclrz(com.xcwlkj.sjgz.model.dto.zsymyycl.SjyyclrzDTO)
     */
    @Override
    public Wrapper<SjyyclrzVO> sjyyclrz(SjyyclrzDTO sjyyclrzDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.sjgz.service.ZsymyyclXcmcFeignApi#cxyyclbjsl(com.xcwlkj.sjgz.model.dto.zsymyycl.CxyyclbjslDTO)
     */
    @Override
    public Wrapper<CxyyclbjslVO> cxyyclbjsl(CxyyclbjslDTO cxyyclbjslDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.sjgz.service.ZsymyyclXcmcFeignApi#cxqybmslb(com.xcwlkj.sjgz.model.dto.zsymyycl.CxqybmslbDTO)
     */
    @Override
    public Wrapper<CxqybmslbVO> cxqybmslb(CxqybmslbDTO cxqybmslbDto) {
        return WrapMapper.error();
    }
}
