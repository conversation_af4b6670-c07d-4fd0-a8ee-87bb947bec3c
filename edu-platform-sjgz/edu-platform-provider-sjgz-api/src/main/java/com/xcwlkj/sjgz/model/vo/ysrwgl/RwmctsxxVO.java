/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.vo.ysrwgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 14-试卷运送任务-任务明细提示消息vo
 * <AUTHOR>
 * @version $Id: RwmctsxxVO.java, v 0.1 2020年04月21日 16时23分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class RwmctsxxVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 任务明细提示信息列表 */
    private List<RwmxtsxxItemVO> rwmxtsxxList;
    /** 总条数 */
    private Integer totalRows;

}
