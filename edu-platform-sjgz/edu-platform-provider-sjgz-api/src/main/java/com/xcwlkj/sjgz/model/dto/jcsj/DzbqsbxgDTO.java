/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.dto.jcsj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 18-电子标签设备-修改dto
 * <AUTHOR>
 * @version $Id: DzbqsbxgDTO.java, v 0.1 2020年12月03日 16时06分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DzbqsbxgDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 电子标签编号 */
    @NotBlank(message = "电子标签编号不能为空")
    private String dzbqbh;
    /** 标签ID */
    @NotBlank(message = "标签ID不能为空")
    private String id;
    /** 型号 */
    @NotBlank(message = "型号不能为空")
    private String xh;
    /** 是否启用：0 未启用；1 已启用； */
    @NotBlank(message = "是否启用：0 未启用；1 已启用；不能为空")
    private String sfqy;
    /** gps开关状态：0 未开；1 已开； */
    @NotBlank(message = "gps开关状态：0 未开；1 已开；不能为空")
    private String gpskgzt;

}
