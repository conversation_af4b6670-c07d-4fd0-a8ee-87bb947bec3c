/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.vo.tszsgj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 02-获取标签自己上报的数据统计信息vo
 * <AUTHOR>
 * @version $Id: HqBqSbTjxxVO.java, v 0.1 2020年12月17日 18时02分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqBqSbTjxxVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 调试时上报标签信息列表 头部 */
    private List<String> tsDzbqSbxxLbHeader;
    /** 调试时上报上报标签信息列表 数据提 */
    private List<List<String>> tsDzbqSbxxLbBody;

}
