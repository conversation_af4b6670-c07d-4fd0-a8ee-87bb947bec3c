/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.dto.ysrwgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 16-运送任务明细-快速修改任务明细名称dto
 * <AUTHOR>
 * @version $Id: KsxgrwmxmcDTO.java, v 0.1 2021年12月30日 11时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KsxgrwmxmcDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 运送任务明细编号 */
    @NotBlank(message = "运送任务明细编号不能为空")
    private String ysrwmxbh;
    /** 任务名称 */
    @NotBlank(message = "任务名称不能为空")
    private String rwmc;

}
