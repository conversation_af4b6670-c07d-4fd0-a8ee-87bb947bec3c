/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.dto.jcsj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 27-手持或标签设备-是否启用dto
 * <AUTHOR>
 * @version $Id: ScdzSfqyDTO.java, v 0.1 2020年12月16日 14时51分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ScdzSfqyDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 0 未启用；1 已启用； */
    @NotBlank(message = "0 未启用；1 已启用；不能为空")
    private String sfqy;
    /** sczdsb 手持终端设备； dzbqsb 电子标签设备； */
    @NotBlank(message = "sczdsb 手持终端设备； dzbqsb 电子标签设备；不能为空")
    private String sblx;
    /** 设备编号集合（可一条，可多条） */
    @NotNull(message = "设备编号集合（可一条，可多条）不能为空")
    private List<String> sbbhs;

}
