/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.vo.jcsj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 10-试卷车辆-列表vo
 * <AUTHOR>
 * @version $Id: SjcllbVO.java, v 0.1 2020年12月01日 16时24分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SjcllbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 试卷车辆列表 */
    private List<SjclItemVO> sjclList;
    /** 总条数 */
    private Integer totalRows;

}
