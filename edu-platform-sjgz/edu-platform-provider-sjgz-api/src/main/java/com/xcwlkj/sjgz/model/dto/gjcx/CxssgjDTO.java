/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.dto.gjcx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 查询实时轨迹dto
 * <AUTHOR>
 * @version $Id: CxssgjDTO.java, v 0.1 2020年12月04日 17时47分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CxssgjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 查询类型：sjysrw/dzbqsb/sczdsb */
    @NotBlank(message = "查询类型：sjysrw/dzbqsb/sczdsb不能为空")
    private String cxlx;
    /** 查询信息列表 */
    @NotNull(message = "查询信息列表不能为空")
    private List<CxxxItemDTO> cxxxList;

}
