/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sjgz.model.vo.jrptcx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 上报出入库信息vo
 * <AUTHOR>
 * @version $Id: OptResultItemVO.java, v 0.1 2021年03月29日 11时17分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class OptResultItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 任务id */
    private String taskId;
    /** 操作码 */
    private String optCode;

}
