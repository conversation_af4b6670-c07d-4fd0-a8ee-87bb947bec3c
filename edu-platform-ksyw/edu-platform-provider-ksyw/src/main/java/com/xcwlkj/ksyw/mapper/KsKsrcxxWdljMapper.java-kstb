/**
 * xcwlkj.com Inc.
 * Copyright (c) 2021-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.ksyw.model.domain.KsKsrcxxWdlj;
import com.xcwlkj.ksyw.model.vo.sfhysjtj.KcdcljItemVO;



/**
 * 考生入场信息_文档路径表数据库操作
 * <AUTHOR>
 * @version $Id: InitKsKsrcxxWdljMapper.java, v 0.1 2021年01月18日 16时00分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsKsrcxxWdljMapper extends MyMapper<KsKsrcxxWdlj> {

    /**
	 * 分页查询考生入场信息_文档路径表
	 * 
	 * @param example
	 * @return
	 */
	List<KsKsrcxxWdlj> pageList(KsKsrcxxWdlj example);
	
	//批量插入 文档储存路径
	void batchInsertWdlj(@Param("list") List<KcdcljItemVO> list,
						@Param("ksjhbh") String ksjhbh,
						@Param("ccm") String ccm);
	
}
