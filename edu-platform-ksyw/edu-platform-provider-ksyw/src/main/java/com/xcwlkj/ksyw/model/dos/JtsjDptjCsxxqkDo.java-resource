package com.xcwlkj.ksyw.model.dos;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class JtsjDptjCsxxqkDo implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 考区编号 */
    private String kqbh;

    /** 考点数量 */
    private Integer kd;

    /** 考场数量 */
    private Integer kc;
}
