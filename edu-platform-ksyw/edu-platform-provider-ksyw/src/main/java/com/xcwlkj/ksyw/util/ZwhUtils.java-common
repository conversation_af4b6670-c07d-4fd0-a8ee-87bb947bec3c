package com.xcwlkj.ksyw.util;


import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;



public class ZwhUtils {
	
//	private static HashMap<String,String> map;

	/**
     * 将 座位号 转为目标座位号
     */
    public static String getZwhNum(String s) {
    	
    	if(StringUtils.isBlank(s)) {
			return null;
		}
    	
    	Map<String, String> map = new HashMap<String, String>();
    	map.put("01", "1");
		map.put("02", "2");
		map.put("03", "3");
		map.put("04", "4");
		map.put("05", "5");
		map.put("06", "6");
		map.put("07", "7");
		map.put("08", "8");
		map.put("09", "9");
		map.put("10", "10");
		map.put("11", "11");
		map.put("12", "12");
		map.put("13", "13");
		map.put("14", "14");
		map.put("15", "15");
		map.put("16", "16");
		map.put("17", "17");
		map.put("18", "18");
		map.put("19", "19");
		map.put("20", "20");
		map.put("21", "21");
		map.put("22", "22");
		map.put("23", "23");
		map.put("24", "24");
		map.put("25", "25");
		map.put("26", "26");
		map.put("27", "27");
		map.put("28", "28");
		map.put("29", "29");
		map.put("30", "30");
		
        if(map.containsKey(s))  return map.get(s);
        return  s;
    }

}
