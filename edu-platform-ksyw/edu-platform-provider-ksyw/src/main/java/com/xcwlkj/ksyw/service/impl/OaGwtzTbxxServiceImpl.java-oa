/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service.impl;

import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.mapper.OaGwtzTbxxMapper;
import com.xcwlkj.ksyw.model.domain.OaGwtzTbxx;
import com.xcwlkj.ksyw.service.OaGwtzTbxxService;

import tk.mybatis.mapper.entity.Example;

import java.util.List;

import javax.annotation.Resource;


/**
 * 公文通知对填报信息汇总服务
 * <AUTHOR>
 * @version $Id: OaGwtzTbxxServiceImpl.java, v 0.1 2020年08月17日 11时26分 xcwlkj.com Exp $
 */
@Service("oaGwtzTbxxService")
public class OaGwtzTbxxServiceImpl  implements OaGwtzTbxxService  {

    @Resource
    private OaGwtzTbxxMapper modelMapper;
    
    @Override
    public void delete(String tbxxuuid) {
    	modelMapper.deleteByPrimaryKey(tbxxuuid);
    }
    
    @Override
    public void delete_List(String sczt ,List<String> tbxxuuidsList) {
    	if (tbxxuuidsList.size()==0)	return ;
    	modelMapper.delete_List(sczt,tbxxuuidsList);
    }
    
    @Override
    public void insert(OaGwtzTbxx oaGwtzTbxx) {
    	modelMapper.insert(oaGwtzTbxx);
    }
    
    @Override
    public void insert_List(List<OaGwtzTbxx> oaGwtzTbxxs) {
    	if (oaGwtzTbxxs.size()==0)	return ;
    	modelMapper.insert_List(oaGwtzTbxxs);
    }
    
    @Override
    public List<OaGwtzTbxx> selectByExample(Example example) { 
    	return modelMapper.selectByExample(example);
    }
    
    @Override
    public void updete(OaGwtzTbxx oaGwtzTbxx) {
    	modelMapper.updateByPrimaryKey(oaGwtzTbxx);
    }
    
    @Override
    public void removeByGwtzUuid(String gwtzUuid) {
        modelMapper.removeByGwtzUuid(gwtzUuid);
    }
    
}