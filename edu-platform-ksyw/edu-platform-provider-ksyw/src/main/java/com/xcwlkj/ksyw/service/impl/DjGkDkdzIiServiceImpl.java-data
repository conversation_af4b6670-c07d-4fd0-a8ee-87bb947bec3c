/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service.impl;

import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.mapper.DjGkDkdzIiMapper;
import com.xcwlkj.ksyw.model.domain.DjGkDkdzIi;
import com.xcwlkj.ksyw.service.DjGkDkdzIiService;

import java.util.List;

import javax.annotation.Resource;


/**
 * 高考 高职单招 考区考点考场数据 技能服务
 * <AUTHOR>
 * @version $Id: DjGkDkdzIiServiceImpl.java, v 0.1 2020年06月25日 17时36分 xcwlkj.com Exp $
 */
@Service("djGkDkdzIiService")
public class DjGkDkdzIiServiceImpl  implements DjGkDkdzIiService  {

    @Resource
    private DjGkDkdzIiMapper modelMapper;

    @Override
    public List<DjGkDkdzIi> selectAll() {
        return modelMapper.selectAll();
    }

}