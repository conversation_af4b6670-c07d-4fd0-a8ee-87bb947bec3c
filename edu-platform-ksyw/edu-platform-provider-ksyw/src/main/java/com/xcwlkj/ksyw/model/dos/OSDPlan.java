package com.xcwlkj.ksyw.model.dos;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class OSDPlan implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** OSD配置计划:OSDPlan,设备参数透明设置:Transparent */
    private String DefaultOSD;

    private String Mode;

    /** OSD计划列表(必选) */
    private List<OSDPlans> plans;
}
