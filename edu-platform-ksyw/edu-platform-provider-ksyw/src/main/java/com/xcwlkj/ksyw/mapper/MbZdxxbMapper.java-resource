/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.ksyw.model.domain.MbZdxxb;



/**
 * 码表字段信息表数据库操作
 * <AUTHOR>
 * @version $Id: InitMbZdxxbMapper.java, v 0.1 2020年04月21日 15时03分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface MbZdxxbMapper extends MyMapper<MbZdxxb> {

    /**
	 * 分页查询码表字段信息表
	 * 
	 * @param example
	 * @return
	 */
	List<MbZdxxb> pageList(MbZdxxb example);
}
