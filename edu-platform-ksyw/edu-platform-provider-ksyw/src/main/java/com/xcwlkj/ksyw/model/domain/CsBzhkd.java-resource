/**
 * xcwlkj.com Inc.
 * Copyright (c) 2021-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * cs_bzhkd
 * 
 * <AUTHOR>
 * @version $Id: CsBzhkd.java, v 0.1 2021年11月12日 11时06分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "cs_bzhkd")
public class CsBzhkd implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /**  */
    @Id
    @Column(name = "bzhkdid")
    private String            bzhkdid;
    /**  */
    @Column(name = "ksgljgbzm")
    private String            ksgljgbzm;
    /** 名称 */
    @Column(name = "bzhkdmc")
    private String            bzhkdmc;
    /** 简称 */
    @Column(name = "bzhkdjc")
    private String            bzhkdjc;
    /** 考点照片 */
    @Column(name = "bzhkdzp")
    private String            bzhkdzp;
    /** 考点类别码 */
    @Column(name = "kdlbm")
    private String            kdlbm;
    /** 考点类别 */
    @Column(name = "kdlb")
    private String            kdlb;
    /** 考点所在省码 */
    @Column(name = "kdszsfm")
    private String            kdszsfm;
    /** 考点所在省 */
    @Column(name = "kdszsf")
    private String            kdszsf;
    /** 考点所在市码 */
    @Column(name = "kdszsqm")
    private String            kdszsqm;
    /** 考点所在市 */
    @Column(name = "kdszsq")
    private String            kdszsq;
    /** 考点所在区县码 */
    @Column(name = "kdszqxm")
    private String            kdszqxm;
    /** 考点所在区县 */
    @Column(name = "kdszqx")
    private String            kdszqx;
    /** 考点地址 */
    @Column(name = "kddz")
    private String            kddz;
    /** 考点建成时间 */
    @Column(name = "kdjcsj")
    private String            kdjcsj;
    /** 考点经度 */
    @Column(name = "kdjd")
    private BigDecimal            kdjd;
    /** 考点纬度 */
    @Column(name = "kdwd")
    private BigDecimal            kdwd;
    /** 考点负责人姓名 */
    @Column(name = "kdfzrxm")
    private String            kdfzrxm;
    /** 考点负责人电话 */
    @Column(name = "kdfzrdh")
    private String            kdfzrdh;
    /** 考点负责人编号 */
    @Column(name = "kdfzrbh")
    private String            kdfzrbh;
    /** 考务负责人姓名 */
    @Column(name = "kwfzrxm")
    private String            kwfzrxm;
    /** 考务负责人电话 */
    @Column(name = "kwfzrdh")
    private String            kwfzrdh;
    /** 考务负责人编号 */
    @Column(name = "kwfzrbh")
    private String            kwfzrbh;
    /** 技术负责人姓名 */
    @Column(name = "jsfzrxm")
    private String            jsfzrxm;
    /** 技术负责人电话 */
    @Column(name = "jsfzrdh")
    private String            jsfzrdh;
    /** 技术负责人编号 */
    @Column(name = "jsfzrbh")
    private String            jsfzrbh;
    /** 考务办公室电话 */
    @Column(name = "kwbgsdh")
    private String            kwbgsdh;
    /** 试卷保管室电话 */
    @Column(name = "sjbgsdh")
    private String            sjbgsdh;
    /** 是否可编辑 */
    @Column(name = "sfkbj")
    private String            sfkbj;
    /** sip地址 */
    @Column(name = "sipdz")
    private String            sipdz;
    /** 删除状态 */
    @Column(name = "sczt")
    private String            sczt;
    /** 视频监考室电话 */
    @Column(name = "spjksdh")
    private String            spjksdh;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;
    /** 学校的层次类型         K1 省属学校   K2 市属学校  K3 县属学校 */
    @Column(name = "cclx")
    private String            cclx;
    /** 设备建设完成考点  0未完成  1已完成 --该考点身份验证作弊防控设备均已建设完毕 */
    @Column(name = "sbjswckd")
    private String            sbjswckd;
    /** 教育部对接的编号 */
    @Column(name = "jybdjbh")
    private String            jybdjbh;
    /** 节点简称 */
    @Column(name = "jdjc")
    private String            jdjc;
    /** 院校类型代码(仅用于大学) */
    @Column(name = "yxlxm")
    private String            yxlxm;
    /** 高校代码(仅用于大学) */
    @Column(name = "gxdm")
    private String            gxdm;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


