/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service;

import com.xcwlkj.ksyw.model.domain.KsKsrcxxJkmxckycxxb;
import com.xcwlkj.ksyw.model.vo.jczhkqzl.HqksrcslV2VO;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;


/**
 * 健康码行程卡异常信息表服务
 * <AUTHOR>
 * @version $Id: KsKsrcxxJkmxckycxxbService.java, v 0.1 2021年11月18日 15时49分 xcwlkj.com Exp $
 */
@Service
public interface KsKsrcxxJkmxckycxxbService  {

    /**保存一个实体，null的属性也会保存，不会使用数据库默认值*/
    int insert(KsKsrcxxJkmxckycxxb record);
    /**保存一个实体，null的属性不会保存，会使用数据库默认值*/
    int insertSelective(KsKsrcxxJkmxckycxxb record);
    /**插入数据，限制为实体包含`id`属性并且必须为自增列，实体配置的主键策略无效*/
    int insertUseGeneratedKeys(KsKsrcxxJkmxckycxxb record);
    /**根据主键更新属性不为null的值*/
    int updateByPrimaryKeySelective(KsKsrcxxJkmxckycxxb record);
    /**根据Example条件更新实体`record`包含的不是null的属性值*/
    int updateByExampleSelective(KsKsrcxxJkmxckycxxb record, Example example);
    /**根据实体属性作为条件进行删除，查询条件使用等号*/
    int delete(KsKsrcxxJkmxckycxxb record);
    /**根据主键字段进行删除，方法参数必须包含完整的主键属性*/
    int deleteByPrimaryKey(String record);
    /**根据Example条件删除数据*/
    int deleteByExample(Example example);
    /**根据主键字段进行查询，方法参数必须包含完整的主键属性，查询条件使用等号*/
    KsKsrcxxJkmxckycxxb selectByPrimaryKey(Object record);
    /**查询全部结果*/
    List<KsKsrcxxJkmxckycxxb> selectAll();
    /**根据Example条件进行查询*/
    List<KsKsrcxxJkmxckycxxb> selectByExample(Example example);
    /**单个匹配，必须保证匹配一条数据，如果匹配的有多条则报错*/
    KsKsrcxxJkmxckycxxb selectOne(KsKsrcxxJkmxckycxxb record);

    /**
     * 根据查询条件统计数量
     *
     * @param example
     * @return
     */
    int selectCountByExample(Example example);

    HqksrcslV2VO getkqjkmslxx(String ksjhbh,String kqbh,String ccm);
}