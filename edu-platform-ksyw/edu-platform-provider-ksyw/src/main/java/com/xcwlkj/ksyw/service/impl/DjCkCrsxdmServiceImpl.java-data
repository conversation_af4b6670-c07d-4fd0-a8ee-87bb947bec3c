/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service.impl;

import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.mapper.DjCkCrsxdmMapper;
import com.xcwlkj.ksyw.mapper.DjZkKskMapper;
import com.xcwlkj.ksyw.model.domain.DjCkCrsxdm;
import com.xcwlkj.ksyw.model.domain.DjZkKsk;
import com.xcwlkj.ksyw.service.DjCkCrsxdmService;

import java.util.List;

import javax.annotation.Resource;


/**
 * 成考 市县代码服务
 * <AUTHOR>
 * @version $Id: DjCkCrsxdmServiceImpl.java, v 0.1 2020年10月17日 17时26分 xcwlkj.com Exp $
 */
@Service("djCkCrsxdmService")
public class DjCkCrsxdmServiceImpl  implements DjCkCrsxdmService  {

    @Resource
    private DjCkCrsxdmMapper modelMapper;


//    @Override
//    public List<DjZkKsk> selectAll() {
//        return modelMapper.selectAll();
//    }

    @Override
    public List<DjCkCrsxdm> selectAllKq() {
        return modelMapper.selectAllKq();
    }
//
//    @Override
//    public List<DjZkKsk> pageListUseLimitKsBmxx(int offSet, int limit) {
//        return modelMapper.pageListUseLimitKsBmxx(offSet, limit);
//    }
//
//    @Override
//    public List<DjZkKsk> pageListUseLimitKsBpxx(int offSet, int limit) {
//        return modelMapper.pageListUseLimitKsBpxx(offSet, limit);
//    }
    
}