/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.web;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import com.xcwlkj.ksyw.model.domain.*;
import com.xcwlkj.ksyw.service.*;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.xcwlkj.model.enums.DjywxtmcEnum;
import com.xcwlkj.model.enums.JgslbcxlxEnum;
import com.xcwlkj.model.enums.KdgwzzmEnum;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.model.enums.SfhyztEnum;
import com.xcwlkj.sbyw.model.dto.hqcslistipclb.HqcsListipclbDTO;
import com.xcwlkj.sbyw.model.vo.hqcslistipclb.HqcsListipclbVO;
import com.xcwlkj.sbyw.model.vo.hqcslistipclb.IpczyItemVO;
import com.xcwlkj.sbyw.service.DwfwSysFeignApi;
import com.xcwlkj.service.SfhyCacheOperateService;
import com.xcwlkj.spxc.model.dto.spxcdwfw.HqkdxxsjLbDTO;
import com.xcwlkj.spxc.model.vo.spxcdwfw.FzjckdxxsjItemVO;
import com.xcwlkj.spxc.model.vo.spxcdwfw.HqkdxxsjLbVO;
import com.xcwlkj.spxc.service.SpxcDwfwSysFeignApi;
import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.util.ObjectUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.ksyw.model.dto.jczhkdxq.HqkcjtlbxhbfspDTO;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.HqkcjtlbxhbfspVO;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.HqkdxxsjItemVO;
import com.xcwlkj.ksyw.exceptions.KsywBusiException;
import com.xcwlkj.ksyw.model.dto.jczhkdxq.GjkdbhhqkcxxDTO;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.GjkdbhhqkcxxItemVO;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.GjkdbhhqkcxxVO;
import com.xcwlkj.ksyw.model.dto.jczhkdxq.GjkdbsmhqkdxxDTO;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.GjkdbsmhqkdxxVO;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.HqkcjtlbxhbfspItemVO;
import com.xcwlkj.ksyw.model.dto.jczhkdxq.HqkskcqkDTO;
import com.xcwlkj.ksyw.model.enums.SfhySftgEnum;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.HqkskcqkVO;
import com.xcwlkj.ksyw.model.dto.jczhkdxq.HqkdxxsjDTO;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import com.xcwlkj.ksyw.model.vo.jczhkdxq.HqkdxxsjVO;
import com.xcwlkj.util.wrapper.Wrapper;

import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

/**
 * 决策指挥考点详情接口
 * <AUTHOR>
 * @version $Id: JczhKdxqXcmcFeignClient.java, v 0.1 2020年05月23日 14时41分 xcwlkj.com Exp $
 */
@RestController
public class JczhKdxqXcmcFeignClient extends BaseFeignClient implements JczhKdxqXcmcFeignApi {

    @Resource
    private KdKcxxService   kcxxService;

    @Resource
    private KdKdxxService   kdxxService;

    @Resource
    private CsBzhkdService  bzhkdService;

    @Resource
    private CsBzhkcService  bzhkcService;
    

    @Autowired
    private KsKsrcxxService ksksrcxxService;
    

    @Resource
    private KsKwryService   kwryService;

    @Resource
    private KsBpxxService   ksbpService;

    @Resource
    private DwfwSysFeignApi sbywDwfwSysApi;

    public void setSbywDwfwSysApi(DwfwSysFeignApi sbywDwfwSysApi) {
        this.sbywDwfwSysApi = sbywDwfwSysApi;
    }

    @Resource
    private SpxcDwfwSysFeignApi spxcApi;

    public void setSpxcApi(SpxcDwfwSysFeignApi spxcApi) {
        this.spxcApi = spxcApi;
    }

    @Autowired
    private SfhyCacheOperateService sfhyCacheOperateService;

    @Resource
    private KdKcxxBykcService bykcService;

    @Resource
    private KsKsrcxxJkmxckycxxbService ksKsrcxxJkmxckycxxbService;

    /** 
     * @see com.xcwlkj.ksyw.service.JczhKdxqXcmcFeignApi#hqkcjtlbxhbfsp(com.xcwlkj.ksyw.model.dto.jczhkdxq.HqkcjtlbxhbfspDTO)
     */
    @Override
    public Wrapper<HqkcjtlbxhbfspVO> hqkcjtlbxhbfsp(@RequestBody @Validated HqkcjtlbxhbfspDTO dto) {
        logger.info("考点详情-获取考场镜头列表循环播放视频HqkcjtlbxhbfspDTO={}", dto);
        HqkcjtlbxhbfspVO result = new HqkcjtlbxhbfspVO();
        //查询考点信息
        KdKdxx kdKdxx = kdxxService.selectByKey(dto.getKdbzid());
        if (ObjectUtil.isEmpty(kdKdxx)) {
            throw new KsywBusiException("无此考点信息！");
        }
        if (ObjectUtil.isEmpty(kdKdxx.getBzhkdid())) {
            throw new KsywBusiException("该考点未关联标准化考点！");
        }
        //查询考场信息
        Example kcxxExample = new Example(KdKcxx.class);
        Criteria kcxxCriteria = kcxxExample.createCriteria();
        kcxxCriteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        kcxxCriteria.andEqualTo("kdbh", kdKdxx.getKdbh());
        kcxxCriteria.andEqualTo("ccm", kdKdxx.getCcm());
        kcxxCriteria.andEqualTo("ksjhbh", kdKdxx.getKsjhbh());
        List<KdKcxx> kcxxList = kcxxService.selectByExample(kcxxExample);
        List<String> bzhkcBhList = new ArrayList<String>();
        for (KdKcxx kdKcxx : kcxxList) {
            if (StringUtil.isNotBlank(kdKcxx.getBzhkcbh())) {
                bzhkcBhList.add(kdKcxx.getBzhkcbh());
            }
        }

        if (bzhkcBhList.isEmpty()) {
            throw new KsywBusiException("该考点考场未关联标准化考场");
        }
        List<HqkcjtlbxhbfspItemVO> hqkcjtlbxhbfspList = new ArrayList<HqkcjtlbxhbfspItemVO>();

        //调用设备运维 视频巡查-（摄像头）获取所有场所摄像头列表
        HqcsListipclbDTO hqcsListipclbDto = new HqcsListipclbDTO();
        hqcsListipclbDto.setCsbhLb(bzhkcBhList);
        Wrapper<HqcsListipclbVO> hqcsListipclbWarpper = sbywDwfwSysApi
            .hqcsListipclb(hqcsListipclbDto);
        if (ObjectUtil.isNotEmpty(hqcsListipclbWarpper.getResult())
            && CollectionUtils.isNotEmpty(hqcsListipclbWarpper.getResult().getIpczyList())) {
            List<IpczyItemVO> ipczyList = hqcsListipclbWarpper.getResult().getIpczyList();
            for (IpczyItemVO ipczyItemVO : ipczyList) {
                for (KdKcxx kdKcxx : kcxxList) {
                    if (StringUtil.isNotBlank(kdKcxx.getBzhkcbh())
                        && ipczyItemVO.getCsbh().equals(kdKcxx.getBzhkcbh())) {
                        HqkcjtlbxhbfspItemVO hqkcjtlbxhbfsp = new HqkcjtlbxhbfspItemVO();
                        hqkcjtlbxhbfsp.setKcbzid(kdKcxx.getKcbzid());
                        hqkcjtlbxhbfsp.setKcmc(kdKcxx.getKcmc());
                        hqkcjtlbxhbfsp.setJtsipurl(ipczyItemVO.getSipdz());
                        hqkcjtlbxhbfspList.add(hqkcjtlbxhbfsp);
                    }
                }
            }
        }
        //返回指定数量
        if (hqkcjtlbxhbfspList.size() > dto.getHqsl()) {
            hqkcjtlbxhbfspList = hqkcjtlbxhbfspList.subList(0, dto.getHqsl());
        }
        result.setHqkcjtlbxhbfspList(hqkcjtlbxhbfspList);
        logger.info("hqkcjtlbxhbfsp - 考点详情-获取考场镜头列表循环播放视频. [OK] HqkcjtlbxhbfspVO={}", result);
        return WrapMapper.ok(result);
    }

    /** 
     * @see com.xcwlkj.ksyw.service.JczhKdxqXcmcFeignApi#gjkdbhhqkcxx(com.xcwlkj.ksyw.model.dto.jczhkdxq.GjkdbhhqkcxxDTO)
     */
    @Override
    public Wrapper<GjkdbhhqkcxxVO> gjkdbhhqkcxx(@RequestBody @Validated GjkdbhhqkcxxDTO dto) {
        logger.info("考点详情-根据考点编号获取考场信息GjkdbhhqkcxxDTO={}", dto);
        GjkdbhhqkcxxVO result = new GjkdbhhqkcxxVO();
        //查询考场信息
        Example example = new Example(KdKcxx.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        criteria.andEqualTo("ccm", dto.getCcm());
        criteria.andEqualTo("kdbh", dto.getKdbh());
        criteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        List<KdKcxx> kcxxList = kcxxService.selectByExample(example);
        List<GjkdbhhqkcxxItemVO> gjkdbhhqkcxxList = new ArrayList<GjkdbhhqkcxxItemVO>();
        for (KdKcxx kdKcxx : kcxxList) {
            GjkdbhhqkcxxItemVO gjkdbhhqkcxx = new GjkdbhhqkcxxItemVO();
            gjkdbhhqkcxx.setKcbzid(kdKcxx.getKcbzid());
            gjkdbhhqkcxx.setKcbh(kdKcxx.getKcbh());
            gjkdbhhqkcxx.setKcmc(kdKcxx.getKcmc());
            gjkdbhhqkcxx.setBzhkdid(kdKcxx.getBzhkdid());
            gjkdbhhqkcxx.setBzhkcid(kdKcxx.getBzhkcid());
            gjkdbhhqkcxx.setBzhkcbh(kdKcxx.getBzhkcbh());
            gjkdbhhqkcxx.setBzhkcmc(kdKcxx.getBzhkcmc());
            gjkdbhhqkcxxList.add(gjkdbhhqkcxx);
        }

        Collections.sort(gjkdbhhqkcxxList,
            Comparator.nullsLast(Comparator.comparing(GjkdbhhqkcxxItemVO::getKcmc)));

        Example jkmExample = new Example(KsKsrcxxJkmxckycxxb.class);
        Criteria jkmCriteria = jkmExample.createCriteria();
        jkmCriteria.andEqualTo("kdbh", dto.getKdbh());
        jkmCriteria.andEqualTo("ccm", dto.getCcm());
        jkmCriteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        jkmExample.selectProperties("bykcbh");
        jkmExample.setDistinct(true);
        List<KsKsrcxxJkmxckycxxb> jkmList = ksKsrcxxJkmxckycxxbService.selectByExample(jkmExample);
        if (CollectionUtil.isNotEmpty(jkmList)) {
            List<String> bykcidList = jkmList.stream().map(KsKsrcxxJkmxckycxxb::getBykcbh)
                .collect(Collectors.toList());
            Example bykcExample = new Example(KdKcxxBykc.class);
            Criteria bykcCriteria = bykcExample.createCriteria();
            bykcCriteria.andIn("kcbzid", bykcidList);
            bykcCriteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
            bykcCriteria.andEqualTo("kdbh", dto.getKdbh());
            bykcCriteria.andEqualTo("ccm", dto.getCcm());
            bykcCriteria.andEqualTo("ksjhbh", dto.getKsjhbh());
            bykcExample.orderBy("cslx");
            List<KdKcxxBykc> bykcList = bykcService.selectByExample(bykcExample);
            for (KdKcxxBykc bykcxx : bykcList) {
                GjkdbhhqkcxxItemVO gjkdbhhqkcxx = new GjkdbhhqkcxxItemVO();
                gjkdbhhqkcxx.setKcbzid(bykcxx.getKcbzid());
                gjkdbhhqkcxx.setKcbh(bykcxx.getKcbzid());
                gjkdbhhqkcxx.setKcmc(bykcxx.getKcmc());
                gjkdbhhqkcxx.setBzhkdid(bykcxx.getBzhkdid());
                gjkdbhhqkcxx.setBzhkcid(bykcxx.getBzhkcid());
                gjkdbhhqkcxx.setBzhkcbh(bykcxx.getBzhkcbh());
                gjkdbhhqkcxx.setBzhkcmc(bykcxx.getBzhkcmc());
                gjkdbhhqkcxx.setKclx(bykcxx.getCslx());
                gjkdbhhqkcxxList.add(gjkdbhhqkcxx);
            }
        }
        
        result.setGjkdbhhqkcxxList(gjkdbhhqkcxxList);

        logger.info("gjkdbhhqkcxx - 考点详情-根据考点编号获取考场信息. [OK] GjkdbhhqkcxxVO={}", result);
        return WrapMapper.ok(result);
    }

    /** 
     * @see com.xcwlkj.ksyw.service.JczhKdxqXcmcFeignApi#gjkdbsmhqkdxx(com.xcwlkj.ksyw.model.dto.jczhkdxq.GjkdbsmhqkdxxDTO)
     */
    @Override
    public Wrapper<GjkdbsmhqkdxxVO> gjkdbsmhqkdxx(@RequestBody @Validated GjkdbsmhqkdxxDTO dto) {
        logger.info("考点详情-根据考点标识码获取考点信息GjkdbsmhqkdxxDTO={}", dto);
        GjkdbsmhqkdxxVO result = new GjkdbsmhqkdxxVO();
        //查询考点信息
        KdKdxx kdKdxx = kdxxService.selectByKey(dto.getKdbzid());
        if (ObjectUtil.isEmpty(kdKdxx)) {
            throw new KsywBusiException("无此考点信息！");
        }
        if (StringUtil.isBlank(kdKdxx.getBzhkdid())) {
            throw new KsywBusiException("该考点未关联标准化考点！");
        }
        //查询标准化考点信息
        CsBzhkd bzhkd = bzhkdService.selectByKey(kdKdxx.getBzhkdid());

        result.setKwbgsdh(bzhkd.getKwbgsdh());
        result.setSjbgsdh(bzhkd.getSjbgsdh());
        result.setSpjksdh(bzhkd.getSpjksdh());
        result.setKdjd(bzhkd.getKdjd());
        result.setKdwd(bzhkd.getKdwd());
        result.setKddz(bzhkd.getKddz());
        //查询考务人员信息
        Example example = new Example(KsKwry.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        criteria.andEqualTo("ccm", kdKdxx.getCcm());
        criteria.andEqualTo("ksjhbh", kdKdxx.getKsjhbh());
        criteria.andEqualTo("kdbh", kdKdxx.getKdbh());
        criteria.andEqualTo("ksjsm", KdgwzzmEnum.ZK.getCode());
        List<KsKwry> kwryList = kwryService.selectByExample(example);
        if (kwryList != null && !kwryList.isEmpty()) {
            KsKwry kwry = kwryList.get(0);
            result.setZkxm(kwry.getCsrymc());
            result.setZkzp(kwry.getCsrytx());
        }

        logger.info("gjkdbsmhqkdxx - 考点详情-根据考点标识码获取考点信息. [OK] GjkdbsmhqkdxxVO={}", result);
        return WrapMapper.ok(result);
    }

    /** 
     * @see com.xcwlkj.ksyw.service.JczhKdxqXcmcFeignApi#hqkskcqk(com.xcwlkj.ksyw.model.dto.jczhkdxq.HqkskcqkDTO)
     */
    @Override
    public Wrapper<HqkskcqkVO> hqkskcqk(@RequestBody @Validated HqkskcqkDTO dto) {
        logger.info("考点详情-获取考生考场情况HqkskcqkDTO={}", dto);
        HqkskcqkVO result = new HqkskcqkVO();
        //查询考点信息
        KdKdxx kdKdxx = new KdKdxx();
        kdKdxx.setSczt(ScztEnum.NOTDEL.getCode());
        kdKdxx.setCcm(dto.getCcm());
        kdKdxx.setKsjhbh(dto.getKsjhbh());
        kdKdxx.setKdbh(dto.getKdbh());
        KdKdxx kdxx = kdxxService.selectOne(kdKdxx);
        if (ObjectUtil.isEmpty(kdxx)) {
            throw new KsywBusiException("无此考点信息！");
        }
        if (StringUtil.isBlank(kdxx.getBzhkdid())) {
            throw new KsywBusiException("该考点未关联标准化考点！");
        }
        //考生总数
        int kszs = 0;
        //入场人数
        int rcrs = 0;
        //考场总数
        int kczs = 0;
        //启用数量
        int qysl = 0;
        //统计标准化考场数量
        Example bzhkcExample = new Example(CsBzhkc.class);
        Criteria bzhkcCriteria = bzhkcExample.createCriteria();
        bzhkcCriteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        bzhkcCriteria.andEqualTo("bzhkdid", kdxx.getBzhkdid());
        kczs = bzhkcService.selectCountByExample(bzhkcExample);
        //统计考场数量
        Example kcExample = new Example(KdKcxx.class);
        Criteria kcCriteria = kcExample.createCriteria();
        kcCriteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        kcCriteria.andEqualTo("ccm", dto.getCcm());
        kcCriteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        kcCriteria.andEqualTo("kdbh", dto.getKdbh());
        qysl = kcxxService.selectCountByExample(kcExample);
        //统计考生数量
        Example ksbpExample = new Example(KsBpxx.class);
        Criteria ksbpCriteria = ksbpExample.createCriteria();
        ksbpCriteria.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        ksbpCriteria.andEqualTo("ccm", dto.getCcm());
        ksbpCriteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        ksbpCriteria.andEqualTo("kdbh", dto.getKdbh());
        kszs = ksbpService.selectCountByExample(ksbpExample);
        
        
//        //redis 身份核验查询条件
//        String sfhycxtj = DjywxtmcEnum.SFHY.getCode() + ":" + dto.getKsjhbh() + ":" + dto.getCcm()
//                          + ":" + kdxx.getKqbh() + ":" + kdxx.getKdbh() + ":"
//                          + SfhyztEnum.TRUE.getCode();
//        Set<String> kshytgSet = sfhyCacheOperateService.queryKsrcXxSet(sfhycxtj);
//        //TODO 【2020-07-05临时需求】核验通过准考证号列表==》改为身份证号需去重
//        List<String> hytgzkzhList = new ArrayList<String>();
//        for (String string : kshytgSet) {
//            String[] hytgxx = string.split(":");
//            if (!hytgzkzhList.contains(hytgxx[0]))
//                hytgzkzhList.add(hytgxx[0]);
//        }
//        rcrs = hytgzkzhList.size();
        
        String ccm = dto.getCcm();
        String ksjhbh = dto.getKsjhbh();
        String kdbh = dto.getKdbh();

        Example rcxxexample = new Example(KsKsrcxx.class);
        //入场人数
        rcxxexample.createCriteria().andEqualTo("ksjhbh", ksjhbh).andEqualTo("ccm", ccm)
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
                .andEqualTo("sfrc", SfhySftgEnum.YRC.getCode())
                .andEqualTo("kdbh", kdbh);
        rcrs = ksksrcxxService.selectCountByExample(rcxxexample);
//        rcxxexample.clear();
        
        
        result.setKczs(kczs);
        result.setKszs(kszs);
        result.setQysl(qysl);
        result.setRcrs(rcrs);

        logger.info("hqkskcqk - 考点详情-获取考生考场情况. [OK] HqkskcqkVO={}", result);
        return WrapMapper.ok(result);
    }

    /** 
     * @see com.xcwlkj.ksyw.service.JczhKdxqXcmcFeignApi#hqkdxxsj(com.xcwlkj.ksyw.model.dto.jczhkdxq.HqkdxxsjDTO)
     */
    @Override
    public Wrapper<HqkdxxsjVO> hqkdxxsj(@RequestBody @Validated HqkdxxsjDTO dto) {
        logger.info("考点详情-获取考点消息事件HqkdxxsjDTO={}", dto);
        HqkdxxsjVO result = new HqkdxxsjVO();
        //调用视频巡查接口  辅助决策 - 获取考点消息事件列表
        HqkdxxsjLbDTO hqkdxxsjLbDto = new HqkdxxsjLbDTO();
        hqkdxxsjLbDto.setCcm(dto.getCcm());
        hqkdxxsjLbDto.setJgbh(dto.getBzhkdid());
        hqkdxxsjLbDto.setJglx(JgslbcxlxEnum.Bzhkd.getCode());
        hqkdxxsjLbDto.setKsjhbh(dto.getKsjhbh());
        hqkdxxsjLbDto.setPageNum(dto.getPageNum());
        hqkdxxsjLbDto.setPageSize(dto.getPageSize());
        Wrapper<HqkdxxsjLbVO> kdxxsjWrapper = spxcApi.hqkdxxsjLb(hqkdxxsjLbDto);
        List<HqkdxxsjItemVO> hqkdxxsjList = new ArrayList<HqkdxxsjItemVO>();
        if (ObjectUtil.isNotEmpty(kdxxsjWrapper.getResult())) {
            //消息事件列表返回值不为空
            if (CollectionUtils.isNotEmpty(kdxxsjWrapper.getResult().getFzjckdxxsjList())) {
                List<FzjckdxxsjItemVO> fzjckdxxsjList = kdxxsjWrapper.getResult()
                    .getFzjckdxxsjList();
                for (FzjckdxxsjItemVO fzjckdxxsjItemVO : fzjckdxxsjList) {
                    HqkdxxsjItemVO hqkdxxsj = new HqkdxxsjItemVO();
                    hqkdxxsj.setXxsjlx(fzjckdxxsjItemVO.getXxsjlx());
                    hqkdxxsj.setXxsjnr(fzjckdxxsjItemVO.getXxsjnr());
                    hqkdxxsj.setXxsjsj(fzjckdxxsjItemVO.getXxsjsj());
                    hqkdxxsjList.add(hqkdxxsj);
                }
            }
            //总条数返回值不为空
            if (ObjectUtil.isNotEmpty(kdxxsjWrapper.getResult().getTotalRows())) {
                result.setTotalRows(kdxxsjWrapper.getResult().getTotalRows());
            }
        }
        result.setHqkdxxsjList(hqkdxxsjList);
        logger.info("hqkdxxsj - 考点详情-获取考点消息事件. [OK] HqkdxxsjVO={}", result);
        return WrapMapper.ok(result);
    }
}
