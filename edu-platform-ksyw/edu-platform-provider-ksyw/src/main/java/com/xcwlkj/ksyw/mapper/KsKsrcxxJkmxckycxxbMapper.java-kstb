/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.ksyw.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.ksyw.model.domain.KsKsrcxxJkmxckycxxb;



/**
 * 健康码行程卡异常信息表数据库操作
 * <AUTHOR>
 * @version $Id: InitKsKsrcxxJkmxckycxxbMapper.java, v 0.1 2022年01月04日 19时39分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsKsrcxxJkmxckycxxbMapper extends MyMapper<KsKsrcxxJkmxckycxxb> {

    /**
	 * 分页查询健康码行程卡异常信息表
	 * 
	 * @param example
	 * @return
	 */
	List<KsKsrcxxJkmxckycxxb> pageList(KsKsrcxxJkmxckycxxb example);

	int getksjkmycsl(@Param("ksjhbh")String ksjhbh,
					 @Param("kqbh")String kqbh,
					 @Param("ccm")String ccm,
					 @Param("yclx")String yclx,
					 @Param("cclx")String cclx);




}
