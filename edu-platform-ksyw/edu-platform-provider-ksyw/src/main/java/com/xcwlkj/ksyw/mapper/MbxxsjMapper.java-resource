package com.xcwlkj.ksyw.mapper;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import com.xcwlkj.ksyw.model.dto.mbsj.MbbjsjItemDTO;
import com.xcwlkj.ksyw.model.dto.mbsj.MbbjzjxxItemDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.xcwlkj.ksyw.model.domain.MbZdxxb;
import com.xcwlkj.ksyw.model.dto.mbsj.MbbcsjItemDTO;
import com.xcwlkj.ksyw.model.dto.mbsj.ZjxxItemDTO;

/**
 * 码表信息记录
 * <AUTHOR>
 * @version $Id: MbxxsjMapper.java, v 0.1 2020年04月10日 00时26分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface MbxxsjMapper {

    /**
     * 创建码表信息
     * 
     * @param mbywmc
     * @param zjzd
     * @param mbzdxxbList
     */
    void createTable(@Param("mbywmc") String mbywmc, @Param("zjzdList") List<String> zjzdList,
                     @Param("mbzdxxbList") List<MbZdxxb> mbzdxxbList);

    /**
     * 更新表备注
     * 
     * @param mbywmc
     * @param mbzwmc
     */
    void updateTableComment(@Param("mbywmc")String mbywmc, @Param("mbzwmc") String mbzwmc);

    /**
     * 更新列备注
     * 
     * @param mbywmc
     * @param zdywmc
     * @param zdmc
     */
    void updateColumnComment(String mbywmc, String zdywmc, String zdmc);

    /**
     * 删除码表
     * 
     * @param mbywmc
     */
    void dropTable(String mbywmc);

    /**
     * 查询码表内容
     * 
     * @param params
     * @return
     */
    LinkedList<HashMap<String, Object>> queryByTableName(@Param("mbywmc") String mbywmc,
                                                   @Param("cxzd") String cxzd,
                                                   @Param("cxcsMap") Map<String, String> cxcsMap,
                                                   @Param("zjzd") String zjzd,
                                                   @Param("pxzd") String pxzd);

    /**
     * 插入数据
     * 
     * @param mbywmc
     * @param mbzdxxbList
     */
    void insertToMbTable(@Param("mbywmc")String mbywmc,@Param("mbzdxxbList")List<MbbcsjItemDTO> mbzdxxbList);
    
    
    /**
     * 根据主键查询数据
     * 
     * @param mbywmc
     * @param zjxxList
     * @return
     */
    HashMap<String,String> queryTableMsgByKey(@Param("mbywmc") String mbywmc,@Param("zjxxList") List<ZjxxItemDTO> zjxxList );

    /**
     * 删除数据
     *
     * @param mbywmc
     * @param zjxxList
     */
    void deleteMbTableMsg(@Param("mbywmc") String mbywmc, @Param("zjxxList") List<ZjxxItemDTO> zjxxList);

    /**
     * 更新数据
     * @param mbywmc
     * @param mbbjzjxxList
     * @param mbbjsjList
     */
    void updateToMbTable(@Param("mbywmc") String mbywmc, @Param("mbbjzjxxList") List<MbbjzjxxItemDTO> mbbjzjxxList
            , @Param("mbbjsjList") List<MbbjsjItemDTO> mbbjsjList);

    void qkmbsj(@Param("mbywmc") String mbywmc);
}
