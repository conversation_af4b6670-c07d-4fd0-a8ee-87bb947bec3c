<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.ksyw.mapper.KsPjlsPjkmMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.ksyw.model.domain.KsPjlsPjkm">
        <id column="pjlsbh" jdbcType="VARCHAR" property="pjlsbh" />
        <id column="ksrpjxk" jdbcType="VARCHAR" property="ksrpjxk" />
        <result column="xkmc" jdbcType="VARCHAR" property="xkmc" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        pjlsbh,
        ksrpjxk,
        xkmc

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="pjlsbh != null and pjlsbh != ''">
            AND pjlsbh = #{pjlsbh,jdbcType=VARCHAR}
        </if>
        <if test="ksrpjxk != null and ksrpjxk != ''">
            AND ksrpjxk = #{ksrpjxk,jdbcType=VARCHAR}
        </if>
        <if test="xkmc != null and xkmc != ''">
            AND xkmc = #{xkmc,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="pjlsbh != null ">
            pjlsbh = #{pjlsbh,jdbcType=VARCHAR},
        </if>
        <if test="ksrpjxk != null ">
            ksrpjxk = #{ksrpjxk,jdbcType=VARCHAR},
        </if>
        <if test="xkmc != null ">
            xkmc = #{xkmc,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.ksyw.model.domain.KsPjlsPjkm"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from ks_pjls_pjkm
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
	
	<insert id="batchSavePjlsPjkm" parameterType="com.xcwlkj.ksyw.model.domain.KsPjlsPjkm">
		insert into ks_pjls_pjkm
		( 
			pjlsbh, 
        	ksrpjxk,
        	xkmc
		) VALUES
		 <foreach item="pjlsPjkm" collection="pjlsPjkmList"   separator="," >
		 ( 		  #{pjlsPjkm.pjlsbh}
				 ,#{pjlsPjkm.ksrpjxk}
				 ,#{pjlsPjkm.xkmc} 
		)
	    </foreach>
		
	</insert>
	
</mapper>
