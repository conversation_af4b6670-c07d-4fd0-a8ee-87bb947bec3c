<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.ksyw.kstb.mapper.KsQdjlMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.ksyw.kstb.model.domain.KsQdjl">
        <id column="qdbh" jdbcType="VARCHAR" property="qdbh" />
        <result column="kwrybh" jdbcType="VARCHAR" property="kwrybh" />
        <result column="csrymc" jdbcType="VARCHAR" property="csrymc" />
        <result column="csrysjh" jdbcType="VARCHAR" property="csrysjh" />
        <result column="ccm" jdbcType="VARCHAR" property="ccm" />
        <result column="kmmc" jdbcType="VARCHAR" property="kmmc" />
        <result column="qdsj" jdbcType="TIMESTAMP" property="qdsj" />
        <result column="qdlx" jdbcType="VARCHAR" property="qdlx" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        qdbh,
        kwrybh,
        csrymc,
        csrysjh,
        ccm,
        kmmc,
        qdsj,
        qdlx

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="qdbh != null and qdbh != ''">
            AND qdbh = #{qdbh,jdbcType=VARCHAR}
        </if>
        <if test="kwrybh != null and kwrybh != ''">
            AND kwrybh = #{kwrybh,jdbcType=VARCHAR}
        </if>
        <if test="csrymc != null and csrymc != ''">
            AND csrymc = #{csrymc,jdbcType=VARCHAR}
        </if>
        <if test="csrysjh != null and csrysjh != ''">
            AND csrysjh = #{csrysjh,jdbcType=VARCHAR}
        </if>
        <if test="ccm != null and ccm != ''">
            AND ccm = #{ccm,jdbcType=VARCHAR}
        </if>
        <if test="kmmc != null and kmmc != ''">
            AND kmmc = #{kmmc,jdbcType=VARCHAR}
        </if>
        <if test="qdsj != null and qdsj != ''">
            AND qdsj = #{qdsj,jdbcType=TIMESTAMP}
        </if>
        <if test="qdlx != null and qdlx != ''">
            AND qdlx = #{qdlx,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="qdbh != null ">
            qdbh = #{qdbh,jdbcType=VARCHAR},
        </if>
        <if test="kwrybh != null ">
            kwrybh = #{kwrybh,jdbcType=VARCHAR},
        </if>
        <if test="csrymc != null ">
            csrymc = #{csrymc,jdbcType=VARCHAR},
        </if>
        <if test="csrysjh != null ">
            csrysjh = #{csrysjh,jdbcType=VARCHAR},
        </if>
        <if test="ccm != null ">
            ccm = #{ccm,jdbcType=VARCHAR},
        </if>
        <if test="kmmc != null ">
            kmmc = #{kmmc,jdbcType=VARCHAR},
        </if>
        <if test="qdsj != null ">
            qdsj = #{qdsj,jdbcType=TIMESTAMP},
        </if>
        <if test="qdlx != null ">
            qdlx = #{qdlx,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.ksyw.kstb.model.domain.KsQdjl"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from ks_qdjl
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
