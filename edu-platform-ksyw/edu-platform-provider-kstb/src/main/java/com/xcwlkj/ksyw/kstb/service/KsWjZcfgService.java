/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.kstb.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.kstb.model.domain.KsWjZcfg;

import tk.mybatis.mapper.entity.Example;

/**
 * 考试违纪政策法规服务
 * 
 * <AUTHOR>
 * @version $Id: KsWjZcfgService.java, v 0.1 2020年08月19日 09时42分 xcwlkj.com Exp $
 */
@Service
public interface KsWjZcfgService {

	/**
	 * 单表根据条件查询
	 * 
	 * @param example
	 * @return
	 */
	List<KsWjZcfg> selectByExample(Example example);

	/**
	 * 根据主键更新
	 * 
	 * @param uuid
	 */
	void updateByKey(KsWjZcfg ksWjZcfg);

	/**
	 * 新增
	 * 
	 * @param ksWjZcfg
	 */
	void insert(KsWjZcfg ksWjZcfg);

	/**
	 * 查询全部
	 */
	List<KsWjZcfg> selectAll();

	/**
	 * [条件更新]
	 *
	 * @param example
	 * @return
	 */
	int updateByExampleSelective(KsWjZcfg ksWjZcfg, Example example);

	/**
	 * 查询单个实体
	 * 
	 * @param ksWjZcfg
	 * @return
	 */
	KsWjZcfg selectOne(KsWjZcfg ksWjZcfg);

	/**
	 * 根据类型统计数量
	 * 
	 * @param example
	 * @return
	 */
	int selectCountByExample(Example example);

}