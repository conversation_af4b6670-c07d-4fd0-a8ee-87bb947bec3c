/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.ksyw;

//import com.xcwlkj.ksyw.data.model.domain.KdKqxx;
//import com.xcwlkj.ksyw.data.service.KdKdxxService;
//import com.xcwlkj.ksyw.data.service.KdKqxxService;
//import com.xcwlkj.ksyw.data.service.KsBpxxService;
//import com.xcwlkj.ksyw.exceptions.KsywBusiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 
 * <AUTHOR>
 * @version $Id: ThirdChannelApplicationTests.java, v 0.1 2018年8月29日 下午9:50:49 danfeng.zhou Exp $
 */
//@RunWith(SpringRunner.class)
//@SpringBootTest
public class MbCacheOperateTests {
    protected final Logger        logger = LoggerFactory.getLogger(this.getClass());

//    @Autowired
//    private MbCacheOperateService mbCacheOperateService;
//
//    @Autowired
//    private RedisUtil             redisUtil;
//    @Autowired
//    private KsJtsjzsService jtsjzsService;
//
//    @Autowired
//    private KdKqxxService kqxxService;
//
//    @Autowired
//    private KsBpxxService ksbpService;
//    @Autowired
//    private KdKdxxService kdxxService;
//
//   // @Test
//    public static void main(String[] args){
//        for(int i=10;i<=2021;i++){
//            String iToS=String.valueOf(i);
//            StringBuffer buffer=new StringBuffer();
//            buffer.append(iToS);
//            String fzStr=buffer.reverse().toString();
//            if(iToS.equals(fzStr)){
//                System.out.println("+++++一样的:"+i);
//            }
//        }
//    }
//    //@Test
//    public void atomic(){
//        Map<String,String> map= Maps.newHashMap();
//        KsJtsjzs jts=new KsJtsjzs();
//        jts.setBh(IdGenerateUtil.generateId());
//        jts.setBbh("0.1");
//        jts.setKsjhbh("1022202101");
//        map=JSONObject.parseObject(JSON.toJSONString(jts),Map.class);
//        System.out.println(map.toString());
//        for(String key:map.keySet()){
//            System.out.println("++++++++++++++key:"+key);
//            System.out.println("+++++++++++++++value:"+map.get(key));
//        }
//        /**
//        AtomicInteger atomicInteger=new AtomicInteger(10);
//        for(int i=0;i<10;i++){
//            atomicInteger.getAndAdd(1);
//            System.out.println("+++累加:"+atomicInteger.get());
//        }
//         **/
//    }
//    /**
//     * 导入考生籍贯信息
//     */
//    //@Test
//    public void drksjgxx(){
//        String ksjhbh="1022202101";
//        String filePath="D:\\sj.xlsx";
//        String sheetName="1";
//        ExcelData data=new ExcelData(filePath,sheetName);
//        //data.assembleKsjgData(ksjhbh);
//        data.assembleJg(ksjhbh);
//    }
//    class ExcelData{
//        private XSSFSheet sheet;
//        ExcelData(String filePath,String sheetName){
//            FileInputStream fileInputStream=null;
//            try{
//                fileInputStream=new FileInputStream(filePath);
//                XSSFWorkbook sheets=new XSSFWorkbook(fileInputStream);
//                sheet=sheets.getSheet(sheetName);
//            }catch(Exception e){
//                e.printStackTrace();
//            }
//        }
//
//        public String getExcelDataByIndex(int row,int column){
//            XSSFRow row1=sheet.getRow(row);
//            String cell=row1.getCell(column).toString();
//            return cell;
//        }
//
//        public String getCellByCaseName(String caseName,int currentColumn,int targetColumn){
//            String operateSteps="";
//            int rows=sheet.getPhysicalNumberOfRows();
//            for(int i=0;i<rows;i++){
//                XSSFRow row=sheet.getRow(i);
//                String cell=row.getCell(currentColumn).toString();
//                if(cell.equals(caseName)){
//                    operateSteps=row.getCell(targetColumn).toString();
//                    break;
//                }
//            }
//            return operateSteps;
//        }
//
//        public void readExcelData(){
//            int rows=sheet.getPhysicalNumberOfRows();
//            for(int i=0;i<rows;i++){
//                XSSFRow row=sheet.getRow(i);
//                int columns=row.getPhysicalNumberOfCells();
//                for(int j=0;j<columns;j++){
//                    String cell=row.getCell(j).toString();
//                    System.out.println(cell);
//                }
//            }
//        }
//
//        /**
//         * 组装考生性别数据
//         * @param ksjhbh
//         */
//        public void assembleKsxbData(String ksjhbh){
//            int rows=sheet.getPhysicalNumberOfRows();
//            int totalNx=0;
//            int totalNv=0;
//            int totalZs=0;
//            for(int i=0;i<rows;i++){
//                XSSFRow row=sheet.getRow(i);
//                int columns=row.getPhysicalNumberOfCells();
//                //考区编号
//                String kqbh=row.getCell(0).toString();
//                //考区名称
//                String kqmc=row.getCell(1).toString();
//                //男性数量
//                String nxsl=row.getCell(2).toString();
//                //女性数量
//                String nvsl=row.getCell(3).toString();
//                //行政编码
//                String xzbm=row.getCell(4).toString();
//                KsJtsjzs jtsjzs=new KsJtsjzs();
//                jtsjzs.setBh(IdGenerateUtil.generateId());
//                jtsjzs.setBbh("0.1");
//                jtsjzs.setKsjhbh(ksjhbh);
//                jtsjzs.setCcm(null);
//                jtsjzs.setGlcsbh(kqbh);
//                jtsjzs.setSjlx("ksxb");
//                jtsjzs.setNr1(nxsl);
//                totalNx=totalNx+Integer.valueOf(nxsl);
//                jtsjzs.setNr2(nvsl);
//                totalNv=totalNv+Integer.valueOf(nvsl);
//                jtsjzs.setNr3(kqmc);
//                int zs=Integer.valueOf(nxsl)+Integer.valueOf(nvsl);
//                totalZs=totalZs+zs;
//                jtsjzs.setNr4(String.valueOf(zs));
//                jtsjzs.setNr5(xzbm);
//                jtsjzsService.insert(jtsjzs);
//            }
//            KsJtsjzs js=new KsJtsjzs();
//            js.setBh(IdGenerateUtil.generateId());
//            js.setBbh("0.1");
//            js.setKsjhbh(ksjhbh);
//            js.setCcm(null);
//            js.setGlcsbh("00");
//            js.setSjlx("ksxb");
//            js.setNr1(String.valueOf(totalNx));
//            js.setNr2(String.valueOf(totalNv));
//            js.setNr3("浙江");
//            js.setNr4(String.valueOf(totalZs));
//            js.setNr5("330000");
//            jtsjzsService.insert(js);
//        }
//        /**
//         * 组装考生学历报考比
//         * @param ksjhbh
//         */
//        public void assembleKsxlData(String ksjhbh){
//            int rows=sheet.getPhysicalNumberOfRows();
//            for(int i=0;i<rows;i++){
//                XSSFRow row=sheet.getRow(i);
//                int columns=row.getPhysicalNumberOfCells();
//                //地区
//                String xl=row.getCell(0).toString();
//                //数量
//                String sl=row.getCell(1).toString();
//                KsJtsjzs jtsjzs=new KsJtsjzs();
//                jtsjzs.setBh(IdGenerateUtil.generateId());
//                jtsjzs.setBbh("0.1");
//                jtsjzs.setKsjhbh(ksjhbh);
//                jtsjzs.setCcm(null);
//                jtsjzs.setGlcsbh("00");
//                jtsjzs.setSjlx("ksxlbkb");
//                jtsjzs.setNr1(xl);
//                jtsjzs.setNr2(sl);
//                jtsjzs.setNr3("浙江");
//                jtsjzs.setNr5("330000");
//                jtsjzsService.insert(jtsjzs);
//            }
//        }
//
//        public void assembleJg(String ksjhbh){
//            int rows=sheet.getPhysicalNumberOfRows();
//            for(int i=0;i<rows;i++){
//                XSSFRow row=sheet.getRow(i);
//                int columns=row.getPhysicalNumberOfCells();
//                //地区
//                String dq=row.getCell(0).toString();
//                //数量
//                String sl=row.getCell(1).toString();
//                System.out.println("+++++++++++++地区:"+dq);
//                System.out.println("+++++++++++++人数:"+sl);
//            }
//        }
//        /**
//         * 组装考生籍贯数据
//         */
//        public void assembleKsjgData(String ksjhbh){
//            int rows=sheet.getPhysicalNumberOfRows();
//            for(int i=0;i<rows;i++){
//                XSSFRow row=sheet.getRow(i);
//                int columns=row.getPhysicalNumberOfCells();
//                //地区
//                String dq=row.getCell(0).toString();
//                //数量
//                String sl=row.getCell(1).toString();
//                KsJtsjzs jtsjzs=new KsJtsjzs();
//                jtsjzs.setBh(IdGenerateUtil.generateId());
//                jtsjzs.setBbh("0.1");
//                jtsjzs.setKsjhbh(ksjhbh);
//                jtsjzs.setCcm(null);
//                jtsjzs.setGlcsbh("00");
//                jtsjzs.setSjlx("ksjgbkb");
//                jtsjzs.setNr1(dq);
//                jtsjzs.setNr2(sl);
//                jtsjzs.setNr3("浙江");
//                jtsjzs.setNr5("330000");
//                jtsjzsService.insert(jtsjzs);
//            }
//        }
//    }
//
//   // @Test
//    public void contextLoads() {
//
//        //        List<Map<Object, Object>>  result = mbCacheOperateService.queryMbsjList("jy_kskmdmb:GK");
//        //        System.out.println(result);
//
//        String keyString = "sfhy:1011202001:12:330100:10101:F";
//
//        Set<String> result = redisUtil.sGetStr(keyString);
//        
//        for (Object key : result) {
//            System.out.println(key.toString());
//        }
//        
//        //        List<String> value = new ArrayList<String>();
//        //        value.add("330121200305061025:330121200305061025");
//        //        value.add("330121200305061055:330121200305061055");
//        //        redisUtil.lSet(keyString, value);
//        //        
//        //        
//        //        Set<Object> result = redisUtil.sGet(keyString);
//        //        for (Object object : result) {   
//        //        System.out.println(object.toString());
//        //        
//
//    }
//
//    /**
//     * 组装考生入场统计数据
//     */
//    @Test
//    public void formatKsrcxxtj(){
//        String ksjhbh="1004202104";
//        String[] ccArray=new String[]{"11","12","21","22"};
//        for(int i=0;i<ccArray.length;i++){
//            String ccm=ccArray[i];
//            //考生人数map
//            ConcurrentHashMap<String, Integer> ksrsParam = new ConcurrentHashMap<String, Integer>();
//            //核验人数map
//            ConcurrentHashMap<String, Integer> hyrsParam = new ConcurrentHashMap<String, Integer>();
//            KsJtsjzs ksJtsjzs = new KsJtsjzs();
//            ksJtsjzs.setBh(IdGenerateUtil.generateId());
//            ksJtsjzs.setKsjhbh(ksjhbh);
//            ksJtsjzs.setCcm(ccm);
//            ksJtsjzs.setSjlx(KsJtsjlxEunm.KSRCTJSJ.getCode());
//            ksJtsjzs.setGlcsbh("00");
//            Example example0 = new Example(KsJtsjzs.class);
//            Example.Criteria createCriteria = example0.createCriteria();
//            createCriteria.andEqualTo("ksjhbh", ksjhbh);
//            createCriteria.andEqualTo("ccm", ccm);
//            createCriteria.andEqualTo("sjlx", KsJtsjlxEunm.KSRCTJSJ.getCode());
//            List<KsJtsjzs> selectByExample = jtsjzsService.selectByExample(example0);
//            if(CollectionUtils.isNotEmpty(selectByExample)){
//                jtsjzsService.removeByKeys(example0);
//            }
//            List<KdKqxx> kqList=kqxxService.selectKqxxByksjhbh(ksjhbh);
//            if(CollectionUtils.isEmpty(kqList)) {
//                throw new KsywBusiException("get kqlist is null");
//            }else {
//                int ywrsCount=0;//义乌人数
//                for(KdKqxx temp:kqList) {
//                    if(!StringUtils.equals("00", temp.getKqbh())) {
//                        // 根据考区编号去查询对应的人数
//                        Example example1 = new Example(KsBpxx.class);
//                        Example.Criteria kscriteria = example1.createCriteria();
//                        kscriteria.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
//                        kscriteria.andEqualTo("ccm", ccm);
//                        kscriteria.andEqualTo("ksjhbh", ksjhbh);
//                        kscriteria.andLike("kqbh", temp.getKqbh() + "%");
//                        int selectCountByExample = ksbpService.selectCountByExample(example1);
//                        if(StringUtils.equals("1295",temp.getKqbh())) {
//                            //义乌
//                            ywrsCount=selectCountByExample;
//                        }
//                        ksrsParam.put(temp.getKqbh(),selectCountByExample);
//                    }else {
//                        //统计全省的人数
//                        Example example1 = new Example(KsBpxx.class);
//                        Example.Criteria kscriteria = example1.createCriteria();
//                        kscriteria.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
//                        kscriteria.andEqualTo("ccm", ccm);
//                        kscriteria.andEqualTo("ksjhbh", ksjhbh);
//                        int selectCountByExample = ksbpService.selectCountByExample(example1);
//                        ksrsParam.put("00",selectCountByExample);
//                    }
//                    hyrsParam.put(temp.getKqbh(), 0);//初始化核验人数
//                }
//                int jhcount=ksrsParam.get("07");//金华人数
//                ksrsParam.put("07", ywrsCount+jhcount);//金华的总人数需要加上义乌的数据
//            }
//            JSONArray nr5array = new JSONArray();
//            for (String key : ksrsParam.keySet()) {
//                com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
//                jsonObject.put("kszsl", ksrsParam.get(key));
//                jsonObject.put("hytgsl", 0);
//                jsonObject.put("kqbh", key);
//                nr5array.add(jsonObject);
//            }
//            //添加考点的总数 add by xuaz @2021-06-05
//            //考生人数map
//            ConcurrentHashMap<String, Integer> kdxxRsParam = new ConcurrentHashMap<String, Integer>();
//            List<String> kdxxList=kdxxService.selectDistinctKdByKsjhbh(ksjhbh);
//            if(!CollectionUtils.isEmpty(kdxxList)) {
//                for(String kdTemp:kdxxList) {
//                    Example kdexample = new Example(KsBpxx.class);
//                    Example.Criteria kscriteria = kdexample.createCriteria();
//                    kscriteria.andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
//                    kscriteria.andEqualTo("ccm", ccm);
//                    kscriteria.andEqualTo("ksjhbh", ksjhbh);
//                    kscriteria.andEqualTo("bzhkdbid",kdTemp);
//                    int kdRsCountByExample=0;
//                    kdRsCountByExample = ksbpService.selectCountByExample(kdexample);
//                    kdxxRsParam.put(kdTemp, kdRsCountByExample);
//                }
//                for(String kdxxKey:kdxxRsParam.keySet()) {
//                    com.alibaba.fastjson.JSONObject kdjsonObject = new com.alibaba.fastjson.JSONObject();
//                    kdjsonObject.put("kszsl", kdxxRsParam.get(kdxxKey));
//                    kdjsonObject.put("hytgsl", 0);
//                    kdjsonObject.put("kqbh", kdxxKey);
//                    nr5array.add(kdjsonObject);
//                }
//            }
//            System.out.println("----------------------------");
//            System.out.println(nr5array.toJSONString());
//            ksJtsjzs.setNr5(nr5array.toJSONString());
//            jtsjzsService.insert(ksJtsjzs);
//        }
//    }

}
