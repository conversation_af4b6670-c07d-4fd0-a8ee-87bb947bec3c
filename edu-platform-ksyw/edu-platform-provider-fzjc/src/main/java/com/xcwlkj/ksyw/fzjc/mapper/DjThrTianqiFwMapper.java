/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.fzjc.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.ksyw.fzjc.model.domain.DjThrTianqiFw;



/**
 * 待获取的天气的城市数据库操作
 * <AUTHOR>
 * @version $Id: InitDjThrTianqiFwMapper.java, v 0.1 2020年06月29日 18时52分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface DjThrTianqiFwMapper extends MyMapper<DjThrTianqiFw> {

    /**
	 * 分页查询待获取的天气的城市
	 * 
	 * @param example
	 * @return
	 */
	List<DjThrTianqiFw> pageList(DjThrTianqiFw example);
}
