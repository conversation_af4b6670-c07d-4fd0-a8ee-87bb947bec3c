/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.fzjc.web;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.gxdj.model.enums.DataSynchEventTypeEnum;
import com.xcwlkj.ksyw.data.model.bo.SjtbjgDpBO;
import com.xcwlkj.ksyw.data.model.domain.KdKcxx;
import com.xcwlkj.ksyw.data.model.domain.KdKdxx;
import com.xcwlkj.ksyw.data.model.domain.KdKqxx;
import com.xcwlkj.ksyw.data.model.domain.KsKsjh;
import com.xcwlkj.ksyw.data.model.dos.RsptBksrDO;
import com.xcwlkj.ksyw.data.service.*;
import com.xcwlkj.ksyw.fzjc.model.domain.KsJtsjtjDp;
import com.xcwlkj.ksyw.fzjc.service.KsJtsjtjDpService;
import com.xcwlkj.ksyw.ksrc.model.dos.SjdbtjDO;
import com.xcwlkj.ksyw.ksrc.service.KsDistributeStatusService;
import com.xcwlkj.ksyw.ksrc.service.KsKsxxExportStatusService;
import com.xcwlkj.ksyw.model.dto.fzjc.*;
import com.xcwlkj.ksyw.model.vo.fzjc.*;
import com.xcwlkj.ksyw.resource.model.domain.CsBzhkd;
import com.xcwlkj.ksyw.resource.model.dos.KsgljgXqDO;
import com.xcwlkj.ksyw.resource.model.dos.RsptKdsdrsDO;
import com.xcwlkj.ksyw.resource.service.CsBzhkdService;
import com.xcwlkj.ksyw.resource.service.CsKsgljgService;
import com.xcwlkj.ksyw.service.FzjcXcmcFeignApi;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.ArrayUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Fzjc接口
 *
 * <AUTHOR>
 * @version $Id: FzjcXcmcFeignClient.java, v 0.1 2020年07月01日 17时34分 xcwlkj.com Exp $
 */
@RestController
public class FzjcXcmcFeignClient extends BaseFeignClient implements FzjcXcmcFeignApi {

    @Autowired
    private KsJtsjzsService ksJtsjzsService;

    @Autowired
    private KdKqxxService kdKqxxService;
    @Autowired
    private KdKdxxService kdKdxxService;
    @Resource
    private KdKcxxService kdKcxxService;
    @Autowired
    private KsBpxxService ksBpxxService;
    @Autowired
    private KsJtsjtjDpService ksJtsjtjDpService;
    @Resource
    private SjtbSjtbjgService sjtbSjtbjgService;
    @Resource
    private KsKsxxExportStatusService ksKsxxExportStatusService;
    @Resource
    private KsDistributeStatusService ksDistributeStatusService;
    @Resource
    private CsKsgljgService csKsgljgService;
    @Resource
    private CsBzhkdService csBzhkdService;
    @Resource
    private KsKsjhService ksKsjhService;
    @Value("${xc.statistics.pkgException.PageSize:10}")
    private int pkgExceptionPageSize;

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzs(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsDTO)
     */
    @Override
    public Wrapper<DpsjzsVO> dpsjzs(@RequestBody @Validated DpsjzsDTO dto) {
        logger.info("大屏统计数据展示DpsjzsDTO={}", dto);
        DpsjzsVO result = new DpsjzsVO();
        //获取天情况
        TqqkVO tqqkVO = ksJtsjzsService.getTqqk(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());
        //获取考试时间安排表
        KssjapVO kssjapVO = ksJtsjzsService.getKssjap(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());
        //获取设备使用情况列表
        List<SbqklbItemVO> sbqklb = ksJtsjzsService.getSbqklb(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());
        //考生情况信息
        KsqkxxVO ksqkxxVO = ksJtsjzsService.getKsqkxx(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());
        //工作人员情况
        List<GzryqkItemVO> gzryqk = ksJtsjzsService.getGzryqk(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());
        //场所信息情况
        List<CsxxqkItemVO> csxxqk = ksJtsjzsService.getCsxxqk(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());
        //新高考组合列表
        List<XgkzhlbItemVO> xgkzhlb = ksJtsjzsService.getXgkzhlb(dto.getKqbh(), dto.getKsjhbh(), dto.getCcm());

        result.setTqqk(tqqkVO);
        result.setKssjap(kssjapVO);
        result.setSbqklb(sbqklb);
        result.setKsqkxx(ksqkxxVO);
        result.setGzryqk(gzryqk);
        result.setCsxxqk(csxxqk);
        result.setXgkzhlb(xgkzhlb);
        logger.info("dpsjzs - 大屏统计数据展示. [OK] DpsjzsVO={}", result);
        return WrapMapper.ok(result);
    }


    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsFp(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsFpDTO)
     */
    @Override
    public Wrapper<DpsjzsFpVO> dpsjzsFp(@RequestBody @Validated DpsjzsFpDTO dto) {
        logger.info("大屏统计数据展示-副屏DpsjzsFpDTO={}", dto);
//		String kqbh = "00";
//		String ksjhbh = "1001202001";
        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        DpsjzsFpVO result = new DpsjzsFpVO();
        //场所信息情况
        List<CsxxqkItemVO> csxxqk = ksJtsjzsService.getCsxxqk(kqbh, ksjhbh, null);
        if (CollectionUtils.isNotEmpty(csxxqk)) {
            csxxqk.remove(0);
        }
        //新高考组合列表
        List<XgkzhlbItemVO> xgkzhlb = ksJtsjzsService.getXgkzhlb(kqbh, ksjhbh, null);
        /** 考生信息详情列表 */
        List<KsxxxqlbItemVO> ksxxxqlb = ksJtsjzsService.getKsxxxqlb(kqbh, ksjhbh, null);
        /** 设备列表 */
        List<SblbItemVO> sblb = ksJtsjzsService.getSblb(kqbh, ksjhbh, null);
        /** 工作人员列表 */
        List<GzrylbItemVO> gzrylb = ksJtsjzsService.getGzrylb(kqbh, ksjhbh, null);

        result.setCsxxqk(csxxqk);
        result.setXgkzhlb(xgkzhlb);
        result.setKsxxxqlb(ksxxxqlb);
        result.setSblb(sblb);
        result.setGzrylb(gzrylb);
        logger.info("dpsjzsFp - 大屏统计数据展示-副屏. [OK] DpsjzsFpVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsKsxbsl(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsKsxbslDTO)
     */
    @Override
    public Wrapper<DpsjzsKsxbslVO> dpsjzsKsxbsl(@RequestBody @Validated DpsjzsKsxbslDTO dto) {
        logger.info("大屏统计数据展示-考生性别数量DpsjzsKsxbslDTO={}", dto);
        DpsjzsKsxbslVO result = new DpsjzsKsxbslVO();
        // TODO 具体的业务实现  by xcwlkj.com
        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        List<KsxbsjItemVO> ksxbsjList = new ArrayList<KsxbsjItemVO>();
        if (StringUtils.isBlank(ccm)) {
            ksxbsjList = ksJtsjzsService.dpsjzsKsxbsl(kqbh, ksjhbh, null);
        } else {

            ksxbsjList = ksJtsjzsService.dpsjzsKsxbsl(kqbh, ksjhbh, ccm);
        }
        result.setKsxbsj(ksxbsjList);

        logger.info("dpsjzsKsxbsl - 大屏统计数据展示-考生性别数量. [OK] DpsjzsKsxbslVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsFpcsxxqkVO> dpsjzsFpcsxxqk(DpsjzsFpcsxxqkDTO dto) {
        logger.info("大屏统计数据展示-副屏-场所信息情况 DpsjzsFpDTO={}", dto);
        DpsjzsFpcsxxqkVO result = new DpsjzsFpcsxxqkVO();

        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        List<CsxxqkItemVO> csxxqklist = ksJtsjzsService.getCsxxqk(kqbh, ksjhbh, ccm);

        //场所信息情况
        if (CollectionUtils.isNotEmpty(csxxqklist) && csxxqklist.size() > 1) {
            csxxqklist.remove(0);
        }

        //父地区场所信息情况列表
        KdKqxx kdKqxx = kdKqxxService.selectByKey(kqbh, ksjhbh);
        String kqmc = kdKqxx.getKqmc();

        int kdsl = 0;
        int kcsl = 0;
        int bmssl = 0;
        int kqsl = 0;
//		List<FkqcsxxqklbItemVO> fkqcsxxqk = ksJtsjzsService.getfkqcsxxqk(kqbh, ksjhbh, ccm);
//		if (CollectionUtils.isNotEmpty(fkqcsxxqk)) {
//			FkqcsxxqklbItemVO fkqcsxxqklbItemVO = fkqcsxxqk.get(0);
//			if (fkqcsxxqklbItemVO != null) {
//				kdsl = fkqcsxxqklbItemVO.getKdsl();
//				kcsl = fkqcsxxqklbItemVO.getKcsl();
//				bmssl = fkqcsxxqklbItemVO.getBmssl();a
//				kqsl = fkqcsxxqklbItemVO.getKqsl();
//			}
//		}

        // 改为从子数据列表中 进行数据汇总
        for (CsxxqkItemVO csxxqkItemVO : csxxqklist) {
            kdsl += str2int(csxxqkItemVO.getKdsl());
            kcsl += str2int(csxxqkItemVO.getKcsl());
            bmssl += str2int(csxxqkItemVO.getBmssl());
            kqsl += str2int(csxxqkItemVO.getKqsl());
        }
        result.setCsxxqk(csxxqklist);
        result.setFdqmc(kqmc);
        result.setFdqkdsl("" + kdsl);
        result.setFdqkcsl("" + kcsl);
        result.setFdqbmssl("" + bmssl);
        result.setFdqkqsl("" + kqsl);

        logger.info("dpsjzsFp - 大屏统计数据展示-副屏-场所信息情况. [OK] DpsjzsFpVO={}", result);
        return WrapMapper.ok(result);
    }

    int str2int(String intString) {
        try {
            return Integer.parseInt(intString, 10);
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsFpksxxxqlb(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsFpksxxxqlbDTO)
     */
    @Override
    public Wrapper<DpsjzsFpksxxxqlbVO> dpsjzsFpksxxxqlb(@RequestBody @Validated DpsjzsFpksxxxqlbDTO dto) {
        logger.info("大屏统计数据展示-副屏_考生信息详情DpsjzsFpksxxxqlbDTO={}", dto);
        DpsjzsFpksxxxqlbVO result = new DpsjzsFpksxxxqlbVO();

        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        List<KsxxxqlbItemVO> ksxxxqlb = new ArrayList<KsxxxqlbItemVO>();
        /** 考生信息详情列表 */
        if (StringUtils.isBlank(ccm)) {
            ksxxxqlb = ksJtsjzsService.getKsxxxqlb(kqbh, ksjhbh, null);

        } else {
            ksxxxqlb = ksJtsjzsService.getKsxxxqlb(kqbh, ksjhbh, ccm);
        }


        result.setKsxxxqlb(ksxxxqlb);

        logger.info("dpsjzsFpksxxxqlb - 大屏统计数据展示-副屏_考生信息详情. [OK] DpsjzsFpksxxxqlbVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsFpsblb(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsFpsblbDTO)
     */
    @Override
    public Wrapper<DpsjzsFpsblbVO> dpsjzsFpsblb(@RequestBody @Validated DpsjzsFpsblbDTO dto) {
        logger.info("大屏统计数据展示-副屏_设备列表DpsjzsFpsblbDTO={}", dto);
        DpsjzsFpsblbVO result = new DpsjzsFpsblbVO();

        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        /** 设备列表 */
        List<SblbItemVO> sbqklb = ksJtsjzsService.getSblb(kqbh, ksjhbh, ccm);

        //父考区设备情况列表
        KdKqxx kdKqxx = kdKqxxService.selectByKey(kqbh, ksjhbh);
        String sjkqbh = kdKqxx.getSjkqbh();
        String fkqmc = kdKqxx.getKqmc();

        List<FkqsbqklbItemVO> fkqsbqklb = ksJtsjzsService.getfkqsbqklb(kqbh, ksjhbh, ccm);

        result.setSblb(sbqklb);
        result.setFkqmc(fkqmc);
        result.setFkqsbqklb(fkqsbqklb);

        logger.info("dpsjzsFpsblb - 大屏统计数据展示-副屏_设备列表. [OK] DpsjzsFpsblbVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsFpgzrylb(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsFpgzrylbDTO)
     */
    @Override
    public Wrapper<DpsjzsFpgzrylbVO> dpsjzsFpgzrylb(@RequestBody @Validated DpsjzsFpgzrylbDTO dto) {
        logger.info("大屏统计数据展示-副屏_工作人员DpsjzsFpgzrylbDTO={}", dto);
        DpsjzsFpgzrylbVO result = new DpsjzsFpgzrylbVO();

        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();

        /** 工作人员列表 */
        List<GzrylbItemVO> gzrylb = ksJtsjzsService.getGzrylb(kqbh, ksjhbh, ccm);

        result.setGzrylb(gzrylb);

        logger.info("dpsjzsFpgzrylb - 大屏统计数据展示-副屏_工作人员. [OK] DpsjzsFpgzrylbVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsFpgzrylbfltj(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsFpgzrylbfltjDTO)
     */
    @Override
    public Wrapper<DpsjzsFpgzrylbfltjVO> dpsjzsFpgzrylbfltj(@RequestBody @Validated DpsjzsFpgzrylbfltjDTO dto) {
        logger.info("大屏统计数据展示-副屏_工作人员分类统计DpsjzsFpgzrylbfltjDTO={}", dto);
        DpsjzsFpgzrylbfltjVO result = new DpsjzsFpgzrylbfltjVO();

//        String ccm = dto.getCcm();
        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();


        List<FpgzrylbfltjItemVO> fpgzrylbfltj = new ArrayList<FpgzrylbfltjItemVO>();
        // 地区类别
        List<FlsjItemVO> fltjdqlbGzrylb = ksJtsjzsService.fltjdqlbGzrylb(kqbh, ksjhbh, null);

        FpgzrylbfltjItemVO dqlbItemVO = new FpgzrylbfltjItemVO();
        dqlbItemVO.setFlmc("dqlb");
        dqlbItemVO.setFlsj(fltjdqlbGzrylb);
        List<FlsjItemVO> flsjItemVOS = ksJtsjzsService.fltjdqlbfkqGzrylb(kqbh, ksjhbh, null);
        if (CollectionUtils.isNotEmpty(flsjItemVOS)) {
            FlsjItemVO fkqdqlbxx = flsjItemVOS.get(0);
            if (fkqdqlbxx != null) {
                String sl = fkqdqlbxx.getSl();
                String mc = fkqdqlbxx.getMc();
                dqlbItemVO.setFjmc(mc);
                dqlbItemVO.setFjsl(sl);
                fpgzrylbfltj.add(dqlbItemVO);
            }
        }

        /// todo: 此处硬编码，需要改为 从 缓存中获取  zhangrj  20210605  
        // 职责类别

        FlsjItemVO[] flsjItemVOArray = new FlsjItemVO[5];//{};

        List<FlsjItemVO> fltjzzlbGzrylb = ksJtsjzsService.fltjzzlbGzrylb(kqbh, ksjhbh, null);
        logger.info("大屏统计数据展示-副屏_工作人员分类统计   fltjzzlbGzrylb={}", fltjzzlbGzrylb);

        for (int i = 0; i < fltjzzlbGzrylb.size(); i++) {
            FlsjItemVO flsjItemVO = fltjzzlbGzrylb.get(i);
            if ("主考".equals(flsjItemVO.getMc())) {
                flsjItemVOArray[0] = flsjItemVO;
            } else if (flsjItemVO.getMc().contains("副主考")) {
                flsjItemVOArray[1] = flsjItemVO;
            }
//           else if( flsjItemVO.getMc().contains("监考教师"))
            else if ("监考教师".equals(flsjItemVO.getMc()) || "监考老师".equals(flsjItemVO.getMc())
                    || "监考员".equals(flsjItemVO.getMc()) || "场内监考员".equals(flsjItemVO.getMc())) {
                flsjItemVOArray[2] = flsjItemVO;
            } else if (flsjItemVO.getMc().contains("视频监考员")) {
                flsjItemVOArray[3] = flsjItemVO;
            } else if (flsjItemVO.getMc().contains("其他")) {
                flsjItemVOArray[4] = flsjItemVO;
            }
            ;
        }

        fltjzzlbGzrylb = ArrayUtil.toList(flsjItemVOArray);

        FpgzrylbfltjItemVO zzlbItemVO = new FpgzrylbfltjItemVO();
        zzlbItemVO.setFlmc("zzlb");
        zzlbItemVO.setFlsj(fltjzzlbGzrylb);
        fpgzrylbfltj.add(zzlbItemVO);

        logger.info("大屏统计数据展示-副屏_工作人员分类统计   fltjzzlbGzrylb={}", fltjzzlbGzrylb);

        result.setFpgzrylbfltj(fpgzrylbfltj);

        logger.info("dpsjzsFpgzrylbfltj - 大屏统计数据展示-副屏_工作人员分类统计. [OK] DpsjzsFpgzrylbfltjVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsKsfltj(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsKsfltjDTO)
     */
    @Override
    public Wrapper<DpsjzsKsfltjVO> dpsjzsKsfltj(@RequestBody @Validated DpsjzsKsfltjDTO dto) {
        logger.info("大屏统计数据展示-考生分类统计DpsjzsKsfltjDTO={}", dto);
        String ccm = dto.getCcm();
        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();

        DpsjzsKsfltjVO result = ksJtsjzsService.dpsjzsKsfltj(kqbh, ksjhbh, ccm);

        logger.info("dpsjzsKsfltj - 大屏统计数据展示-考生分类统计. [OK] DpsjzsKsfltjVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsHqtqlqkssl(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsHqtqlqksslDTO)
     */
    @Override
    public Wrapper<DpsjzsHqtqlqksslVO> dpsjzsHqtqlqkssl(@RequestBody @Validated DpsjzsHqtqlqksslDTO dto) {
        logger.info("大屏统计数据展示_提前录取的考生数量DpsjzsHqtqlqksslDTO={}", dto);
        DpsjzsHqtqlqksslVO result = new DpsjzsHqtqlqksslVO();

        String ccm = dto.getCcm();
        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();

        TqlqsltjVO tqlqsltjVO = ksJtsjzsService.tqlqkssl(kqbh, ksjhbh, ccm);
        result.setTqlqsltj(tqlqsltjVO);

        logger.info("dpsjzsHqtqlqkssl - 大屏统计数据展示_提前录取的考生数量. [OK] DpsjzsHqtqlqksslVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsKkjswcl(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsKkjswclDTO)
     */
    @Override
    public Wrapper<DpsjzsKkjswclVO> dpsjzsKkjswcl(@RequestBody @Validated DpsjzsKkjswclDTO dto) {
        logger.info("大屏统计数据展示-开考结束完成率DpsjzsKkjswclDTO={}", dto);
        DpsjzsKkjswclVO result = new DpsjzsKkjswclVO();

        String ccm = dto.getCcm();
        String kqbh = dto.getKqbh();
        String ksjhbh = dto.getKsjhbh();

        List<String> kqbhes = new ArrayList<>();
        if (StringUtils.equals("00", kqbh) || StringUtils.equals("S45", kqbh)) {
            //查询下级考区编号
            List<KdKqxx> kdKqxxes = kdKqxxService.selectChildKqxxRs(ksjhbh, kqbh);
            List<String> xjkqbhs = kdKqxxes.stream().map(KdKqxx::getKqbh).collect(Collectors.toList());
            kqbhes.addAll(xjkqbhs);
            for (String xjkqbh : xjkqbhs) {
                List<KdKqxx> xjkdKqxxes = kdKqxxService.selectChildKqxxRs(ksjhbh, xjkqbh);
                if (CollectionUtils.isNotEmpty(xjkdKqxxes)) {
                    List<String> kqbhs = xjkdKqxxes.stream().map(KdKqxx::getKqbh).collect(Collectors.toList());
                    kqbhes.addAll(kqbhs);
                }
            }
        } else {
            //查询下级考区编号
            List<KdKqxx> kdKqxxes = kdKqxxService.selectChildKqxxRs(ksjhbh, kqbh);
            kqbhes = kdKqxxes.stream().map(KdKqxx::getKqbh).collect(Collectors.toList());
            //加入自身
            kqbhes.add(kqbh);

        }

        Example Example = new Example(KdKdxx.class);
        Example.createCriteria()
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andIn("kqbh", kqbhes);

        //总数量
        int zslCount = kdKdxxService.selectCountByExample(Example);

        Example.clear();
        Example.createCriteria()
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("kdkkzt", ScztEnum.DEL.getCode())
                .andIn("kqbh", kqbhes);

        //开考完成数量
        int ykkCount = kdKdxxService.selectCountByExample(Example);

        Example.clear();
        Example.createCriteria()
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("ccm", ccm)
                .andEqualTo("kdksjszt", ScztEnum.DEL.getCode())
                .andIn("kqbh", kqbhes);

        //考试结束数量
        int ksjsCount = kdKdxxService.selectCountByExample(Example);

        KsxbsjVO ksxbsjVO = new KsxbsjVO();
        ksxbsjVO.setZsl(zslCount);
        ksxbsjVO.setJswcs(ksjsCount);
        ksxbsjVO.setKkwcs(ykkCount);

        result.setKsxbsj(ksxbsjVO);


        logger.info("dpsjzsKkjswcl - 大屏统计数据展示-开考结束完成率. [OK] DpsjzsKkjswclVO={}", result);
        return WrapMapper.ok(result);
    }

    /**
     * @see com.xcwlkj.ksyw.service.FzjcXcmcFeignApi#dpsjzsKsbksdsl(com.xcwlkj.ksyw.model.dto.fzjc.DpsjzsKsbksdslDTO)
     */
    @Override
    public Wrapper<DpsjzsKsbksdslVO> dpsjzsKsbksdsl(@RequestBody @Validated DpsjzsKsbksdslDTO dto) {
        logger.info("大屏统计数据展示-考试报考实到数量DpsjzsKsbksdslDTO={}", dto);
        DpsjzsKsbksdslVO result = new DpsjzsKsbksdslVO();

        String ksjhbh = dto.getKsjhbh();
        String ccm = dto.getCcm();
        String kqbh = dto.getKqbh();

        List<String> kqbhList = new ArrayList<String>();
        if (StringUtils.equals("00", kqbh) || StringUtils.equals("S45", kqbh)) {
            //查询下级考区编号
            List<KdKqxx> kdKqxxes = kdKqxxService.selectChildKqxxRs(ksjhbh, kqbh);
            kqbhList = kdKqxxes.stream().map(KdKqxx::getKqbh).collect(Collectors.toList());
        } else {
            kqbhList.add(kqbh);
        }

        List<RsptBksrDO> rsptBksrDOList = ksBpxxService.selectBkrstj(ksjhbh, ccm, kqbhList);

        List<KsbksdslItemVO> ksbksdslItemVOS = new ArrayList<KsbksdslItemVO>();
        for (RsptBksrDO rsptBksrDO : rsptBksrDOList) {
            KsbksdslItemVO ksbksdslItemVO = new KsbksdslItemVO();
            RsptKdsdrsDO rsptKdsdrsDO = kdKdxxService.selectKdsdrs(ksjhbh, ccm, rsptBksrDO.getKqbh());
            KdKqxx kdKqxx = kdKqxxService.selectByKey(rsptBksrDO.getKqbh(), ksjhbh);
            ksbksdslItemVO.setBksl(rsptBksrDO.getBksl());
            ksbksdslItemVO.setKqmc(kdKqxx.getKqmc().substring(2));
            ksbksdslItemVO.setKqbh(rsptBksrDO.getKqbh());
            ksbksdslItemVO.setSdsl(rsptKdsdrsDO.getKdsdrs());
            ksbksdslItemVOS.add(ksbksdslItemVO);
        }
        result.setKsbksdslList(ksbksdslItemVOS);

        logger.info("dpsjzsKsbksdsl - 大屏统计数据展示-考试报考实到数量. [OK] DpsjzsKsbksdslVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsKdkcslVO> dpsjzsKdkcsl(@RequestBody @Validated DpsjzsKdkcslDTO dto) {
        logger.info("大屏统计数据展示-考点考场数量DpsjzsKdkcslDTO={}", dto);
        DpsjzsKdkcslVO result = new DpsjzsKdkcslVO();

        List<DpsjzsKdkcslItemVO> itemVOList = new ArrayList<>();
        Example kdxxExample = new Example(KdKdxx.class);
        kdxxExample.setDistinct(true);
        kdxxExample.setCountProperty("bzhkdid");
        kdxxExample.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andEqualTo("ksjhbh", dto.getKsjhbh());
        int kdcount = kdKdxxService.selectCountByExample(kdxxExample);
        DpsjzsKdkcslItemVO kdsl = new DpsjzsKdkcslItemVO();
        kdsl.setLxdm("KDZS");
        kdsl.setLxmc("考点总数");
        kdsl.setSl(kdcount);
        itemVOList.add(kdsl);

        Example kcxxExample = new Example(KdKcxx.class);
        kcxxExample.setDistinct(true);
        kcxxExample.setCountProperty("bzhkcid");
        kcxxExample.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andEqualTo("ksjhbh", dto.getKsjhbh());
        int kcCount = kdKcxxService.selectCountByExample(kcxxExample);
        DpsjzsKdkcslItemVO qykcsl = new DpsjzsKdkcslItemVO();
        qykcsl.setLxdm("QYKCS");
        qykcsl.setLxmc("启用考场数");
        qykcsl.setSl(kcCount);
        itemVOList.add(qykcsl);

        Example example = new Example(KsJtsjtjDp.class);
        example.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", dto.getKsjhbh()).andEqualTo("sjfl", "KCWGZXLX");
        KsJtsjtjDp kcwgzxlx = ksJtsjtjDpService.selectOneByExample(example);
        if (null != kcwgzxlx && StringUtils.isNotBlank(kcwgzxlx.getBz())) {
            JSONArray jsonArray = JSONArray.parseArray(kcwgzxlx.getBz());
            DpsjzsKdkcslItemVO kdkcslItemVO;
            for (Object o : jsonArray) {
                kdkcslItemVO = new DpsjzsKdkcslItemVO();
                JSONObject jsonObject = (JSONObject) o;
                kdkcslItemVO.setLxdm(jsonObject.getString("LXDM"));
                kdkcslItemVO.setLxmc(jsonObject.getString("LXMC"));
                kdkcslItemVO.setSl(jsonObject.getInteger("LXSL"));
                itemVOList.add(kdkcslItemVO);
            }
        } else {
            DpsjzsKdkcslItemVO kcwgzxItemVO = new DpsjzsKdkcslItemVO();
            kcwgzxItemVO.setLxdm("KCWGZX");
            kcwgzxItemVO.setLxmc("考场网关在线数");
            kcwgzxItemVO.setSl(0);
            itemVOList.add(kcwgzxItemVO);
            DpsjzsKdkcslItemVO kcwglxItemVO = new DpsjzsKdkcslItemVO();
            kcwglxItemVO.setLxdm("KCWGLX");
            kcwglxItemVO.setLxmc("考场网关离线数");
            kcwglxItemVO.setSl(0);
            itemVOList.add(kcwglxItemVO);
        }
        result.setKdkcslList(itemVOList);
        logger.info("dpsjzsKdkcsl - 大屏统计数据展示-考点考场数量. [OK] DpsjzsKdkcslVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsSjtbqkVO> dpsjzsSjtbqk(@RequestBody @Validated DpsjzsSjtbqkDTO dto) {
        logger.info("大屏统计数据展示-数据同步情况DpsjzsSjtbqkDTO={}", dto);
        DpsjzsSjtbqkVO result = new DpsjzsSjtbqkVO();
        List<SjtbjgDpBO> dpBOList = sjtbSjtbjgService.selectDpsjByKsjh(dto.getKsjhbh());
        List<DpsjzsSjtbqkItemVO> itemVOList = new ArrayList<>();
        DpsjzsSjtbqkItemVO sjtbqkItemVO;
        for (SjtbjgDpBO bo : dpBOList) {
            sjtbqkItemVO = new DpsjzsSjtbqkItemVO();
            sjtbqkItemVO.setTblxdm(bo.getSjdm());
            sjtbqkItemVO.setTblxmc(DataSynchEventTypeEnum.get(bo.getSjdm()).getSjlxmc());
            sjtbqkItemVO.setTbsjsl(Integer.parseInt(bo.getYxjls()));
            sjtbqkItemVO.setTbsj(DateUtil.format(bo.getKssj(), DateUtil.DEFAULT_DATE_TIME));
            itemVOList.add(sjtbqkItemVO);
        }
        result.setSjtbqkList(itemVOList);
        logger.info("dpsjzsSjtbqk - 大屏统计数据展示-数据同步情况. [OK] DpsjzsSjtbqkVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsSjxfycVO> dpsjzsSjxfyc(@RequestBody @Validated DpsjzsSjxfycDTO dto) {
        logger.info("大屏统计数据展示-数据下发异常DpsjzsSjxfycDTO={}", dto);
        DpsjzsSjxfycVO result = new DpsjzsSjxfycVO();
        Example example = new Example(KsJtsjtjDp.class);
        example.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", dto.getKsjhbh()).andEqualTo("sjfl", "SJXFYC");
        List<KsJtsjtjDp> list = ksJtsjtjDpService.selectByExample(example);
        List<DpsjzsSjxfycItemVO> itemVOList = new ArrayList<>();
        DpsjzsSjxfycItemVO sjxfycItemVO;
        for (KsJtsjtjDp jtsjtj : list) {
            sjxfycItemVO = new DpsjzsSjxfycItemVO();
            sjxfycItemVO.setDsmc(jtsjtj.getDsmc());
            sjxfycItemVO.setQxmc(jtsjtj.getXqmc());
            sjxfycItemVO.setKdmc(jtsjtj.getBzhkdmc());
            sjxfycItemVO.setKcmc(jtsjtj.getBzhkcmc());
            sjxfycItemVO.setYcms(jtsjtj.getBz());
            itemVOList.add(sjxfycItemVO);
        }
        result.setSjxfycList(itemVOList);
        logger.info("dpsjzsSjxfyc - 大屏统计数据展示-数据下发异常. [OK] DpsjzsSjxfycVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsSjxftjVO> dpsjzsSjxftj(@RequestBody @Validated DpsjzsSjxftjDTO dto) {
        logger.info("大屏统计数据展示-数据下发统计DpsjzsSjxftjDTO={}", dto);
        DpsjzsSjxftjVO result = new DpsjzsSjxftjVO();
        Example example = new Example(KsJtsjtjDp.class);
        example.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("sjfl", "SJXFTJ").andEqualTo("glcsbh", dto.getKqbh());
        List<KsJtsjtjDp> list = ksJtsjtjDpService.selectByExample(example);
        List<DpsjzsSjxftjItemVO> itemVOList = new ArrayList<>();
        DpsjzsSjxftjItemVO sjxftjItemVO;
        for (KsJtsjtjDp jtsjtj : list) {
            sjxftjItemVO = new DpsjzsSjxftjItemVO();
            String glcsbhjb = jtsjtj.getGlcsbhjb();
            String csbh = null;
            String csmc = null;
            if (StringUtils.equals("1", glcsbhjb)) {
                csbh = jtsjtj.getDsdm();
                csmc = jtsjtj.getDsmc();
            } else if (StringUtils.equals("2", glcsbhjb)) {
                csbh = jtsjtj.getXqdm();
                csmc = jtsjtj.getXqmc();
            } else if (StringUtils.equals("3", glcsbhjb)) {
                csbh = jtsjtj.getBzhkdid();
                csmc = jtsjtj.getBzhkdmc();
            }
            sjxftjItemVO.setCsbh(csbh);
            sjxftjItemVO.setCsmc(csmc);
            int xfkczs = 0, yxfkcs = 0, wxfkcs = 0, xfyckcs = 0;
            if (StringUtils.isNotBlank(jtsjtj.getBz())) {
                JSONObject jsonObject = JSONObject.parseObject(jtsjtj.getBz());
                xfkczs = Integer.parseInt(jsonObject.getString("XFZS"));
                yxfkcs = Integer.parseInt(jsonObject.getString("YXFKCS"));
                wxfkcs = Integer.parseInt(jsonObject.getString("WXFKCS"));
                xfyckcs = Integer.parseInt(jsonObject.getString("XFYCKCS"));
            }
            sjxftjItemVO.setXfkczs(xfkczs);
            sjxftjItemVO.setYxfkcs(yxfkcs);
            sjxftjItemVO.setWxfkcs(wxfkcs);
            sjxftjItemVO.setXfyckcs(xfyckcs);
            itemVOList.add(sjxftjItemVO);
        }
        result.setSjxftjList(itemVOList);
        logger.info("dpsjzsSjxftj - 大屏统计数据展示-数据下发统计. [OK] DpsjzsSjxftjVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsSjdbtjVO> dpsjzsSjdbtj(@RequestBody @Validated DpsjzsSjdbtjDTO dto) {
        logger.info("大屏统计数据展示-数据打包统计DpsjzsSjdbtjDTO={}", dto);
        DpsjzsSjdbtjVO result = new DpsjzsSjdbtjVO();

        if (StringUtils.equals(dto.getKqjbbs(), "Z1") || StringUtils.equals(dto.getKqjbbs(), "Z2")) {
            result.setSjdbtjList(ksJtsjtjDpService.dpsjzsSjdbtjKdBykq(dto));
        } else if (StringUtils.equals(dto.getKqjbbs(), "Z3")) {
            result.setSjdbtjList(ksJtsjtjDpService.dpsjzsSjdbtjKdByXq(dto));
        }

        logger.info("dpsjzsSjdbtj - 大屏统计数据展示-数据打包统计. [OK] DpsjzsSjdbtjVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsSjdbycVO> dpsjzsSjdbyc(@RequestBody @Validated DpsjzsSjdbycDTO dto) {
        logger.info("大屏统计数据展示-数据打包异常DpsjzsSjdbycDTO={}", dto);
        DpsjzsSjdbycVO result = new DpsjzsSjdbycVO();
        List<DpsjzsSjdbycItemVO> itemVOList = new ArrayList<>();

        Map<String, String> exceptionDescMap = ksKsxxExportStatusService.getExportExceptionDesc(dto.getKsjhbh());

        if (exceptionDescMap.size() > 0) {

            Set<String> bzhkdidList = exceptionDescMap.keySet();

            Example emBzhkd = new Example(CsBzhkd.class);
            emBzhkd.createCriteria()
                    .andIn("bzhkdid", bzhkdidList);
            emBzhkd.selectProperties("bzhkdid", "bzhkdmc", "ksgljgbzm");
            Map<String, CsBzhkd> csBzhkdMap = csBzhkdService.selectByExample(emBzhkd).stream().collect(Collectors.toMap(CsBzhkd::getBzhkdid, Function.identity()));

            Map<String, KsgljgXqDO> ksgljgXqDOMap = csKsgljgService.getXqKsgljg().stream().collect(Collectors.toMap(KsgljgXqDO::getXqdm, Function.identity()));

            for (String bzhkdid : bzhkdidList) {
                CsBzhkd csBzhkd = csBzhkdMap.getOrDefault(bzhkdid, new CsBzhkd());
                DpsjzsSjdbycItemVO dpsjzsSjdbycItemVO = new DpsjzsSjdbycItemVO();

                KsgljgXqDO ksgljgXqDO = ksgljgXqDOMap.get(csBzhkd.getKsgljgbzm());
                if (ksgljgXqDO != null) {
                    dpsjzsSjdbycItemVO.setDsmc(ksgljgXqDO.getDsmc());
                    dpsjzsSjdbycItemVO.setQxmc(ksgljgXqDO.getXqmc());
                }
                dpsjzsSjdbycItemVO.setKdmc(csBzhkd.getBzhkdmc());
                dpsjzsSjdbycItemVO.setYcms(exceptionDescMap.get(bzhkdid));
                itemVOList.add(dpsjzsSjdbycItemVO);
            }

        }
        result.setSjdbycList(itemVOList);
        logger.info("dpsjzsSjdbyc - 大屏统计数据展示-数据打包异常. [OK] DpsjzsSjdbycVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<DpsjzsKdsjdbxfxqVO> dpsjzsKdsjdbxfxq(@RequestBody @Validated DpsjzsKdsjdbxfxqDTO dto) {
        logger.info("大屏统计数据展示-考点数据打包下发详情DpsjzsKdsjdbxfxqDTO={}", dto);
        DpsjzsKdsjdbxfxqVO result = new DpsjzsKdsjdbxfxqVO();
        Example example = new Example(KsJtsjtjDp.class);
        example.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("sjfl", "SJXFTJ").andEqualTo("bzhkdid", dto.getKdbh());
        KsJtsjtjDp ksJtsjtjDp = ksJtsjtjDpService.selectOneByExample(example);

        DpsjzsSjxftjItemVO sjxfxqItemVO = new DpsjzsSjxftjItemVO();
        sjxfxqItemVO.setCsbh(ksJtsjtjDp.getBzhkdid());
        sjxfxqItemVO.setCsmc(ksJtsjtjDp.getBzhkdmc());

        int xfkczs = 0, yxfkcs = 0, wxfkcs = 0, xfyckcs = 0;
        if (StringUtils.isNotBlank(ksJtsjtjDp.getBz())) {
            JSONObject jsonObject = JSONObject.parseObject(ksJtsjtjDp.getBz());
            xfkczs = Integer.parseInt(jsonObject.getString("XFZS"));
            yxfkcs = Integer.parseInt(jsonObject.getString("YXFKCS"));
            wxfkcs = Integer.parseInt(jsonObject.getString("WXFKCS"));
            xfyckcs = Integer.parseInt(jsonObject.getString("XFYCKCS"));
        }
        sjxfxqItemVO.setXfkczs(xfkczs);
        sjxfxqItemVO.setYxfkcs(yxfkcs);
        sjxfxqItemVO.setWxfkcs(wxfkcs);
        sjxfxqItemVO.setXfyckcs(xfyckcs);
        result.setSjxfxqItemVO(sjxfxqItemVO);

        DpsjzsSjdbtjItemVO sjdbxqItemVO = new DpsjzsSjdbtjItemVO();
        sjdbxqItemVO.setCsbh(ksJtsjtjDp.getBzhkdid());
        sjdbxqItemVO.setCsmc(ksJtsjtjDp.getBzhkdmc());

        List<SjdbtjDO> sjdbtjDOS = ksKsxxExportStatusService.countCompleteKdByKd(dto.getKsjhbh(), null, Arrays.asList(dto.getKdbh()));

        sjdbxqItemVO.setDbkds(sjdbtjDOS.get(0).getDbkds());
        sjdbxqItemVO.setZkds(sjdbtjDOS.get(0).getZkds());
        result.setSjdbxqItemVO(sjdbxqItemVO);
        logger.info("dpsjzsKdsjdbxfxq - 大屏统计数据展示-考点数据打包下发详情. [OK] DpsjzsKdsjdbxfxqVO={}", result);
        return WrapMapper.ok(result);
    }

    @Override
    public Wrapper<Void> dpsjtjclSjxftj() {
        logger.info("dpsjtjclSjxftj - 大屏统计数据处理-数据下发情况统计");
        List<KsKsjh> ksjhList = getDefaultKsjh();
        if (CollectionUtils.isEmpty(ksjhList)) {
            return WrapMapper.ok();
        }
        for (KsKsjh ksjh : ksjhList) {
            ksJtsjtjDpService.sjxftj(ksjh.getKsjhbh(), null);
        }
        logger.info("dpsjtjclSjxftj - 大屏统计数据处理-数据下发情况统计. [OK]");
        return WrapMapper.ok();
    }

    @Override
    public Wrapper<Void> dpsjtjclSjxfyc() {
        logger.info("大屏统计数据处理-数据下发异常");
        List<KsKsjh> ksjhList = getDefaultKsjh();
        if (CollectionUtils.isEmpty(ksjhList)) {
            return WrapMapper.ok();
        }
        for (KsKsjh ksjh : ksjhList) {
            ksJtsjtjDpService.dpsjtjclSjxfyc(ksjh.getKsjhbh());
        }
        logger.info("dpsjtjclSjxfyc - 大屏统计数据处理-数据下发异常. [OK]");
        return WrapMapper.ok();
    }

    @Override
    public Wrapper<Void> dpsjclKcwgZxLxSl() {
        logger.info("大屏统计数据处理-考场网关在线离线数量");
        List<KsKsjh> ksjhList = getDefaultKsjh();
        if (CollectionUtils.isEmpty(ksjhList)) {
            return WrapMapper.ok();
        }
        for (KsKsjh ksjh : ksjhList) {
            ksJtsjtjDpService.dpsjclKcwgZxLxSl(ksjh.getKsjhbh());
        }
        logger.info("dpsjclKcwgZxLxSl - 大屏统计数据处理-考场网关在线离线数量. [OK]");
        return WrapMapper.ok();
    }

    private List<KsKsjh> getDefaultKsjh() {
        Example example = new Example(KsKsjh.class);
        example.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode()).andEqualTo("sfmrksjh", "1");
        return ksKsjhService.selectByExample(example);
    }
}