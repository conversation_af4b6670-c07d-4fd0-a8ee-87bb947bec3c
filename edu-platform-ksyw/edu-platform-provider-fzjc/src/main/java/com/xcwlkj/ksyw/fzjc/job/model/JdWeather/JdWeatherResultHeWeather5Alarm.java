package com.xcwlkj.ksyw.fzjc.job.model.JdWeather;

import java.util.List;

import lombok.Data;

@Data
public class JdWeatherResultHeWeather5Alarm
{
    public String level ;//"蓝色",
    public String stat ;//"预警中",
    public String title ;//"天津市气象台发布大风蓝色预警",
    public String txt ;//"预计今天后半夜到明天夜间我区将受大风影响，平均风力达到6级，阵风7级以上。请有关单位和人员作好防范准备",
    public String type ;//"大风"

    public String sender; //"北京市气象局",
    public String senderCode; //"110000",
    public String startTime; //"2021-06-01T17:15+08:00",
    public String endTime; // "2021-06-02T17:15+08:00",
    List<String> originalImpactedAdcodeList; //[ "110000",    "110108",    "110105",]
}
