<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.ksyw.data.mapper.DjWfpjkryxxMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.ksyw.data.model.domain.DjWfpjkryxx">
        <id column="jkryxxbm" jdbcType="VARCHAR" property="jkryxxbm" />
        <result column="sfzh" jdbcType="VARCHAR" property="sfzh" />
        <result column="xm" jdbcType="VARCHAR" property="xm" />
        <result column="xb" jdbcType="VARCHAR" property="xb" />
        <result column="lxdh" jdbcType="VARCHAR" property="lxdh" />
        <result column="bzlx" jdbcType="VARCHAR" property="bzlx" />
        <result column="gzdw" jdbcType="VARCHAR" property="gzdw" />
        <result column="xl" jdbcType="VARCHAR" property="xl" />
        <result column="zc" jdbcType="VARCHAR" property="zc" />
        <result column="csxk" jdbcType="VARCHAR" property="csxk" />
        <result column="cjjygzsj" jdbcType="VARCHAR" property="cjjygzsj" />
        <result column="ydwzw" jdbcType="VARCHAR" property="ydwzw" />
        <result column="bcksdrzw" jdbcType="VARCHAR" property="bcksdrzw" />
        <result column="bcksjkkdmc" jdbcType="VARCHAR" property="bcksjkkdmc" />
        <result column="dykcjkfzr" jdbcType="VARCHAR" property="dykcjkfzr" />
        <result column="sfcsgjkgz" jdbcType="VARCHAR" property="sfcsgjkgz" />
        <result column="bcksjkkdbh" jdbcType="VARCHAR" property="bcksjkkdbh" />
        <result column="ksjhbh" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="sbr" jdbcType="VARCHAR" property="sbr" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        jkryxxbm,
        sfzh,
        xm,
        xb,
        lxdh,
        bzlx,
        gzdw,
        xl,
        zc,
        csxk,
        cjjygzsj,
        ydwzw,
        bcksdrzw,
        bcksjkkdmc,
        dykcjkfzr,
        sfcsgjkgz,
        bcksjkkdbh,
        ksjhbh,
        sbr,
        sczt,
        update_time

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="jkryxxbm != null and jkryxxbm != ''">
            AND jkryxxbm = #{jkryxxbm,jdbcType=VARCHAR}
        </if>
        <if test="sfzh != null and sfzh != ''">
            AND sfzh = #{sfzh,jdbcType=VARCHAR}
        </if>
        <if test="xm != null and xm != ''">
            AND xm = #{xm,jdbcType=VARCHAR}
        </if>
        <if test="xb != null and xb != ''">
            AND xb = #{xb,jdbcType=VARCHAR}
        </if>
        <if test="lxdh != null and lxdh != ''">
            AND lxdh = #{lxdh,jdbcType=VARCHAR}
        </if>
        <if test="bzlx != null and bzlx != ''">
            AND bzlx = #{bzlx,jdbcType=VARCHAR}
        </if>
        <if test="gzdw != null and gzdw != ''">
            AND gzdw = #{gzdw,jdbcType=VARCHAR}
        </if>
        <if test="xl != null and xl != ''">
            AND xl = #{xl,jdbcType=VARCHAR}
        </if>
        <if test="zc != null and zc != ''">
            AND zc = #{zc,jdbcType=VARCHAR}
        </if>
        <if test="csxk != null and csxk != ''">
            AND csxk = #{csxk,jdbcType=VARCHAR}
        </if>
        <if test="cjjygzsj != null and cjjygzsj != ''">
            AND cjjygzsj = #{cjjygzsj,jdbcType=VARCHAR}
        </if>
        <if test="ydwzw != null and ydwzw != ''">
            AND ydwzw = #{ydwzw,jdbcType=VARCHAR}
        </if>
        <if test="bcksdrzw != null and bcksdrzw != ''">
            AND bcksdrzw = #{bcksdrzw,jdbcType=VARCHAR}
        </if>
        <if test="bcksjkkdmc != null and bcksjkkdmc != ''">
            AND bcksjkkdmc = #{bcksjkkdmc,jdbcType=VARCHAR}
        </if>
        <if test="dykcjkfzr != null and dykcjkfzr != ''">
            AND dykcjkfzr = #{dykcjkfzr,jdbcType=VARCHAR}
        </if>
        <if test="sfcsgjkgz != null and sfcsgjkgz != ''">
            AND sfcsgjkgz = #{sfcsgjkgz,jdbcType=VARCHAR}
        </if>
        <if test="bcksjkkdbh != null and bcksjkkdbh != ''">
            AND bcksjkkdbh = #{bcksjkkdbh,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND ksjhbh = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="sbr != null and sbr != ''">
            AND sbr = #{sbr,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="jkryxxbm != null ">
            jkryxxbm = #{jkryxxbm,jdbcType=VARCHAR},
        </if>
        <if test="sfzh != null ">
            sfzh = #{sfzh,jdbcType=VARCHAR},
        </if>
        <if test="xm != null ">
            xm = #{xm,jdbcType=VARCHAR},
        </if>
        <if test="xb != null ">
            xb = #{xb,jdbcType=VARCHAR},
        </if>
        <if test="lxdh != null ">
            lxdh = #{lxdh,jdbcType=VARCHAR},
        </if>
        <if test="bzlx != null ">
            bzlx = #{bzlx,jdbcType=VARCHAR},
        </if>
        <if test="gzdw != null ">
            gzdw = #{gzdw,jdbcType=VARCHAR},
        </if>
        <if test="xl != null ">
            xl = #{xl,jdbcType=VARCHAR},
        </if>
        <if test="zc != null ">
            zc = #{zc,jdbcType=VARCHAR},
        </if>
        <if test="csxk != null ">
            csxk = #{csxk,jdbcType=VARCHAR},
        </if>
        <if test="cjjygzsj != null ">
            cjjygzsj = #{cjjygzsj,jdbcType=VARCHAR},
        </if>
        <if test="ydwzw != null ">
            ydwzw = #{ydwzw,jdbcType=VARCHAR},
        </if>
        <if test="bcksdrzw != null ">
            bcksdrzw = #{bcksdrzw,jdbcType=VARCHAR},
        </if>
        <if test="bcksjkkdmc != null ">
            bcksjkkdmc = #{bcksjkkdmc,jdbcType=VARCHAR},
        </if>
        <if test="dykcjkfzr != null ">
            dykcjkfzr = #{dykcjkfzr,jdbcType=VARCHAR},
        </if>
        <if test="sfcsgjkgz != null ">
            sfcsgjkgz = #{sfcsgjkgz,jdbcType=VARCHAR},
        </if>
        <if test="bcksjkkdbh != null ">
            bcksjkkdbh = #{bcksjkkdbh,jdbcType=VARCHAR},
        </if>
        <if test="ksjhbh != null ">
            ksjhbh = #{ksjhbh,jdbcType=VARCHAR},
        </if>
        <if test="sbr != null ">
            sbr = #{sbr,jdbcType=VARCHAR},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.ksyw.data.model.domain.DjWfpjkryxx"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from dj_wfpjkryxx
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
