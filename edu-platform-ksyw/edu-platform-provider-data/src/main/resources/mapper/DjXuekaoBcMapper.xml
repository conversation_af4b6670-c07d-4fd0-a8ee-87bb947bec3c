<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.ksyw.data.mapper.DjXuekaoBcMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.ksyw.data.model.domain.DjXuekaoBc">
        <result column="xm" jdbcType="VARCHAR" property="xm" />
        <result column="zkzh" jdbcType="VARCHAR" property="zkzh" />
        <result column="bmd" jdbcType="VARCHAR" property="bmd" />
        <result column="kd_h" jdbcType="VARCHAR" property="kdH" />
        <result column="kc_h" jdbcType="VARCHAR" property="kcH" />
        <result column="zw_h" jdbcType="VARCHAR" property="zwH" />
        <result column="kslx_h" jdbcType="VARCHAR" property="kslxH" />
        <result column="kszl_h" jdbcType="VARCHAR" property="kszlH" />
        <result column="kszl_mc" jdbcType="VARCHAR" property="kszlMc" />
        <result column="ds_h" jdbcType="BIGINT" property="dsH" />
        <result column="ds_mc" jdbcType="VARCHAR" property="dsMc" />
        <result column="xq_h" jdbcType="BIGINT" property="xqH" />
        <result column="xq_mc" jdbcType="VARCHAR" property="xqMc" />
        <result column="kd_mc" jdbcType="VARCHAR" property="kdMc" />
        <result column="kc_hc" jdbcType="VARCHAR" property="kcHc" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        xm,
        zkzh,
        bmd,
        kd_h,
        kc_h,
        zw_h,
        kslx_h,
        kszl_h,
        kszl_mc,
        ds_h,
        ds_mc,
        xq_h,
        xq_mc,
        kd_mc,
        kc_hc

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="xm != null and xm != ''">
            AND xm = #{xm,jdbcType=VARCHAR}
        </if>
        <if test="zkzh != null and zkzh != ''">
            AND zkzh = #{zkzh,jdbcType=VARCHAR}
        </if>
        <if test="bmd != null and bmd != ''">
            AND bmd = #{bmd,jdbcType=VARCHAR}
        </if>
        <if test="kdH != null and kdH != ''">
            AND kd_h = #{kdH,jdbcType=VARCHAR}
        </if>
        <if test="kcH != null and kcH != ''">
            AND kc_h = #{kcH,jdbcType=VARCHAR}
        </if>
        <if test="zwH != null and zwH != ''">
            AND zw_h = #{zwH,jdbcType=VARCHAR}
        </if>
        <if test="kslxH != null and kslxH != ''">
            AND kslx_h = #{kslxH,jdbcType=VARCHAR}
        </if>
        <if test="kszlH != null and kszlH != ''">
            AND kszl_h = #{kszlH,jdbcType=VARCHAR}
        </if>
        <if test="kszlMc != null and kszlMc != ''">
            AND kszl_mc = #{kszlMc,jdbcType=VARCHAR}
        </if>
        <if test="dsH != null and dsH != ''">
            AND ds_h = #{dsH,jdbcType=BIGINT}
        </if>
        <if test="dsMc != null and dsMc != ''">
            AND ds_mc = #{dsMc,jdbcType=VARCHAR}
        </if>
        <if test="xqH != null and xqH != ''">
            AND xq_h = #{xqH,jdbcType=BIGINT}
        </if>
        <if test="xqMc != null and xqMc != ''">
            AND xq_mc = #{xqMc,jdbcType=VARCHAR}
        </if>
        <if test="kdMc != null and kdMc != ''">
            AND kd_mc = #{kdMc,jdbcType=VARCHAR}
        </if>
        <if test="kcHc != null and kcHc != ''">
            AND kc_hc = #{kcHc,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="xm != null ">
            xm = #{xm,jdbcType=VARCHAR},
        </if>
        <if test="zkzh != null ">
            zkzh = #{zkzh,jdbcType=VARCHAR},
        </if>
        <if test="bmd != null ">
            bmd = #{bmd,jdbcType=VARCHAR},
        </if>
        <if test="kdH != null ">
            kd_h = #{kdH,jdbcType=VARCHAR},
        </if>
        <if test="kcH != null ">
            kc_h = #{kcH,jdbcType=VARCHAR},
        </if>
        <if test="zwH != null ">
            zw_h = #{zwH,jdbcType=VARCHAR},
        </if>
        <if test="kslxH != null ">
            kslx_h = #{kslxH,jdbcType=VARCHAR},
        </if>
        <if test="kszlH != null ">
            kszl_h = #{kszlH,jdbcType=VARCHAR},
        </if>
        <if test="kszlMc != null ">
            kszl_mc = #{kszlMc,jdbcType=VARCHAR},
        </if>
        <if test="dsH != null ">
            ds_h = #{dsH,jdbcType=BIGINT},
        </if>
        <if test="dsMc != null ">
            ds_mc = #{dsMc,jdbcType=VARCHAR},
        </if>
        <if test="xqH != null ">
            xq_h = #{xqH,jdbcType=BIGINT},
        </if>
        <if test="xqMc != null ">
            xq_mc = #{xqMc,jdbcType=VARCHAR},
        </if>
        <if test="kdMc != null ">
            kd_mc = #{kdMc,jdbcType=VARCHAR},
        </if>
        <if test="kcHc != null ">
            kc_hc = #{kcHc,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.ksyw.data.model.domain.DjXuekaoBc"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from dj_xuekao_bc
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
	
	
	<!-- 查询考点信息 -->
	<select id="selectAllKd" parameterType="com.xcwlkj.ksyw.data.model.domain.DjXuekao"
		resultMap="BaseResultMap">
		SELECT DISTINCT DS_H, DS_MC, XQ_H, XQ_MC, KD_H, KD_MC, KSLX_H, KSZL_H, KSZL_MC
		FROM dj_xuekao_bc
		ORDER BY DS_H, XQ_H, KD_H
	</select>

	<!-- 查询考场信息 -->
	<select id="selectAllKc" parameterType="com.xcwlkj.ksyw.data.model.domain.DjXuekao"
		resultMap="BaseResultMap">
		SELECT DISTINCT DS_H, DS_MC, XQ_H, XQ_MC, KD_H, KD_MC, KC_H, KC_HC, KSLX_H, KSZL_H, KSZL_MC
		FROM dj_xuekao_bc
		ORDER BY DS_H, XQ_H, KD_H, KC_HC
	</select>

	<!-- 查询考生报名信息  分页-->
	<select id="pageListUseLimitKsBmxx" parameterType="com.xcwlkj.ksyw.data.model.domain.DjXuekao"
		resultMap="BaseResultMap">
		SELECT DISTINCT ZKZH , XM  
		FROM dj_xuekao_bc
		ORDER BY ZKZH  
		LIMIT #{offSet} , #{limit}   
	</select>

	<!-- 查询考生编排信息 -->
	<select id="pageListUseLimitKsBpxx" parameterType="com.xcwlkj.ksyw.data.model.domain.DjXuekao"
		resultMap="BaseResultMap">
		SELECT DISTINCT DS_H, DS_MC, XQ_H, XQ_MC, KD_H, KD_MC, KC_H, KC_HC, KSLX_H, KSZL_H, KSZL_MC, XM, ZKZH, ZW_H
		FROM dj_xuekao_bc
		ORDER BY DS_H, XQ_H, KD_H, KC_HC
		LIMIT #{offSet} , #{limit}  
	</select>
	
</mapper>
