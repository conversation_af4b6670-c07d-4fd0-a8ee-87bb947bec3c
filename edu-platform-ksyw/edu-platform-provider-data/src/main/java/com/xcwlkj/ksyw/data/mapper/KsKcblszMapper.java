/**
 * xcwlkj.com Inc.
 * Copyright (c) 2021-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.ksyw.data.model.domain.KsKcblsz;



/**
 * ks_kcblsz数据库操作
 * <AUTHOR>
 * @version $Id: InitKsKcblszMapper.java, v 0.1 2021年08月16日 15时32分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsKcblszMapper extends MyMapper<KsKcblsz> {

    /**
	 * 分页查询ks_kcblsz
	 * 
	 * @param example
	 * @return
	 */
	List<KsKcblsz> pageList(KsKcblsz example);
}
