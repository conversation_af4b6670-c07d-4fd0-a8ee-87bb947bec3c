/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;


/**
 * ks_kssjdrmb
 * 
 * <AUTHOR>
 * @version $Id: KsKssjdrmb.java, v 0.1 2020年10月10日 16时59分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "ks_kssjdrmb")
public class KsKssjdrmb implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** uuid */
    @Id
    @Column(name = "uuid")
    private String            uuid;
    /** 模板名称 */
    @Column(name = "mbmc")
    private String            mbmc;
    /** 简要说明 */
    @Column(name = "jysm")
    private String            jysm;
    /** 删除状态 0使用  1删除 */
    @Column(name = "sczt")
    private String            sczt;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


