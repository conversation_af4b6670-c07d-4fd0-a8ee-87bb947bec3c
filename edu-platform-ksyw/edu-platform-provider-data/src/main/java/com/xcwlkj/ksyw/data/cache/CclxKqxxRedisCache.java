package com.xcwlkj.ksyw.data.cache;

import com.xcwlkj.cache.AbstractRedisDataCache;
import com.xcwlkj.ksyw.data.model.cache.kqxx.KqxxQueryParam;
import com.xcwlkj.ksyw.data.model.domain.KdKqxx;
import com.xcwlkj.ksyw.data.service.KdKqxxService;
import com.xcwlkj.model.enums.ScztEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName CclxKqxxRedisCache
 * @Description TODO
 * <AUTHOR>
 * Date 2022-03-03 19:27
 * @Version 1.0
 **/
@Component
@Slf4j
public class CclxKqxxRedisCache extends AbstractRedisDataCache<KqxxQueryParam, List<KdKqxx>> {
    @Resource
    private KdKqxxService kqxxService;

    @Override
    protected long getDuration() {
        return 15 * 24 * 60 * 60;
    }

    @Override
    protected boolean isAutoRefresh() {
        return false;
    }

    @Override
    protected List<KdKqxx> write(KqxxQueryParam key) {
        String ksjhbh = key.getKsjhbh();
        String cclx = key.getCclx();
        Example example = new Example(KdKqxx.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("cclx", cclx)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        List<KdKqxx> kqxxList = kqxxService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(kqxxList)) {
            return kqxxList;
        }
        return null;
    }
}
