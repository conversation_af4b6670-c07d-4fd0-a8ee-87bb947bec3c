/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.data.model.domain.KsPjd;

import tk.mybatis.mapper.entity.Example;



/**
 * 考试评卷点服务
 * <AUTHOR>
 * @version $Id: KsPjdService.java, v 0.1 2020年09月02日 18时34分 xcwlkj.com Exp $
 */
@Service
public interface KsPjdService  {
	
	/**
	 * 单表根据条件查询
	 * @param example
	 * @return
	 */
	List<KsPjd> selectByExample(Example example);
	
	/**
	 * 批量删除评卷点
	 * @param ids
	 */
	void plscPjd(List<String> ids);
	
	/**
	 * 保存
	 * @param ksPjd
	 */
	void save(KsPjd ksPjd);
	
	/**
	 * 根据主键查询
	 * @param pjdbh
	 * @return
	 */
	KsPjd selectByKey(String pjdbh);
	
}