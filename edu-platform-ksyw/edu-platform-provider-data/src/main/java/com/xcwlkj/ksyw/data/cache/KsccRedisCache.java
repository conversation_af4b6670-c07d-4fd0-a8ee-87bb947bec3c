package com.xcwlkj.ksyw.data.cache;

import com.xcwlkj.cache.AbstractRedisDataCache;
import com.xcwlkj.ksyw.data.model.cache.kscc.KmStatObj;
import com.xcwlkj.ksyw.data.model.cache.kscc.KsccQueryParam;
import com.xcwlkj.ksyw.data.model.cache.kscc.KsccStatObj;
import com.xcwlkj.ksyw.data.model.domain.KsKscc;
import com.xcwlkj.ksyw.data.service.KsKsccService;
import com.xcwlkj.model.enums.ScztEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName KsccRedisCache
 * @Description TODO
 * <AUTHOR>
 * Date 2022-02-23 9:18
 * @Version 1.0
 **/
@Component
@Slf4j
public class KsccRedisCache extends AbstractRedisDataCache<KsccQueryParam, KsccStatObj> {
    @Resource
    private KsKsccService ksccService;

    /**
     * 15天后自动失效
     *
     * @return
     */
    @Override
    protected long getDuration() {
        return 15 * 24 * 60 * 60;
    }

    @Override
    protected boolean isAutoRefresh() {
        return false;
    }

    @Override
    protected KsccStatObj write(KsccQueryParam key) {
        KsccStatObj ksccStatObj = new KsccStatObj();
        Example example = new Example(KsKscc.class);
        example.createCriteria().andEqualTo("ksjhbh", key.getKsjhbh())
                .andEqualTo("ccm", key.getCcm()).andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        List<KsKscc> ksccList = ksccService.selectByExample(example);
        if (CollectionUtils.isNotEmpty(ksccList)) {
            ksccStatObj.setKsjhbh(key.getKsjhbh());
            ksccStatObj.setCcm(key.getCcm());
            List<KmStatObj> kmList = new ArrayList<>();
            for (KsKscc kscc : ksccList) {
                KmStatObj km = new KmStatObj();
                km.setKmm(kscc.getKmm());
                km.setKmmc(kscc.getKmmc());
                kmList.add(km);
            }
            ksccStatObj.setCcmc(ksccList.get(0).getCcmc());
            ksccStatObj.setCcbz(ksccList.get(0).getCcbz());
            ksccStatObj.setKmList(kmList);
            ksccStatObj.setKmkssj(ksccList.get(0).getKmkssj());
            ksccStatObj.setKmjssj(ksccList.get(0).getKmjssj());
            ksccStatObj.setJshysjzt(ksccList.get(0).getJshysjzt());
            ksccStatObj.setKstsqktbzt(ksccList.get(0).getKstsqktbzt());
            ksccStatObj.setKctsqktbzt(ksccList.get(0).getKctsqktbzt());
            ksccStatObj.setSfmrcc(ksccList.get(0).getSfmrkscc());
            ksccStatObj.setKsrcsj(ksccList.get(0).getKsrcsj());
            ksccStatObj.setKssbqksj(ksccList.get(0).getKssbqksj());
            ksccStatObj.setYxcdsj(Integer.parseInt(ksccList.get(0).getYxcdsj() != null? ksccList.get(0).getYxcdsj().toString(): "0"));
        }
        return ksccStatObj;
    }
}
