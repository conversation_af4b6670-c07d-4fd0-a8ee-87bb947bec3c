/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.service;

import java.util.List;

import com.xcwlkj.ksyw.model.dto.ksjhgl.KsszxxDTO;
import com.xcwlkj.ksyw.model.vo.ksjhgl.KsszxxVO;
import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.data.model.domain.KsKsjh;
import com.xcwlkj.ksyw.data.model.dos.ExamPlanDO;
import com.xcwlkj.ksyw.model.vo.examinfo.ExamPlanVO;

import tk.mybatis.mapper.entity.Example;



/**
 * 考试计划服务
 * <AUTHOR>
 * @version $Id: KsKsjhService.java, v 0.1 2020年04月10日 00时26分 xcwlkj.com Exp $
 */
@Service
public interface KsKsjhService  {

    /**
     * 根据主键查询
     * 
     * @param ksjhbh
     * @return
     */
	KsKsjh selectByKey(String ksjhbh);

    /**
     * 单表根据条件查询
     * 
     * @param example
     * @return
     */
    List<KsKsjh> selectByExample(Example example);

    /**
     * 根据主键更新
     * 
     * @param entity
     */
    void updateByKey(KsKsjh entity);

    /**
     * 新增
     * 
     * @param entity
     */
    void insert(KsKsjh entity);

    /**
     * 分页查询
     */
    List<KsKsjh> selectByRowBounds(int pageNum, int pageSize);
    
    /**
     * 查询全部
     */
    List<KsKsjh> selectAll();

    /**
     * [条件更新]
     *
     * @param example
     * @return
     */
    int updateByExampleSelective(KsKsjh entity,Example example);
    
    /**
     * 级联删除考试计划以及考试场次
     * @param entity
     * @param example
     * @return
     */
    int deleteKsjhAndKsccByKsjhbh(List<String> ksjhbhList);
    /**
     * 获取对应的数量
     * @param entity
     * @return
     */
    int selectCount(KsKsjh entity);


    List<ExamPlanVO> getExamPlanV1(String ksjhbh, String kssj, String jssj);

    KsKsjh selectOneByExample(Example example);
    
    /**
     * 获取考试计划以及考试场次
     * @param examPlanDO
     * @return
     */
    List<ExamPlanVO> getExamPlanV2(ExamPlanDO examPlanDO);

    KsszxxVO getSzxx(KsszxxDTO dto);
}