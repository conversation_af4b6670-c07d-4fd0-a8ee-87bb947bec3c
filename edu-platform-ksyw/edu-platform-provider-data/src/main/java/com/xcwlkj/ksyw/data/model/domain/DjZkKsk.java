/**
 * xcwlkj.com Inc.
 * Copyright (c) 2020-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.model.domain;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Id;
import java.io.Serializable;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import lombok.Data;
import lombok.EqualsAndHashCode;



/**
 * 自考(自学考试)考生库
 * 
 * <AUTHOR>
 * @version $Id: DjZkKsk.java, v 0.1 2020年07月27日 09时46分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "dj_zk_ksk")
public class DjZkKsk implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 序号 */
    @Column(name = "xh")
    private Integer            xh;
    /** 地市 */
    @Column(name = "ds")
    private String            ds;
    /** 考区代码 */
    @Column(name = "kqdm")
    private String            kqdm;
    /** 考区名称 */
    @Column(name = "kqmc")
    private String            kqmc;
    /** 身份证号 */
    @Column(name = "sfzh")
    private String            sfzh;
    /** 准考证号 */
    @Column(name = "zkzh")
    private String            zkzh;
    /** 考生姓名 */
    @Column(name = "ksxm")
    private String            ksxm;
    /** 课程代码 */
    @Column(name = "kcdm")
    private String            kcdm;
    /** 课程名称 */
    @Column(name = "kcmc")
    private String            kcmc;
    /** 考点代码 */
    @Column(name = "kddm")
    private String            kddm;
    /** 考点名称 */
    @Column(name = "kdmc")
    private String            kdmc;
    /** 考场序号 */
    @Column(name = "kcxh")
    private String            kcxh;
    /** 座位号 */
    @Column(name = "zwh")
    private String            zwh;
    /** 考试日期 */
    @Column(name = "ksrq")
    private String            ksrq;
    /** 考试时间号 */
    @Column(name = "kssjh")
    private String            kssjh;
    /** 考试时间 */
    @Column(name = "kssj")
    private String            kssj;
    /** 专业代码 */
    @Column(name = "zydm")
    private String            zydm;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


