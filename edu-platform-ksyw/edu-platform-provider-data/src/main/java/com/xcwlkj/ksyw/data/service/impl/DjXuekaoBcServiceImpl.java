/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.service.impl;

import org.springframework.stereotype.Service;

import com.xcwlkj.ksyw.data.mapper.DjXuekaoBcMapper;
import com.xcwlkj.ksyw.data.model.domain.DjXuekaoBc;
import com.xcwlkj.ksyw.data.service.DjXuekaoBcService;

import java.util.List;

import javax.annotation.Resource;


/**
 * 学考追加数据服务
 * <AUTHOR>
 * @version $Id: DjXuekaoBcServiceImpl.java, v 0.1 2020年07月15日 19时05分 xcwlkj.com Exp $
 */
@Service("djXuekaoBcService")
public class DjXuekaoBcServiceImpl  implements DjXuekaoBcService  {

    @Resource
    private DjXuekaoBcMapper modelMapper;

    @Override
    public List<DjXuekaoBc> selectAllKd() {
        return modelMapper.selectAllKd();
    }

    
    @Override
    public List<DjXuekaoBc> selectAllKc() {
        return modelMapper.selectAllKc();
    }

    
    @Override
    public List<DjXuekaoBc> pageListUseLimitKsBmxx(int offSet, int limit) {
        return modelMapper.pageListUseLimitKsBmxx(offSet, limit);
    }
    
    @Override
    public List<DjXuekaoBc> pageListUseLimitKsBpxx(int offSet, int limit) {
        return modelMapper.pageListUseLimitKsBpxx(offSet, limit);
    }

}