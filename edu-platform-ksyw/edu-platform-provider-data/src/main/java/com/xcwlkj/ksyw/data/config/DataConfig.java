/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.ksyw.data.config;

import com.google.common.collect.Maps;
import com.xcwlkj.base.constant.GlobalConstant;
import com.xcwlkj.core.config.SystemCacheInitConfig;
import com.xcwlkj.core.interceptor.SqlLogInterceptor;
import com.xcwlkj.core.interceptor.service.CheckExceptionInterceptor;
import com.xcwlkj.core.interceptor.service.CheckResultInterceptor;
import com.xcwlkj.core.interceptor.service.ServiceInterceptorBeanPostProcessor;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.util.RedisUtilAdapter;
import lombok.Data;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.RestTemplate;

import java.util.Map;

/**
 * 应用配置信息
 * <AUTHOR>
 * @version $Id: AppConfig.java, v 0.1 2018年12月10日 下午7:18:15 danfeng.zhou Exp $
 */
@Data
@ConfigurationProperties(prefix = GlobalConstant.ROOT_PREFIX)
@Configuration
@ConditionalOnProperty(name="spring.application.name", havingValue = "ksdata-service")
public class DataConfig {

    /**
     * resetTemplate模版
     * @return
     */
    @LoadBalanced
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * sql拦截器
     * @return
     */
    @Bean
    public SqlLogInterceptor sqlLogInterceptor() {
        return new SqlLogInterceptor();
    }
    
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
    
    /**
     * 
     * 远程第三方调用bean
     * @return
     */
    @Bean
    public CheckExceptionInterceptor checkException() {
        return new CheckExceptionInterceptor();
    }

    /**
     * 
     * 远程第三方调用返回结果拦截器bean
     * @return
     */
    @Bean
    public CheckResultInterceptor checkResult() {
        return new CheckResultInterceptor();
    }

    /**
     * 远程拦截服务后置处理器
     * @return
     */
    @Bean
    public ServiceInterceptorBeanPostProcessor serviceInterceptorBeanPostProcessor() {
        ServiceInterceptorBeanPostProcessor serviceInterceptorBeanPostProcessor = new ServiceInterceptorBeanPostProcessor();
        Map<String, String> patternMap = Maps.newHashMap();
        String beanClassPattern1 = "com.xcwlkj.service.impl.*(ServiceImpl)$";
        String beanNamePattern1 = ".*(Service)$";
        patternMap.put(beanClassPattern1, beanNamePattern1);
        //支持多个键值对的匹配
        serviceInterceptorBeanPostProcessor.setPattern(patternMap);
        return serviceInterceptorBeanPostProcessor;
    }

    /**
     * 系统缓存初始化
     * 
     * @return
     */
    @Bean
    public SystemCacheInitConfig signBusiCacheInitConfig() {
        return new DataBizCacheInitConfig();
    }

    @Bean
    public RedisUtilAdapter redisUtilAdapter(RedisUtil redisUtil) {
    	return new RedisUtilAdapter(redisUtil);
    }
}
