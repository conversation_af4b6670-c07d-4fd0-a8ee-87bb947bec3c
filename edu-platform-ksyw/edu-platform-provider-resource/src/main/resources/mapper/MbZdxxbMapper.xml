<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.ksyw.resource.mapper.MbZdxxbMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.ksyw.resource.model.domain.MbZdxxb">
        <id column="mbywmc" jdbcType="VARCHAR" property="mbywmc" />
        <id column="zdywmc" jdbcType="VARCHAR" property="zdywmc" />
        <result column="zdmc" jdbcType="VARCHAR" property="zdmc" />
        <result column="zdcd" jdbcType="BIGINT" property="zdcd" />
        <result column="sfwk" jdbcType="VARCHAR" property="sfwk" />
        <result column="sfkbj" jdbcType="VARCHAR" property="sfkbj" />
        <result column="sfzj" jdbcType="VARCHAR" property="sfzj" />
        <result column="mrz" jdbcType="VARCHAR" property="mrz" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        mbywmc,
        zdmc,
        zdywmc,
        zdcd,
        sfwk,
        sfkbj,
        sfzj,
        mrz,
        create_time,
        update_time

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="mbywmc != null and mbywmc != ''">
            AND mbywmc = #{mbywmc,jdbcType=VARCHAR}
        </if>
        <if test="zdmc != null and zdmc != ''">
            AND zdmc = #{zdmc,jdbcType=VARCHAR}
        </if>
        <if test="zdywmc != null and zdywmc != ''">
            AND zdywmc = #{zdywmc,jdbcType=VARCHAR}
        </if>
        <if test="zdcd != null and zdcd != ''">
            AND zdcd = #{zdcd,jdbcType=BIGINT}
        </if>
        <if test="sfwk != null and sfwk != ''">
            AND sfwk = #{sfwk,jdbcType=VARCHAR}
        </if>
        <if test="sfkbj != null and sfkbj != ''">
            AND sfkbj = #{sfkbj,jdbcType=VARCHAR}
        </if>
        <if test="sfzj != null and sfzj != ''">
            AND sfzj = #{sfzj,jdbcType=VARCHAR}
        </if>
        <if test="mrz != null and mrz != ''">
            AND mrz = #{mrz,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="mbywmc != null ">
            mbywmc = #{mbywmc,jdbcType=VARCHAR},
        </if>
        <if test="zdmc != null ">
            zdmc = #{zdmc,jdbcType=VARCHAR},
        </if>
        <if test="zdywmc != null ">
            zdywmc = #{zdywmc,jdbcType=VARCHAR},
        </if>
        <if test="zdcd != null ">
            zdcd = #{zdcd,jdbcType=BIGINT},
        </if>
        <if test="sfwk != null ">
            sfwk = #{sfwk,jdbcType=VARCHAR},
        </if>
        <if test="sfkbj != null ">
            sfkbj = #{sfkbj,jdbcType=VARCHAR},
        </if>
        <if test="sfzj != null ">
            sfzj = #{sfzj,jdbcType=VARCHAR},
        </if>
        <if test="mrz != null ">
            mrz = #{mrz,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.ksyw.resource.model.domain.MbZdxxb"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from mb_zdxxb
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
