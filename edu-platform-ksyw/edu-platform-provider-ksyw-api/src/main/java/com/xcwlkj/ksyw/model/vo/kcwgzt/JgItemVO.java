/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.kcwgzt;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 查询机构列表vo
 * <AUTHOR>
 * @version $Id: JgItemVO.java, v 0.1 2023年04月15日 10时44分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JgItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 机构id */
    private String jgid;
    /** 机构名称 */
    private String jgmc;

}
