package com.xcwlkj.ksyw.model.enums.event;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 数据下发枚举类型
 */
public enum EventTypeSjsbEnum {

    SUCCESS("1", "成功")
    ,FAIL_SBCS("-1401","超时未报")
    ,FAIL_SJQS("-1402","数据缺失")
    ,FAIL_SBCW("-1403","设备故障")
    ,FAIL_SJCW("-1404","数据错误")
    ,FAIL_JKRYWCZ("-1405","监考人员未操作")
    ;
    private final String code;
    private final String desc;

    EventTypeSjsbEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static EventTypeSjsbEnum get(String code) {
        for (EventTypeSjsbEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }

    public static String getDesc(String code) {
        for (EventTypeSjsbEnum c : values()) {
            if (c.getCode().equals(code)) {
                return c.getDesc();
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }

}
