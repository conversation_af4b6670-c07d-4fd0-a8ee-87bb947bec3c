/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.kswj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 11-修改考试违纪处理办法dto
 * <AUTHOR>
 * @version $Id: XgkswjclbfDTO.java, v 0.1 2020年08月24日 10时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class XgkswjclbfDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标题 */
    @NotBlank(message = "标题不能为空")
    private String bt;
    /** 内容 */
    @NotBlank(message = "内容不能为空")
    private String nr;
    /** 考试违纪处理办法-类型（暂未定，先空着） */
    private String lx;
    /** 考试违纪处理办法-主键uuid */
    @NotBlank(message = "考试违纪处理办法-主键uuid不能为空")
    private String uuid;

}
