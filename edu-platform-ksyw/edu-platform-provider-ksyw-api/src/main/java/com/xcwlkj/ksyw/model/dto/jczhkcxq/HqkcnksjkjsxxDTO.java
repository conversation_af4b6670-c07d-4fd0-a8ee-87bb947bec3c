/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.jczhkcxq;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 考场详情-获取考场内考生监考教师信息dto
 * <AUTHOR>
 * @version $Id: HqkcnksjkjsxxDTO.java, v 0.1 2022年01月05日 21时06分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqkcnksjkjsxxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考场编号 */
    @NotBlank(message = "考场编号不能为空")
    private String kcbh;
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 考场类型：bykc 备用/glkc 隔离/kwbgs 考务室 */
    private String kclx;
    /** 标准化考点编号 */
    private String bzhkdbh;
    /** 考场标志id */
    private String kcbzid;

}
