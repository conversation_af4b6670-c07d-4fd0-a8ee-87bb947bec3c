/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.ksjhgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 04-考试计划管理-考试场次修改dto
 * <AUTHOR>
 * @version $Id: KsccXgKmmXzItemDTO.java, v 0.1 2020年04月29日 11时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KsccXgKmmXzItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 科目码 */
    @NotBlank(message = "科目码不能为空")
    private String kmm;
    /** 科目名称 */
    @NotBlank(message = "科目名称不能为空")
    private String kmmc;

}
