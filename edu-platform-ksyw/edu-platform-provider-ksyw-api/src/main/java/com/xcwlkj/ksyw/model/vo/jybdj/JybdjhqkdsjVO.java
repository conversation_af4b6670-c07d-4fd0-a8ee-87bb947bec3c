/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jybdj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 教育部对接业务调用-获取考点信息数据vo
 * <AUTHOR>
 * @version $Id: JybdjhqkdsjVO.java, v 0.1 2021年04月19日 13时47分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JybdjhqkdsjVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考点信息集合 */
    private List<JybdjkdkdxxItemVO> jybdjkdkdxxList;

}
