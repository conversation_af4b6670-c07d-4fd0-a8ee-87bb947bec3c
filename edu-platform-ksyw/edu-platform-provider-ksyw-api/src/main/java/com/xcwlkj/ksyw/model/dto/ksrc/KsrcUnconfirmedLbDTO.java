/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.ksrc;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 未确认考场查询dto
 * <AUTHOR>
 * @version $Id: KsrcUnconfirmedLbDTO.java, v 0.1 2024年05月24日 13时57分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KsrcUnconfirmedLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 机构id */
    @NotBlank(message = "机构id不能为空")
    private String jgid;
    /** 机构类型  ksgljg/bzhkd */
    @NotBlank(message = "机构类型  ksgljg/bzhkd不能为空")
    private String jglx;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 场所名称 */
    private String csmc;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageNum;
    /** 页大小 */
    @NotNull(message = "页大小不能为空")
    private Integer pageSize;

}
