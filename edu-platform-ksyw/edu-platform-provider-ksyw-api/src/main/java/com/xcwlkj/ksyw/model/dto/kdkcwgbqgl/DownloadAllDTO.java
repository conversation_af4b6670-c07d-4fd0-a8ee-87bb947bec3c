/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.kdkcwgbqgl;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;


/**
 * 考场网关标签管理-下载全部dto
 * <AUTHOR>
 * @version $Id: DownloadAllDTO.java, v 0.1 2022年06月10日 15时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DownloadAllDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 机构id */
    @NotBlank(message = "机构id不能为空")
    private String jgid;
    /** 机构类型 ksgljg | bzhkd */
    @NotBlank(message = "机构类型 ksgljg | bzhkd不能为空")
    private String jglx;
    /** 下载情况   0 未下载 ，1 已下载 */
    private String xzzt;

}
