/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import com.xcwlkj.ksyw.service.hystrix.KssjdrXcmcFeignHystrix;
import com.xcwlkj.core.annotation.mock.YapiMock;
import com.xcwlkj.ksyw.model.dto.kssjdr.DbfksbpsjwjdrDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.DbfkdsjwjdrDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrGzdzDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.GxKdBzhkdxxDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrXKDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrXueKaoDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrXueKaoZengJiaDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrZKDTO;
import com.xcwlkj.ksyw.model.dto.kssjdr.KqkdkcsjdrCKDTO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * <AUTHOR>
 * @version $Id: KssjdrXcmcFeignApi.java, v 0.1 2020年05月17日 16时22分 xcwlkj.com Exp $
 */
@FeignClient(value = "ksyw-service", fallback = KssjdrXcmcFeignHystrix.class)
public interface KssjdrXcmcFeignApi {

    /**
     * 11-考生编排数据文件导入(dbf格式)
     * @param dbfksbpsjwjdrDto
     * @return
     */
    @YapiMock(projectId = "117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/dbfksbpsjwjdr", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Wrapper<Void> dbfksbpsjwjdr(@RequestPart(required = true) MultipartFile file,
                                @RequestParam(required = true) String ksjhbh,
                                @RequestParam String wjlj);

    /**
     * 12-考点数据文件导入(dbf格式)
     * @param dbfkdsjwjdrDto
     * @return
     */
    @YapiMock(projectId = "117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/dbfkdsjwjdr", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Wrapper<Void> dbfkdsjwjdr(@RequestPart(required = true) MultipartFile file,
                              @RequestParam(required = true) String ksjhbh,
                              @RequestParam String wjlj);
    /**
     * 14-导入到数据库中，考区、考点、考场数据
     * @param kqkdkcsjdrDto
     * @return
     */
    @YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdr")
    Wrapper<Void> kqkdkcsjdr(@RequestBody KqkdkcsjdrDTO kqkdkcsjdrDto);
    /**
     * 15-导入到数据库中，考区、考点、考场数据(高职单招)
     * @param kqkdkcsjdrGzdzDto
     * @return
     */
    @YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdrGzdz")
    Wrapper<Void> kqkdkcsjdrGzdz(@RequestBody KqkdkcsjdrGzdzDTO kqkdkcsjdrGzdzDto);
    /**
     * 16-同步更新考点数据中的 标准化考点的信息
     * @param gxKdBzhkdxxDto
     * @return
     */
    @YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/gxKdBzhkdxx")
    Wrapper<Void> gxKdBzhkdxx(@RequestBody GxKdBzhkdxxDTO gxKdBzhkdxxDto);
    /**
     * 17-导入到数据库中，考区、考点、考场数据(选考)
     * @param kqkdkcsjdrXKDto
     * @return
     */
    @YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdrXK")
    Wrapper<Void> kqkdkcsjdrXK(@RequestBody KqkdkcsjdrXKDTO kqkdkcsjdrXKDto);
	/**
	 * 19-导入到数据库中，考区、考点、考场、考生数据(学考)
	 * @param kqkdkcsjdrXueKaoDto
	 * @return
	 */
	@YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdrXueKao")
    Wrapper<Void> kqkdkcsjdrXueKao(@RequestBody KqkdkcsjdrXueKaoDTO kqkdkcsjdrXueKaoDto);
	/**
	 * 20-导入到数据库中，考区、考点、考场、考生数据(学考追加数据)
	 * @param kqkdkcsjdrXueKaoZengJiaDto
	 * @return
	 */
	@YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdrXueKaoZengJia")
    Wrapper<Void> kqkdkcsjdrXueKaoZengJia(@RequestBody KqkdkcsjdrXueKaoZengJiaDTO kqkdkcsjdrXueKaoZengJiaDto);
	/**
	 * 21-导入到数据库中，考区、考点、考场数据(自考)
	 * @param kqkdkcsjdrZKDto
	 * @return
	 */
	@YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdrZK")
    Wrapper<Void> kqkdkcsjdrZK(@RequestBody KqkdkcsjdrZKDTO kqkdkcsjdrZKDto);
	/**
	 * 22-导入到数据库中，考区、考点、考场数据(成考)
	 * @param kqkdkcsjdrCKDto
	 * @return
	 */
	@YapiMock(projectId="117", returnClass = Void.class)
    @PostMapping(value = "/ksyw/kssjdr/kqkdkcsjdrCK")
    Wrapper<Void> kqkdkcsjdrCK(@RequestBody KqkdkcsjdrCKDTO kqkdkcsjdrCKDto);
}
