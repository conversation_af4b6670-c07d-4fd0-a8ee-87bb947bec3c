/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.jybdj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 教育部对接业务调用-获取考试安排信息数据dto
 * <AUTHOR>
 * @version $Id: JybdjhqksccsjDTO.java, v 0.1 2021年05月14日 16时02分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JybdjhqksccsjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;

}
