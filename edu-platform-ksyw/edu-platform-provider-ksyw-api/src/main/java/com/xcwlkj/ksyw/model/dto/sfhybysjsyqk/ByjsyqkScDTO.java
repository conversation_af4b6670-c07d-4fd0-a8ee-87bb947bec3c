/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.sfhybysjsyqk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 备用卷使用情况-4.备用卷使用记录删除dto
 * <AUTHOR>
 * @version $Id: ByjsyqkScDTO.java, v 0.1 2021年05月07日 18时39分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ByjsyqkScDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 备用卷使用情况记录id列表 */
    @NotNull(message = "备用卷使用情况记录id列表不能为空")
    private List<String> byjsyqkIdLb;

}
