/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.ksjhgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 38-获取到考区信息列表vo
 * <AUTHOR>
 * @version $Id: KqxxlbVO.java, v 0.1 2021年08月16日 15时33分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KqxxlbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考区信息列表 */
    private List<KqxxlbItemVO> kqxxlb;

}
