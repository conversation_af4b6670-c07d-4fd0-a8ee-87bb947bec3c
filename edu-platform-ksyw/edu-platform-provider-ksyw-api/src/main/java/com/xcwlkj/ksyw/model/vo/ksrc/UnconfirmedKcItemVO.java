/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.ksrc;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 未确认考场查询vo
 * <AUTHOR>
 * @version $Id: UnconfirmedKcItemVO.java, v 0.1 2024年05月24日 13时57分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class UnconfirmedKcItemVO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 地市名称 */
    private String dsmc;
    /** 考区名称 */
    private String kqmc;
    /** 考点代码 */
    private String kddm;
    /** 考点名称 */
    private String kdmc;
    /** 场所代码 */
    private String csdm;
    /** 场所名称 */
    private String csmc;
    /** 考场名称 */
    private String kcmc;

}
