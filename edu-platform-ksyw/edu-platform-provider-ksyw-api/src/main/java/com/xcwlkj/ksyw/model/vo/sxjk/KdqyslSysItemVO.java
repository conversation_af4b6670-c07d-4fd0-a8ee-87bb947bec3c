/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 陕西接口-考点统计考生性别vo
 * <AUTHOR>
 * @version $Id: KdqyslSysItemVO.java, v 0.1 2021年05月17日 11时48分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdqyslSysItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 行政区划/考点名称 */
    private String mc;
    /** 男人数 */
    private Integer nan;
    /** 女人数 */
    private Integer nv;
    /** 总人数 */
    private Integer zrs;

}
