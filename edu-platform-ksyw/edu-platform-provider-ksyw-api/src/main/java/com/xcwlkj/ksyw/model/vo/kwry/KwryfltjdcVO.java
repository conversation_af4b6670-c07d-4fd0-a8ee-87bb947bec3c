/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.kwry;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 36-考务工作人员分类统计导出vo
 * <AUTHOR>
 * @version $Id: KwryfltjdcVO.java, v 0.1 2021年08月03日 11时16分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KwryfltjdcVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 导出文件路径 */
    private String dcwjlj;

}
