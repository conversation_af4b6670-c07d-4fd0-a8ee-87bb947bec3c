/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.jcxxgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 45-陕西-导出标准化考点文档dto
 * <AUTHOR>
 * @version $Id: SxDcBzhkdWdDTO.java, v 0.1 2020年12月07日 18时57分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SxDcBzhkdWdDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标准化考点名称 */
    private String bzhkdmc;
    /** 标准化考点编号 */
    private String bzhkdbh;
    /** 机构标志码 */
    @NotBlank(message = "机构标志码不能为空")
    private String jgid;
    /** 机构类型：bzhkd/ksgljg */
    @NotBlank(message = "机构类型：bzhkd/ksgljg不能为空")
    private String jglx;
    /** 导出类型 ：dbf，pdf，xlsx */
    @NotBlank(message = "导出类型 ：dbf，pdf，xlsx不能为空")
    private String dclx;
    /** 0/1/2 无签章/单位签章/签字确认 */
    @NotBlank(message = "0/1/2 无签章/单位签章/签字确认不能为空")
    private String qmbzm;
    /** 水印内容 */
    @NotBlank(message = "水印内容不能为空")
    private String synr;

}
