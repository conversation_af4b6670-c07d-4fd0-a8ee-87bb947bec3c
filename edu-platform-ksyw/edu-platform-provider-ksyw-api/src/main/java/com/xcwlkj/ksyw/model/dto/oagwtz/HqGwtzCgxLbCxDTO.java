/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.oagwtz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 30 【草稿箱】-查询我的草稿箱dto
 * <AUTHOR>
 * @version $Id: HqGwtzCgxLbCxDTO.java, v 0.1 2020年10月13日 13时32分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqGwtzCgxLbCxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 每页显示数 */
    @NotNull(message = "每页显示数不能为空")
    private Integer pageSize;
    /** 第几页 */
    @NotNull(message = "第几页不能为空")
    private Integer pageNum;

}
