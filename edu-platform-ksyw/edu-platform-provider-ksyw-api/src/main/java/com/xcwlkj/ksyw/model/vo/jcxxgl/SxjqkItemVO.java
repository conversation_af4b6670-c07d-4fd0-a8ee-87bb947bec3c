package com.xcwlkj.ksyw.model.vo.jcxxgl;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 18-标准化考场-信息详情vo
 * <AUTHOR>
 * @version $Id: SxjqkItemVO.java, v 0.1 2022年04月26日 16时12分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SxjqkItemVO implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 摄像机名称 */
    private String sxjmc;
    /** 安装方位 */
    private String azfw;
    /** sip地址 */
    private String sipurl;
    /** 摄像机类型 */
    private String sxjlx;

    /** 设备编号 */
    private String sbbh;

}
