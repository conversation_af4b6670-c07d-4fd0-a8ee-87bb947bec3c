/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jybdj;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 教育部对接业务调用-获取指挥中心vo
 * <AUTHOR>
 * @version $Id: JybdjhqzhzxVO.java, v 0.1 2021年11月12日 09时39分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JybdjhqzhzxVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 指挥中心列表 */
    private List<JybdjhqzhzxItemVO> jybdjhqzhzxList;

}
