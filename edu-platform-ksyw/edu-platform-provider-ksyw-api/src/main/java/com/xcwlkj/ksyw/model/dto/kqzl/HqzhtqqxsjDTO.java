/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.kqzl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 考情总览-获取灾害天气气象数据（灾害气象实时数据  来自于京东）dto
 * <AUTHOR>
 * @version $Id: HqzhtqqxsjDTO.java, v 0.1 2021年06月04日 00时39分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqzhtqqxsjDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 行政区划代码 */
    @NotBlank(message = "行政区划代码不能为空")
    private String xzqhdm;
    /** 行政区划类型 */
    @NotBlank(message = "行政区划类型不能为空")
    private String xzqhlx;
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;

}
