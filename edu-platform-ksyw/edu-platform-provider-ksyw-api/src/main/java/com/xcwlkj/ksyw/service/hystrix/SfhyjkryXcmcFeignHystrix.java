/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service.hystrix;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.ksyw.service.SfhyjkryXcmcFeignApi;

import com.xcwlkj.ksyw.model.dto.sfhyjkry.GetJksbqkDTO;
import com.xcwlkj.ksyw.model.vo.sfhyjkry.GetJksbqkVO;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * @version $Id: SfhyjkryXcmcFeignHystrix.java, v 0.1 2023年02月08日 10时24分 xcwlkj.com Exp $
 */
@Component
public class SfhyjkryXcmcFeignHystrix implements SfhyjkryXcmcFeignApi{

    /** 
     * @see com.xcwlkj.ksyw.service.SfhyjkryXcmcFeignApi#getJksbqk(com.xcwlkj.ksyw.model.dto.sfhyjkry.GetJksbqkDTO)
     */
    @Override
    public Wrapper<GetJksbqkVO> getJksbqk(GetJksbqkDTO getJksbqkDto) {
        return WrapMapper.error();
    }
}
