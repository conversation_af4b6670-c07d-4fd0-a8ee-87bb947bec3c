/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 陕西接口-省市根据行政区码获取考区名称和考点列表vo
 * <AUTHOR>
 * @version $Id: SsHqkqxxItemVO.java, v 0.1 2020年11月25日 14时39分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SsHqkqxxItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考点编号列表 */
    private List<String> kdbhlb;
    /** 考区名称 */
    private String kqmc;
    /** 考区编号 */
    private String kqbh;

}
