/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jyjkks;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 教育-校外考生登记汇总导出vo
 * <AUTHOR>
 * @version $Id: XwksdjHzdcVO.java, v 0.1 2022年04月19日 09时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class XwksdjHzdcVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 文档路径 */
    private String wdlj;

}
