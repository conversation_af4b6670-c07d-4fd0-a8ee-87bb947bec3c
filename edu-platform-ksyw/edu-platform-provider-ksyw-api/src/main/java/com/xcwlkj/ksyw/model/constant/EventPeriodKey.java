package com.xcwlkj.ksyw.model.constant;

/**
 * 事件周期键
 */
public class EventPeriodKey {

    //数据同步
    public final static String SJTB = "SJTB";
    //数据打包
    public final static String SJDB = "SJDB";
    //数据下发
    public final static String SJXF = "SJXF";
    //考试阶段键前缀
    private final static String KSQZ = "KS_";

    //获取考试阶段的键
    public static String getKsjdKey(String ccm) {
        return KSQZ + ccm;
    }
}
