/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.hyyndz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 网关设备照片导出vo
 * <AUTHOR>
 * @version $Id: WgsbzpdcVO.java, v 0.1 2024年12月22日 17时09分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class WgsbzpdcVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** dclj */
    private String dclj;

}
