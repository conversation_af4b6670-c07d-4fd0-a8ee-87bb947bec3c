/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jcxxgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 34-导出考试管理机构文档vo
 * <AUTHOR>
 * @version $Id: JgdcwdVO.java, v 0.1 2020年08月13日 19时13分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JgdcwdVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 总条数 */
    private Integer TotalRows;
    /** 导出路径 */
    private String dclj;

}
