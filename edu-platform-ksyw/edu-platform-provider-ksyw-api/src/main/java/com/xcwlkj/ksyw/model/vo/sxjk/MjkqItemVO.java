/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 陕西接口-根据行政区码获取省市县考区vo
 * <AUTHOR>
 * @version $Id: MjkqItemVO.java, v 0.1 2020年11月11日 09时26分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MjkqItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 末级考区名称 */
    private String mjkqmc;
    /** 末级考区编号 */
    private String mjkqbh;

}
