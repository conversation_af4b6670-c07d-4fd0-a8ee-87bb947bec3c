/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.jcsjqtmkdy;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 11 视频巡查-根据逻辑考点编号获取标准化考点列表dto
 * <AUTHOR>
 * @version $Id: HqbzhkdLbDTO.java, v 0.1 2020年05月22日 11时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqbzhkdLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 考试管理机构编号 */
    @NotBlank(message = "考试管理机构编号不能为空")
    private String ksgljgbh;

}
