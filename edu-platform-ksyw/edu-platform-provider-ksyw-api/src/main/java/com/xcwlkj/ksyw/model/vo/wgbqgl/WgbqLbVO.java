/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.wgbqgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 网关标签管理列表vo
 * <AUTHOR>
 * @version $Id: WgbqLbVO.java, v 0.1 2023年02月03日 14时53分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class WgbqLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 网关标签列表 */
    private List<WgbqItemVO> wgbqList;
    /** 总数 */
    private Integer totalNum;

}
