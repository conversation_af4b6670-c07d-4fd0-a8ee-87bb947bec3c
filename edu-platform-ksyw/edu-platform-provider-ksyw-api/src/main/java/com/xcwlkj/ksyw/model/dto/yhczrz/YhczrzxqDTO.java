/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.yhczrz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 3-用户操作日志详情dto
 * <AUTHOR>
 * @version $Id: YhczrzxqDTO.java, v 0.1 2020年09月17日 10时00分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class YhczrzxqDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 用户操作日志标志码 */
    @NotBlank(message = "用户操作日志标志码不能为空")
    private String czrzbzid;

}
