/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.sfhytsqkcl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 考场特殊情况-7.考场特殊情况汇总导出dto
 * <AUTHOR>
 * @version $Id: KcTsqkhzdcDTO.java, v 0.1 2021年07月23日 14时56分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KcTsqkhzdcDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码  传值为当前场次，不传为全部场次 */
    private String ccm;
    /** 场所类型：考试管理机构（ksgljg） / 考点（kd） */
    @NotBlank(message = "场所类型：考试管理机构（ksgljg） / 考点（kd）不能为空")
    private String cslx;
    /** 场所编号 ksgljh - ksgljgid / kd - kdbh */
    @NotBlank(message = "场所编号 ksgljh - ksgljgid / kd - kdbh不能为空")
    private String csbh;

}
