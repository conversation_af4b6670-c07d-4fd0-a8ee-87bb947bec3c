/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.hyyndz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 考场座位布局列表vo
 * <AUTHOR>
 * @version $Id: KczwbjItemVO.java, v 0.1 2024年10月15日 16时28分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KczwbjItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    private String id;
    /** 地市 */
    private String ds;
    /** 区县 */
    private String qx;
    /** 考点代码 */
    private String kddm;
    /** 考点名称 */
    private String kdmc;
    /** 场次 */
    private String cc;
    /** 类型（0：考场、1：备用考场） */
    private String type;
    /** 考场号 */
    private String kch;
    /** 考场名称 */
    private String kcmc;
    /** 座位布局方式码 */
    private String zwbjfsm;
    /** 座位布局方式 */
    private String zwbjfs;
    /** 座位排列方式码 */
    private String zwplfsm;
    /** 座位排列方式 */
    private String zwplfs;
    /** 座次起始位置码 */
    private String zcqswzm;
    /** 座次起始位置 */
    private String zcqswz;
    /** 逻辑考场号 */
    private String ljkch;
    /** 标准化考场id */
    private String bzhkcid;
    /** 标准化考场名称 */
    private String bzhkcmc;
    /** 标准化考点id */
    private String bzhkdid;
    /** 更新时间 */
    private String updateTime;

}
