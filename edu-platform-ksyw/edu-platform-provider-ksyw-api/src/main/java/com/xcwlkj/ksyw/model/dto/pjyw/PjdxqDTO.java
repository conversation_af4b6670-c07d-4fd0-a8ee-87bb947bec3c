/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.pjyw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 评卷点-详情dto
 * <AUTHOR>
 * @version $Id: PjdxqDTO.java, v 0.1 2021年07月26日 11时14分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class PjdxqDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 评卷点编号 */
    @NotBlank(message = "评卷点编号不能为空")
    private String pjdbh;

}
