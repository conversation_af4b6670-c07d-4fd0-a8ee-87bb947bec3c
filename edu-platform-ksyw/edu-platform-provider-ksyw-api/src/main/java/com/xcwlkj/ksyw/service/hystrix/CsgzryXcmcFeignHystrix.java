/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service.hystrix;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.ksyw.service.CsgzryXcmcFeignApi;

import com.xcwlkj.ksyw.model.dto.jcxxgl.CsgzryLbDcExcelDTO;
import com.xcwlkj.ksyw.model.vo.jcxxgl.CsgzryLbDcExcelVO;
import com.xcwlkj.ksyw.model.dto.jcxxgl.CsgzryLbDTO;
import com.xcwlkj.ksyw.model.vo.jcxxgl.CsgzryLbVO;
import com.xcwlkj.ksyw.model.dto.csgzry.RefreshUserCacheDTO;
import com.xcwlkj.ksyw.model.dto.csgzry.ZhmcxDTO;
import com.xcwlkj.ksyw.model.vo.csgzry.ZhmcxVO;
import com.xcwlkj.ksyw.model.dto.csgzry.DdyhsqDTO;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * @version $Id: CsgzryXcmcFeignHystrix.java, v 0.1 2021年09月19日 19时37分 xcwlkj.com Exp $
 */
@Component
public class CsgzryXcmcFeignHystrix implements CsgzryXcmcFeignApi{

    /** 
     * @see com.xcwlkj.ksyw.service.CsgzryXcmcFeignApi#csgzryLbDcExcel(com.xcwlkj.ksyw.model.dto.jcxxgl.CsgzryLbDcExcelDTO)
     */
    @Override
    public Wrapper<CsgzryLbDcExcelVO> csgzryLbDcExcel(CsgzryLbDcExcelDTO csgzryLbDcExcelDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.CsgzryXcmcFeignApi#csgzryLb(com.xcwlkj.ksyw.model.dto.jcxxgl.CsgzryLbDTO)
     */
    @Override
    public Wrapper<CsgzryLbVO> csgzryLb(CsgzryLbDTO csgzryLbDto) {
        return WrapMapper.error();
    }

    /** 
     * @see com.xcwlkj.ksyw.service.CsgzryXcmcFeignApi#refreshUserCache(com.xcwlkj.ksyw.model.dto.csgzry.RefreshUserCacheDTO)
     */
    @Override
    public Wrapper<Void> refreshUserCache(RefreshUserCacheDTO refreshUserCacheDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.CsgzryXcmcFeignApi#zhmcx(com.xcwlkj.ksyw.model.dto.csgzry.ZhmcxDTO)
     */
    @Override
    public Wrapper<ZhmcxVO> zhmcx(ZhmcxDTO zhmcxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.CsgzryXcmcFeignApi#ddyhsq(com.xcwlkj.ksyw.model.dto.csgzry.DdyhsqDTO)
     */
    @Override
    public Wrapper<Void> ddyhsq(DdyhsqDTO ddyhsqDto) {
        return WrapMapper.error();
    }
}
