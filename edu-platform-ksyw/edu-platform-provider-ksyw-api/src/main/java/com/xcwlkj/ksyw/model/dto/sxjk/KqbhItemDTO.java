/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 陕西接口-根据行政区码获取被管辖考区（含自身）dto
 * <AUTHOR>
 * @version $Id: KqbhItemDTO.java, v 0.1 2020年11月12日 17时34分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KqbhItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考区编号 */
    @NotBlank(message = "考区编号不能为空")
    private String kqbh;
    /** 考区名称 */
    @NotBlank(message = "考区名称不能为空")
    private String kqmc;

}
