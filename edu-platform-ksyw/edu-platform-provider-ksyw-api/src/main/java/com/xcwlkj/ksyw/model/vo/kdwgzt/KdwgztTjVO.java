/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.kdwgzt;

import java.io.Serializable;
import java.util.List;

import com.xcwlkj.ksyw.model.vo.kcwgzt.KcwgztTjItemVO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 考点网关状态列表vo
 * <AUTHOR>
 * @version $Id: KdwgztTjVO.java, v 0.1 2023年04月11日 09时59分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdwgztTjVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;

    /** 当前机构考场网关统计信息 */
    private KdwgztTjItemVO kdwgtj;
    /** 当前机构考场网关统计信息 */
    private List<KdwgztTjItemVO> subKdwgtj;

}
