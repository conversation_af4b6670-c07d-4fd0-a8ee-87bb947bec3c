/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jcsjqtmkdy;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.math.BigDecimal;


/**
 * 12-视频巡查-通过机构标志列表获取考点信息列表（考点已关联的）vo
 * <AUTHOR>
 * @version $Id: SpxcKdxxItemVO.java, v 0.1 2020年05月22日 09时24分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SpxcKdxxItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考点标识码 */
    private String kdid;
    /** 考点编号 */
    private String kdbh;
    /** 考点名称 */
    private String kdmc;
    /** 考点经度 */
    private BigDecimal kdjd;
    /** 考点纬度 */
    private BigDecimal kdwd;
    /** 标准化考点标识码 */
    private String bzhkdid;
    /** 当前考点所属的考试管理机构标志码 */
    private String ksgljgid;

}
