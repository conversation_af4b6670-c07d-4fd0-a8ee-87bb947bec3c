/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jczhkqzl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 考情总览-根据保密室编号获取镜头列表vo
 * <AUTHOR>
 * @version $Id: GjbmsbhhqjtlbVO.java, v 0.1 2020年05月29日 20时21分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GjbmsbhhqjtlbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 根据保密室编号获取镜头列表 */
    private List<GjbmsbhhqjtlbItemVO> gjbmsbhhqjtlb;
    /** 试卷保密室名称 */
    private String sjbmsmc;

}
