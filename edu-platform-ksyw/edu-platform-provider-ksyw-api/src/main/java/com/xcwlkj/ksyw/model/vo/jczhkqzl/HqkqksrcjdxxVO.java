/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jczhkqzl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 考情总览-获取考区考生入场进度信息vo
 * <AUTHOR>
 * @version $Id: HqkqksrcjdxxVO.java, v 0.1 2020年07月02日 19时38分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqkqksrcjdxxVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 入场开始时间，格式 yyyy-MM-dd HH:mm:ss */
    private String rckssj;
    /** 刻度的单位长度，单位：秒 */
    private Integer kddwcd;
    /** 刻度数量 */
    private Integer kdsl;
    /** 按照时间刻度入场人数的列表 */
    private List<SjkdrcrsItemVO> sjkdrcrsList;

}
