/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.pjyw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 评卷老师-删除dto
 * <AUTHOR>
 * @version $Id: PjlsscDTO.java, v 0.1 2021年06月30日 11时42分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class PjlsscDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 评卷老师编号 */
    @NotNull(message = "评卷老师编号不能为空")
    private List<String> pjlsbhs;
    /** 评卷计划编号 */
    private String pjjhbh;

}
