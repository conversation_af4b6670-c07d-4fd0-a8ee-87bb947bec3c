/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.sxjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 陕西接口-省市县考生科类统计vo
 * <AUTHOR>
 * @version $Id: SxjkSsxKlItemVO.java, v 0.1 2020年11月23日 14时58分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SxjkSsxKlItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考生数量 */
    private String kssl;
    /** 科类名称 */
    private String klmc;

}
