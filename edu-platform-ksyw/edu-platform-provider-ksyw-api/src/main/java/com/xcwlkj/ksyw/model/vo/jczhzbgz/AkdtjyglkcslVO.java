/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.jczhzbgz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 按考点统计已关联考场数量vo
 * <AUTHOR>
 * @version $Id: AkdtjyglkcslVO.java, v 0.1 2020年06月29日 20时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class AkdtjyglkcslVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 统计已关联考场数量列表 */
    private List<TjyglkcslItemVO> tjyglkcslList;

}
