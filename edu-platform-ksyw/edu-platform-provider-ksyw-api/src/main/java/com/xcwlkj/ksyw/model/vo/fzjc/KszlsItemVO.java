/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.fzjc;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 大屏统计数据展示vo
 * <AUTHOR>
 * @version $Id: KszlsItemVO.java, v 0.1 2020年07月01日 17时34分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KszlsItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 时间 年月日时分秒 */
    private String sj;
    /** 指令名称 */
    private String zlmc;
    /** 指令备注 */
    private String zlbz;

}
