/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.kssjdr;

import java.io.Serializable;

import org.springframework.web.multipart.MultipartFile;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 12-考点数据文件导入(dbf格式)dto
 * <AUTHOR>
 * @version $Id: DbfkdsjwjdrDTO.java, v 0.1 2020年05月18日 11时21分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DbfkdsjwjdrDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	

//  @RequestPart(required = true, value = "dbffile") MultipartFile file,
//  @RequestParam(required = true) String ksjhbh,
//  @RequestParam String wjlj
  
  private MultipartFile file;

  private String ksjhbh;

  private String wjlj;
}
