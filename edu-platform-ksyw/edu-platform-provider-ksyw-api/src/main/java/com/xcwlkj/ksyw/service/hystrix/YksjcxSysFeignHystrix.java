/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.ksyw.service.hystrix;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.ksyw.service.YksjcxSysFeignApi;

import com.xcwlkj.ksyw.model.dto.yksjcx.BkdjgxxDTO;
import com.xcwlkj.ksyw.model.vo.yksjcx.BkdjgxxVO;
import com.xcwlkj.ksyw.model.dto.yksjcx.KdxxDTO;
import com.xcwlkj.ksyw.model.vo.yksjcx.KdxxVO;
import com.xcwlkj.ksyw.model.dto.yksjcx.KcxxDTO;
import com.xcwlkj.ksyw.model.vo.yksjcx.KcxxVO;
import com.xcwlkj.ksyw.model.dto.yksjcx.KsbmxxDTO;
import com.xcwlkj.ksyw.model.vo.yksjcx.KsbmxxVO;
import com.xcwlkj.ksyw.model.dto.yksjcx.KsbpxxDTO;
import com.xcwlkj.ksyw.model.vo.yksjcx.KsbpxxVO;
import com.xcwlkj.ksyw.model.dto.yksjcx.KdgzryxxDTO;
import com.xcwlkj.ksyw.model.vo.yksjcx.KdgzryxxVO;
import org.springframework.stereotype.Component;

/**
 * 
 * <AUTHOR>
 * @version $Id: YksjcxSysFeignHystrix.java, v 0.1 2021年12月07日 15时08分 xcwlkj.com Exp $
 */
@Component
public class YksjcxSysFeignHystrix implements YksjcxSysFeignApi{

    /** 
     * @see com.xcwlkj.ksyw.service.YksjcxSysFeignApi#bkdjgxx(com.xcwlkj.ksyw.model.dto.yksjcx.BkdjgxxDTO)
     */
    @Override
    public Wrapper<BkdjgxxVO> bkdjgxx(BkdjgxxDTO bkdjgxxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.YksjcxSysFeignApi#kdxx(com.xcwlkj.ksyw.model.dto.yksjcx.KdxxDTO)
     */
    @Override
    public Wrapper<KdxxVO> kdxx(KdxxDTO kdxxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.YksjcxSysFeignApi#kcxx(com.xcwlkj.ksyw.model.dto.yksjcx.KcxxDTO)
     */
    @Override
    public Wrapper<KcxxVO> kcxx(KcxxDTO kcxxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.YksjcxSysFeignApi#ksbmxx(com.xcwlkj.ksyw.model.dto.yksjcx.KsbmxxDTO)
     */
    @Override
    public Wrapper<KsbmxxVO> ksbmxx(KsbmxxDTO ksbmxxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.YksjcxSysFeignApi#ksbpxx(com.xcwlkj.ksyw.model.dto.yksjcx.KsbpxxDTO)
     */
    @Override
    public Wrapper<KsbpxxVO> ksbpxx(KsbpxxDTO ksbpxxDto) {
        return WrapMapper.error();
    }
    /** 
     * @see com.xcwlkj.ksyw.service.YksjcxSysFeignApi#kdgzryxx(com.xcwlkj.ksyw.model.dto.yksjcx.KdgzryxxDTO)
     */
    @Override
    public Wrapper<KdgzryxxVO> kdgzryxx(KdgzryxxDTO kdgzryxxDto) {
        return WrapMapper.error();
    }
}
