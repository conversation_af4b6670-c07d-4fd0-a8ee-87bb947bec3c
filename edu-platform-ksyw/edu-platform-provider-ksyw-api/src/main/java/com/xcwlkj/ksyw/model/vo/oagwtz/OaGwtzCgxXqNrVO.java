/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.oagwtz;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 31 【草稿箱】-查询草稿箱详情vo
 * <AUTHOR>
 * @version $Id: OaGwtzCgxXqNrVO.java, v 0.1 2020年10月13日 13时32分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class OaGwtzCgxXqNrVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标题 */
    private String bt;
    /** 创建人的uuid */
    private String creator;
    /** 删除状态 */
    private String delFlag;
    /** 发布部门 */
    private String fbbm;
    /** 发布对象 */
    private String fbdw;
    /** 发布单位 */
    private String fbdx;
    /** 发布机构 */
    private String fbjgUuid;
    /** 发文字号 */
    private String fwzh;
    /** 是否发送短信 */
    private String isSMS;
    /** 是否填报附件 */
    private String istbfj;
    /** 类型 */
    private String lx;
    /** 内容概述 */
    private String nrgs;
    /** 是否存在附件 */
    private String sfczfj;
    /** 是否需要填报 */
    private String sfxytb;
    /** 短息内容 */
    private String smsnr;
    /** 更新时间 */
    private String updateTime;
    /** uuid */
    private String uuid;
    /** 信息列表 */
    private String xxlb;
    /** 结束时间 */
    private String yxsjJssj;
    /** 开始时间 */
    private String yxsjKssj;
    /** 正文 */
    private String zw;

}
