/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.gxdata;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 监考老师编排信息列表vo
 * <AUTHOR>
 * @version $Id: LsBpxxLbVO.java, v 0.1 2022年04月19日 10时56分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class LsBpxxLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    private List<LsBpxxItemVO> lsBpxxList;
    /** 总数 */
    private Integer totalNum;

}
