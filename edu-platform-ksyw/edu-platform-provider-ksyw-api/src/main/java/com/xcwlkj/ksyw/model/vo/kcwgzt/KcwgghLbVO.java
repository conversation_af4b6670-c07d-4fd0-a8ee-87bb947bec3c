/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.vo.kcwgzt;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 考场网关规划数列表vo
 * <AUTHOR>
 * @version $Id: KcwgghLbVO.java, v 0.1 2023年05月19日 08时40分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KcwgghLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    private List<KcwgghLbItemVO> kcwgghLb;
    /** 总条数 */
    private Integer totalRows;

}
