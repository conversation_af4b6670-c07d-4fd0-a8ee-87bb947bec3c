/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.jcsjqtmkdy;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 18-根据标准化考点获取关联考试管理机构信息dto
 * <AUTHOR>
 * @version $Id: GetKsgljgByBzhkdidDTO.java, v 0.1 2020年06月15日 11时38分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GetKsgljgByBzhkdidDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标准化考点id */
    @NotBlank(message = "标准化考点id不能为空")
    private String bzhkdid;

}
