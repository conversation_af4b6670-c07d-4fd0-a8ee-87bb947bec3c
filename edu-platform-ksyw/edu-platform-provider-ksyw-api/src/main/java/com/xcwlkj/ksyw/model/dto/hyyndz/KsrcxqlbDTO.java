package com.xcwlkj.ksyw.model.dto.hyyndz;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class KsrcxqlbDTO implements Serializable {
    /** 序列ID */
    private static final long serialVersionUID = 1L;
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 考场编号 */
    @NotBlank(message = "考场编号不能为空")
    private String kcbh;
    /** 场次码 */
    @NotBlank(message = "场次码不能为空")
    private String ccm;
    /** 页码数 */
    @NotNull(message = "页码数不能为空")
    private Integer pageNo;
    /** 每页数据量 */
    @NotNull(message = "每页数据量不能为空")
    private Integer pageSize;
}
