/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.jcxxgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 27-场所工作人员-修改dto
 * <AUTHOR>
 * @version $Id: CsgzryXgDTO.java, v 0.1 2020年11月26日 17时28分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CsgzryXgDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 姓名 */
    @NotBlank(message = "姓名不能为空")
    private String csryxm;
    /** 性别 */
    private String csryxb;
    /** 学历 */
    private String csryxl;
    /** 手机号 */
    @NotBlank(message = "手机号不能为空")
    private String csrysjh;
    /** 场所名称 */
    private String csmc;
    /** 场所编号 */
    private String csbh;
    /** 场所类型（ksgljg / bzhkd / bms ） */
    private String cslx;
    /** 岗位名 */
    private String gwm;
    /** 岗位编码 */
    private String gwbm;
    /** 职称 */
    private String csryzc;
    /** 照片id */
    private String csryzp;
    /** 要删除照片id */
    private String zpsc;
    /** 身份证号 */
    private String csrysfzh;
    /** 开始工作时间 */
    private String ksgzsj;
    /** 编制类型 */
    private String bzlx;
    /** 场所人员编号 */
    @NotBlank(message = "场所人员编号不能为空")
    private String csrybh;
    /** 是否创建平台账号,1：是，0：不是 */
    private String sfcjptzh;
    /** 账号名 */
    private String zhm;
    /** 角色编号 */
    private String jsbh;
    /** 角色名称 */
    private String jsmc;
    /** 考试项目列表 */
    private List<KsxmlbItemDTO> ksxmlb;
    /** 电子邮箱 */
    private String csryemail;

}
