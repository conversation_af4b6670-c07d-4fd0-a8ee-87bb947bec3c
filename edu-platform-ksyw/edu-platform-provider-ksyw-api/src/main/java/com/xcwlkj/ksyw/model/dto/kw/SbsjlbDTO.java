/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.ksyw.model.dto.kw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 统计数据-上报数据列表dto
 * <AUTHOR>
 * @version $Id: SbsjlbDTO.java, v 0.1 2020年10月28日 18时47分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbsjlbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 场所编号 */
    private String csbh;
    /** 数据上报编号 */
    @NotBlank(message = "数据上报编号不能为空")
    private String tjsjsbbh;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;

}
