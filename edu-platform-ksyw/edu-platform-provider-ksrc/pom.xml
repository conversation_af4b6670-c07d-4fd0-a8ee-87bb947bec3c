<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>edu-platform-provider-ksrc</artifactId>
	<packaging>jar</packaging>
	<name>edu-platform-provider-ksrc</name>
	<url>http://www.xcwlkj.com</url>
	<description>杭州恒生数字-考生入场服务</description>
	<version>0.0.5.sfhy</version>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
		<docker.host>http://**************:12375</docker.host>
		<java.version>1.8</java.version>
	</properties>

	<!-- 父类信息 -->
	<parent>
		<groupId>com.hisome</groupId>
		<artifactId>edu-platform-ksyw</artifactId>
		<version>0.0.5.sfhy</version>
	</parent>

	<!-- 依赖明细 -->
	<dependencies>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.80</version>
		</dependency>

		<dependency>
			<groupId>org.xerial</groupId>
			<artifactId>sqlite-jdbc</artifactId>
			<version>3.25.2</version>
		</dependency>
		
		<!-- 考试业务 API -->
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>edu-platform-provider-ksyw-api</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>edu-platform-provider-resource</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>edu-platform-provider-data</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		<dependency>
			<groupId>com.github.albfernandez</groupId>
			<artifactId>javadbf</artifactId>
			<version>1.11.1</version>
		</dependency>
		<!-- 设备运维 API -->
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>edu-platform-provider-sbyw-api</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		<!-- 保密室 -->
 		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>edu-platform-provider-bmsjk-api</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>

		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>edu-platform-provider-sbjc-api</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		
		<!--注册中心客户端 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
		</dependency>
		<!-- 配置中心客户端 -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-config</artifactId>
		</dependency>
		<!--自省和监控的集成功能 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<!-- 重试 -->
		<dependency>
			<groupId>org.springframework.retry</groupId>
			<artifactId>spring-retry</artifactId>
		</dependency>
		<!--数据库连接 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
		</dependency>
		<!-- 批处理 -->
	    <dependency>
	      <groupId>org.springframework.boot</groupId>
	      <artifactId>spring-boot-starter-batch</artifactId>
	    </dependency>
    
		<dependency>
		    <groupId>cn.hutool</groupId>
		    <artifactId>hutool-all</artifactId>
		    <version>5.4.2</version>
		</dependency>
		
		<!-- https://mvnrepository.com/artifact/org.docx4j/docx4j -->
	 	<dependency>
		    <groupId>org.docx4j</groupId>
		    <artifactId>docx4j</artifactId>
		    <version>6.0.1</version>
		    <exclusions>
		        <exclusion>
		            <groupId>org.slf4j</groupId>
		            <artifactId>slf4j-log4j12</artifactId>
		        </exclusion>
		        <exclusion>
		            <groupId>log4j</groupId>
		            <artifactId>log4j</artifactId>
		        </exclusion>
		    </exclusions>
		</dependency>

		<!-- 阿里巴巴数据库连接池 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
		</dependency>
		<!--测试框架 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<!-- 模版引擎 -->
		<dependency>
			<groupId>org.freemarker</groupId>
			<artifactId>freemarker</artifactId>
		</dependency>
		<!-- 公共信息 -->
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>edu-platform-common</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		<!-- 消息 -->
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>hisome-provider-msgque-sdk</artifactId>
			<version>0.0.5</version>
		</dependency>
		<!-- 统一认证鉴权中心 API -->
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>xc-springcloud-provider-oauth-api</artifactId>
			<version>0.0.5.sfhy</version>
		</dependency>
		<!-- 文件系统SDK -->
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>hisome-provider-dfs-sdk</artifactId>
			<version>0.0.5</version>
		</dependency>
		<!-- 公共api包 -->
	    <dependency>
		    <groupId>com.xcwlkj</groupId>
		    <artifactId>xc-springcloud-provider-pubc-api</artifactId>
			<version>0.0.5.sfhy</version>
	    </dependency>
	    
	    <!-- https://mvnrepository.com/artifact/com.oracle/ojdbc14 -->
		<!-- <dependency>
		    <groupId>com.oracle</groupId>
		    <artifactId>ojdbc14</artifactId>
		    <version>10.2.0.3.0</version>
		</dependency> -->

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>2.2.7</version>
		</dependency>
		
		<dependency>
		  <groupId>com.aspose</groupId>
		  <artifactId>aspose-words</artifactId>
		  <version>18.8</version>
		  <classifier>jdk16</classifier>
		</dependency>
		
		<!-- 当当分布式定时任务 -->
        <dependency>
            <groupId>com.dangdang</groupId>
            <artifactId>elastic-job-lite-spring</artifactId>
            <version>2.1.5</version>
        </dependency>

		<!--zipkin链路跟踪-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-zipkin</artifactId>
		</dependency>
        
        <!--国密
		<dependency>
		    <groupId>org.bouncycastle</groupId>
		    <artifactId>bcprov-jdk15on</artifactId>
		    <version>1.56</version>
		</dependency>-->
        
		<dependency>
		    <groupId>net.lingala.zip4j</groupId>
		    <artifactId>zip4j</artifactId>
		    <version>2.9.0</version>
		</dependency>

	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
		
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<classifier>exec</classifier>
					<mainClass>com.xcwlkj.ksyw.ksrc.KsrcApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<!--docker镜像build插件 -->
			<plugin>
				<groupId>com.spotify</groupId>
				<artifactId>docker-maven-plugin</artifactId>
				<version>${docker-maven-plugin.version}</version>
				<configuration>
					<buildArgs>
						<JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
					</buildArgs>
					<imageName>${docker.image.prefix}/${project.artifactId}</imageName>
					<dockerDirectory>${project.basedir}/src/main/resources</dockerDirectory>
					<dockerHost>${docker.host}</dockerHost>
					<resources>
						<resource>
							<targetPath>/</targetPath>
							<directory>${project.build.directory}</directory>
							<include>${project.build.finalName}.jar</include>
						</resource>
					</resources>
				</configuration>
			</plugin>
			
		</plugins>

	</build>
</project>
