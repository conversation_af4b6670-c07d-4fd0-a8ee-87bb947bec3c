/**
 * xcwlkj.com Inc.
 * Copyright (c) 2024-2034 All Rights Reserved.
 */
package com.xcwlkj.ksyw.ksrc.mapper;

import java.util.List;

import com.xcwlkj.ksyw.data.mapper.common.CustomMapper;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.ksyw.ksrc.model.domain.KsSbkshysb;



/**
 * ks_sbkshysb数据库操作
 * <AUTHOR>
 * @version $Id: InitKsSbkshysbMapper.java, v 0.1 2024年01月11日 15时40分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface KsSbkshysbMapper extends CustomMapper<KsSbkshysb> {

    /**
	 * 分页查询ks_sbkshysb
	 * 
	 * @param example
	 * @return
	 */
	List<KsSbkshysb> pageList(KsSbkshysb example);
}
