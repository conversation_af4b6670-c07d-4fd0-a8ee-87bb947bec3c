package com.xcwlkj.ksyw.ksrc.job.config;

import javax.sql.DataSource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.dangdang.ddframe.job.event.JobEventConfiguration;
import com.dangdang.ddframe.job.event.rdb.JobEventRdbConfiguration;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperConfiguration;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperRegistryCenter;
import com.xcwlkj.ksyw.ksrc.job.ElasticJobListener;

/**
 * <AUTHOR> Wolf
 * @version $Id: ElasticJobConfig.java, v 0.1 May 27, 2020 6:10:04 PM White Wolf Exp $
 */
@Configuration
@ConditionalOnProperty(name="spring.application.name", havingValue = "ksyw-ksrc-service")
public class ElasticJobConfig {
    @Value("${elasticjob.serverlists}")
    private String serverlists;
    @Value("${elasticjob.namespace}")
    private String namespace;
    
    @Autowired
    private DataSource dataSource;

    @Bean
    public ZookeeperConfiguration zkConfig() {
        return new ZookeeperConfiguration(serverlists, namespace);
    }

    @Bean(initMethod = "init")
    public ZookeeperRegistryCenter zookeeperRegistryCenter(ZookeeperConfiguration config) {
        return new ZookeeperRegistryCenter(config);
    }

    /**
     * 将作业运行的痕迹进行持久化到DB
     *
     * @return
     */
    @Bean
    public JobEventConfiguration jobEventConfiguration() {
        return new JobEventRdbConfiguration(dataSource);
    }

    @Bean
    public ElasticJobListener elasticJobListener() {
        return new ElasticJobListener(100, 100);
    }
}
