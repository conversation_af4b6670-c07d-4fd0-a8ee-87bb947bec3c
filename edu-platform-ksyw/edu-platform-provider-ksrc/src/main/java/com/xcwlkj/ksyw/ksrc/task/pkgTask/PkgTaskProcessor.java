package com.xcwlkj.ksyw.ksrc.task.pkgTask;

import com.xcwlkj.ksyw.exceptions.KsywBusiException;
import com.xcwlkj.ksyw.ksrc.model.domain.KsKsxxExportStatus;
import com.xcwlkj.ksyw.ksrc.model.domain.KsKsxxExportTask;
import com.xcwlkj.ksyw.ksrc.model.dos.PackParamDO;
import com.xcwlkj.ksyw.ksrc.model.enums.ExportTaskConfEnum;
import com.xcwlkj.ksyw.ksrc.model.enums.PackEnum;
import com.xcwlkj.ksyw.ksrc.model.enums.TaskCompleteStatusEnum;
import com.xcwlkj.ksyw.ksrc.service.ExamDataService;
import com.xcwlkj.ksyw.ksrc.service.KsKsxxExportExceptionService;
import com.xcwlkj.ksyw.ksrc.service.KsKsxxExportStatusService;
import com.xcwlkj.ksyw.ksrc.service.KsKsxxExportTaskService;
import com.xcwlkj.ksyw.ksrc.service.impl.dataPkg.DataPkgServices;
import com.xcwlkj.ksyw.ksrc.task.*;
import com.xcwlkj.ksyw.model.vo.datapack.StartPackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component("pkgTaskProcessor")
@Slf4j
public class PkgTaskProcessor extends AbstractTaskProcessor<PkgParam, KsKsxxExportTask> {

    @Resource
    private KsKsxxExportTaskService ksKsxxExportTaskService;
    @Resource
    private KsKsxxExportStatusService ksKsxxExportStatusService;
    @Resource
    private ExamDataService examDataService;
    @Resource
    private KsKsxxExportExceptionService ksxxExportExceptionService;
    @Resource
    private DataPkgServices dataPkgServices;

    @Override
    public ProcessorEnum getProcessorType() {
        return ProcessorEnum.PkgTaskProcessor;
    }

    @Override
    public TaskResult<KsKsxxExportTask> taskExecute(PkgParam param) {
        KsKsxxExportTask ksKsxxExportTask = ksKsxxExportTaskService.selectByPrimaryKey(param.getTaskId());

        // 获取不到任务时延时1s重新获取
        if (ksKsxxExportTask == null){
            try {
                Thread.sleep(1000);
            } catch (InterruptedException interruptedException) {
                interruptedException.printStackTrace();
            }
            ksKsxxExportTask = ksKsxxExportTaskService.selectByPrimaryKey(param.getTaskId());
            if (ksKsxxExportTask == null) {
                throw new KsywBusiException("任务[" + param.getTaskId() + "]已不存在");
            }
        }

        ksKsxxExportTask.setComplete(TaskCompleteStatusEnum.Doing.getCode());
        ksKsxxExportTaskService.updateByPrimaryKey(ksKsxxExportTask);

        // 配置打包参数
        PackParamDO packParamDO = configurePackParamDO(param, ksKsxxExportTask.getKsjhbh());

        try {
            examDataService.packZip(packParamDO);

            KsKsxxExportTask completeTask = new KsKsxxExportTask();
            completeTask.setId(param.getTaskId());
            completeTask.setCompleteTime(new Date());
            completeTask.setComplete(TaskCompleteStatusEnum.Done.getCode());

            // 日志
            String dfsId = ksxxExportExceptionService.uploadLogToDfs(param.getTaskId());
            completeTask.setLogDfsFileObjUri(dfsId);

            ksKsxxExportTaskService.updateByPrimaryKeySelective(completeTask);

        } catch (Exception e){
            log.error(e.getMessage(), e);
            KsKsxxExportTask failTask = new KsKsxxExportTask();
            failTask.setId(param.getTaskId());
            failTask.setComplete(TaskCompleteStatusEnum.Fail.getCode());

            // 日志
            String dfsId = ksxxExportExceptionService.uploadLogToDfs(param.getTaskId());
            failTask.setLogDfsFileObjUri(dfsId);

            ksKsxxExportTaskService.updateByPrimaryKeySelective(failTask);
        }

        return new TaskResult<>(TaskResultTypeEnum.Success, ksKsxxExportTask);
    }

    @Override
    public void reSetTask() {
        Example example = new Example(KsKsxxExportTask.class);
        example.createCriteria()
                .andIn("complete", Arrays.asList(0,1));
        KsKsxxExportTask task = new KsKsxxExportTask();
        task.setComplete(TaskCompleteStatusEnum.Fail.getCode());
        ksKsxxExportTaskService.updateByExampleSelective(task, example);

        ksKsxxExportStatusService.clearDoing();
    }

    @Override
    public void handleTimeOuntEvent(PkgParam param) {
        Example example = new Example(KsKsxxExportTask.class);
        example.createCriteria()
                .andEqualTo("id", param.getTaskId());
        KsKsxxExportTask task = new KsKsxxExportTask();
        task.setComplete(TaskCompleteStatusEnum.Fail.getCode());
        ksKsxxExportTaskService.updateByExampleSelective(task, example);
    }

    private PackParamDO configurePackParamDO(PkgParam param, String ksjhbh){
        PackParamDO packParamDO = new PackParamDO();
        packParamDO.setKsjhbh(ksjhbh);
        packParamDO.setTaskId(param.getTaskId());
        packParamDO.setPackMode(1);
        packParamDO.setBzhkdid(String.join(",",param.getKdList()));
        packParamDO.setUseCache(param.getUseCache());
        List<PackEnum> packEnumList = new ArrayList<>();
        if ((param.getTaskConf() & ExportTaskConfEnum.KS.getCode()) == ExportTaskConfEnum.KS.getCode()){
            packEnumList.add(PackEnum.Pack_GxHisomeStu);
        }
        if ((param.getTaskConf() & ExportTaskConfEnum.JKRY.getCode()) == ExportTaskConfEnum.JKRY.getCode()
                && dataPkgServices.getDataPkgServiceMap().get(PackEnum.Pack_GxHisomeJkry).isPkg(ksjhbh)){
            packEnumList.add(PackEnum.Pack_GxHisomeJkry);
        }
        if ((param.getTaskConf() & ExportTaskConfEnum.JKRYBP.getCode()) == ExportTaskConfEnum.JKRYBP.getCode()
                && dataPkgServices.getDataPkgServiceMap().get(PackEnum.Pack_GxHisomeJkryBp).isPkg(ksjhbh)){
            packEnumList.add(PackEnum.Pack_GxHisomeJkryBp);
        }
        if ((param.getTaskConf() & ExportTaskConfEnum.KCPZ.getCode()) == ExportTaskConfEnum.KCPZ.getCode()){
            packEnumList.add(PackEnum.Pack_GxHisomeCommon);
        }
        if ((param.getTaskConf() & ExportTaskConfEnum.JKRYFB.getCode()) == ExportTaskConfEnum.JKRYFB.getCode()){
            packEnumList.add(PackEnum.Pack_GxHisomeFbJkry);
        }
        if ((param.getTaskConf() & ExportTaskConfEnum.KSZP.getCode()) == ExportTaskConfEnum.KSZP.getCode()){
            packEnumList.add(PackEnum.Pack_GxHisomeKdKsZp);
        }
        if ((param.getTaskConf() & ExportTaskConfEnum.JKRYZP.getCode()) == ExportTaskConfEnum.JKRYZP.getCode()
                && dataPkgServices.getDataPkgServiceMap().get(PackEnum.Pack_GxHisomeKdJkryZp).isPkg(ksjhbh)){
            packEnumList.add(PackEnum.Pack_GxHisomeKdJkryZp);
        }
        packParamDO.setPackEnumList(packEnumList);
        return packParamDO;
    }
}
