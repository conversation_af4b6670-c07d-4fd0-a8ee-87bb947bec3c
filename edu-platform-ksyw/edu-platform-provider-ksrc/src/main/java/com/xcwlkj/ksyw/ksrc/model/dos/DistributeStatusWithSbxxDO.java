package com.xcwlkj.ksyw.ksrc.model.dos;

import lombok.Data;

@Data
public class DistributeStatusWithSbxxDO {
    /** 场所编号 */
    private String csbh;
    /** 场所名称 */
    private String csmc;
    /** 场所绑定设备的序列号 */
    private String xlh;
    /** 考生数据包下发情况 */
    private Integer stuinfPkgStatus;
    /** 配置数据包下发情况 */
    private Integer commonPkgStatus;
    /** 监考人员基本数据包下发情况 */
    private Integer jkryjbxxPkgStatus;
    /** 监考人员编排数据包下发情况 */
    private Integer jkrybpxxPkgStatus;
    /** 考生照片数据包下发情况 */
    private Integer stuzpPkgStatus;
    /** 监考人员照片数据包下发情况 */
    private Integer jkryzpPkgStatus;
}
