//package com.xcwlkj.ksyw.ksrc.taskcenter;
//
//import com.alibaba.fastjson.JSONArray;
//import com.alibaba.fastjson.JSONObject;
//import com.xcwlkj.ksyw.data.model.domain.KdKdxx;
//import com.xcwlkj.ksyw.data.model.domain.KsKsjh;
//import com.xcwlkj.ksyw.data.service.KdKdxxService;
//import com.xcwlkj.ksyw.data.service.KsKsjhService;
//import com.xcwlkj.ksyw.ksrc.service.KdKdxxHysjbService;
//import com.xcwlkj.ksyw.resource.model.domain.CsBzhkd;
//import com.xcwlkj.ksyw.resource.service.CsBzhkdService;
//import com.xcwlkj.model.enums.GeneralMsgTopic;
//import com.xcwlkj.model.enums.ScztEnum;
//import com.xcwlkj.model.enums.XcTaskChannelEnum;
//import com.xcwlkj.msgque.model.domain.XcMsgModel;
//import com.xcwlkj.msgque.service.AbstractBroadCastMsgExecutor;
//import com.xcwlkj.util.StringUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.stereotype.Service;
//import tk.mybatis.mapper.entity.Example;
//
//import java.util.List;
//
///**
// *  @Description 接收数据包下载状态
// *  <AUTHOR>
// *  @Date 2021/6/23 14:09
// *  @Version 1.0
// */
//@Service("PacketDownloadStatusNotice")
//@Slf4j
//@ConditionalOnProperty(name="spring.application.name", havingValue = "ksyw-ksrc-service")
//public class PacketDownloadStatusNotice extends AbstractBroadCastMsgExecutor {
//
//    @Autowired
//    private KsKsjhService ksKsjhService;
//
//    @Autowired
//    private KdKdxxService kdKdxxService;
//
//    @Autowired
//    private KdKdxxHysjbService kdKdxxHysjbService;
//
//    @Autowired
//    private CsBzhkdService csBzhkdService;
//
//    @Override
//    public void exec(XcMsgModel model) {
//        String msgParam = model.getMsgParam();
//        log.info("接受到的消息：{}", msgParam);
//        if (StringUtil.isBlank(msgParam)) {
//            log.info("message PacketDownloadStatus is blank");
//            return;
//        }
//
//        JSONObject jsonObject = JSONObject.parseObject(msgParam);
//        JSONObject dataObject = jsonObject.getJSONObject("data");
//        JSONArray kdlbJSONArray = dataObject.getJSONArray("kdlb");
//        kdlbJSONArray.forEach(x -> {
//            JSONObject object = (JSONObject) x;
//            String ksjhbh = object.getString("ksjhbh");
//            if (StringUtils.isBlank(ksjhbh)) {
//                log.info("未上传考试计划");
//                return;
//            }
//            Example ksKsjhExample = new Example(KsKsjh.class);
//            ksKsjhExample.createCriteria().andEqualTo("ksjhbh", ksjhbh).andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
//            List<KsKsjh> ksKsjhList = ksKsjhService.selectByExample(ksKsjhExample);
//            if (CollectionUtils.isEmpty(ksKsjhList)) {
//                log.info("考试计划编号 {} 不存在", ksjhbh);
//                return;
//            }
//            String ksjhmc = ksKsjhList.get(0).getMc();
//            String orgCode = object.getString("orgCode");
//            Example kdKdxxQueryExample = new Example(KdKdxx.class);
//            kdKdxxQueryExample.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
//                    .andEqualTo("ksjhbh", ksjhbh).andEqualTo("bzhkdid", orgCode);
//            List<KdKdxx> kdKdxxList = kdKdxxService.selectByExample(kdKdxxQueryExample);
//            if (CollectionUtils.isEmpty(kdKdxxList)) {
//                log.info("找不到标准化考点编号对应的考点: bzhkdid={}", orgCode);
//                return;
//            }
//            KdKdxx kdKdxx = kdKdxxList.get(0);
//            String kdbh = kdKdxx.getKdbh();
//            String kdmc = kdKdxx.getKdmc();
//
//            Example csBzhkdExample = new Example(CsBzhkd.class);
//            csBzhkdExample.createCriteria().andEqualTo("sczt", ScztEnum.NOTDEL.getCode()).andEqualTo("bzhkdid", orgCode);
//            List<CsBzhkd> csBzhkdList = csBzhkdService.selectByExample(csBzhkdExample);
//            CsBzhkd csBzhkd = csBzhkdList.get(0);
//            String kdfzrxm = csBzhkd.getKdfzrxm();
//            String kdfzrdh = csBzhkd.getKdfzrdh();
//
//            kdKdxxHysjbService.sendSms(ksjhbh, ksjhmc, kdbh, kdmc, kdfzrxm, kdfzrdh);
//        });
//    }
//
//    @Override
//    public String getMsgTopic() {
//        return GeneralMsgTopic.SFHY_KSJH_PKG_STATUS.getCode();
//    }
//
//    @Override
//    public String getMsgChannel() {
//        return XcTaskChannelEnum.SFHY.getCode();
//    }
//}
