package com.xcwlkj.ksyw.ksrc.third.channel.unifyaccess.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/**
 * 对接统一接入平台URL地址
 * <AUTHOR>
 *
 */
public enum UnifyAccessUrlEnum {

	login("/remote/access/user/login","登录")
	,validate("/remote/access/device/validate","设备验签")
	,sendCommand("/remote/access/device/sendCommand","下发给设备命令")
	,batchCommand("/remote/access/device/batchCommand","批量下发给设备指令")//listByDeviceNames
	,queryDeviceStatus("/remote/access/device/listByDeviceNames","根据设备序列号数组查询设备状态")
	,queryBatchCommandResult("/remote/access/device/batchSendCommandResult","批次命令下发结果查询")
	;
	
	private String code;
    private String desc;

    private UnifyAccessUrlEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

    public static UnifyAccessUrlEnum get(String code) {
        for (UnifyAccessUrlEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }
}
