#!/bin/bash

echo "========================================"
echo "数据库测试框架运行脚本"
echo "========================================"

echo ""
echo "正在运行数据库测试框架验证..."
echo ""

# 设置Maven参数
export MAVEN_OPTS="-Xmx1024m -XX:MaxPermSize=256m"

# 运行框架验证测试
echo "[1/3] 运行框架基础功能测试..."
mvn test -Dtest=DatabaseFrameworkTest#testFrameworkBasics -Dspring.profiles.active=database-test -q

if [ $? -ne 0 ]; then
    echo "框架基础功能测试失败！"
    exit 1
fi

echo "[2/3] 运行数据库连接测试..."
mvn test -Dtest=DatabaseFrameworkTest#testDatabaseConnection -Dspring.profiles.active=database-test -q

if [ $? -ne 0 ]; then
    echo "数据库连接测试失败！"
    exit 1
fi

echo "[3/3] 运行Bean加载检查..."
mvn test -Dtest=DatabaseFrameworkTest#testLoadedBeans -Dspring.profiles.active=database-test -q

if [ $? -ne 0 ]; then
    echo "Bean加载检查失败！"
    exit 1
fi

echo ""
echo "========================================"
echo "所有测试通过！数据库测试框架运行正常。"
echo "========================================"
echo ""

echo "你现在可以："
echo "1. 运行具体的业务测试: mvn test -Dtest=YourTestClass -Dspring.profiles.active=database-test"
echo "2. 运行所有数据库测试: mvn test -Dspring.profiles.active=database-test"
echo "3. 在IDE中直接运行测试类"
echo ""
