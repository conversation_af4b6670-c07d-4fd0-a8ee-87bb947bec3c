# 数据库驱动问题解决方案

## 问题描述
运行测试时出现 `java.lang.ClassNotFoundException: com.mysql.cj.jdbc.Driver` 错误。

## 问题原因
1. **缺少MySQL驱动依赖**：`eeip-standalone` 模块的 `pom.xml` 中缺少 `mysql-connector-java` 依赖
2. **驱动类名不匹配**：配置文件中使用了新版本的驱动类名，但项目使用的是旧版本驱动
3. **Maven依赖下载问题**：网络连接问题导致无法下载依赖

## 解决方案

### 1. 已修复的内容

#### ✅ 添加了MySQL驱动依赖
在 `eeip-standalone/pom.xml` 中已添加：
```xml
<!-- MySQL数据库驱动 -->
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
</dependency>
```

#### ✅ 修正了驱动类名
在 `application-database-test.yml` 中已修正：
```yaml
datasource:
  driver-class-name: com.mysql.jdbc.Driver  # 旧版本驱动类名
```

### 2. 如果仍有问题，请尝试以下解决方案

#### 方案A：使用本地Maven仓库
如果网络问题导致依赖下载失败，可以：

1. **检查本地仓库**：
```bash
ls ~/.m2/repository/mysql/mysql-connector-java/
```

2. **手动下载MySQL驱动**：
   - 下载 `mysql-connector-java-5.1.47.jar`
   - 放到项目的 `lib` 目录下
   - 在 `pom.xml` 中添加系统依赖：

```xml
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>5.1.47</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/lib/mysql-connector-java-5.1.47.jar</systemPath>
</dependency>
```

#### 方案B：更新Maven配置
在 `~/.m2/settings.xml` 中配置镜像：
```xml
<mirrors>
    <mirror>
        <id>aliyun</id>
        <mirrorOf>central</mirrorOf>
        <name>Aliyun Central</name>
        <url>https://maven.aliyun.com/repository/central</url>
    </mirror>
</mirrors>
```

#### 方案C：使用新版本MySQL驱动
如果要使用新版本驱动，需要：

1. **更新pom.xml**：
```xml
<dependency>
    <groupId>mysql</groupId>
    <artifactId>mysql-connector-java</artifactId>
    <version>8.0.33</version>
</dependency>
```

2. **更新配置文件**：
```yaml
datasource:
  driver-class-name: com.mysql.cj.jdbc.Driver
  url: jdbc:mysql://*************:3306/eeip_alone?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
```

### 3. 验证修复

#### 方法1：运行简单测试
```bash
cd eeip-standalone
mvn test -Dtest=SimpleDatabaseTest#testDriverClassOnly
```

#### 方法2：检查依赖
```bash
mvn dependency:tree | grep mysql
```

#### 方法3：编译检查
```bash
mvn compile
```

### 4. 如果Maven依赖问题持续存在

#### 临时解决方案：跳过依赖检查
```bash
mvn test -Dtest=SimpleDatabaseTest -Dmaven.test.skip.exec=false -o
```

#### 或者使用IDE运行
1. 在IDE中导入项目
2. 确保MySQL驱动jar包在classpath中
3. 直接运行测试类

### 5. 验证数据库测试框架

一旦MySQL驱动问题解决，可以按以下步骤验证框架：

```bash
# 1. 验证驱动加载
mvn test -Dtest=SimpleDatabaseTest#testDriverClassOnly

# 2. 验证数据库连接
mvn test -Dtest=SimpleDatabaseTest#testDatabaseConnection

# 3. 验证Spring框架
mvn test -Dtest=DatabaseFrameworkTest -Dspring.profiles.active=database-test

# 4. 运行完整测试
mvn test -Dspring.profiles.active=database-test
```

## 常见错误和解决方案

### 错误1：ClassNotFoundException: com.mysql.jdbc.Driver
**解决**：确保添加了mysql-connector-java依赖

### 错误2：ClassNotFoundException: com.mysql.cj.jdbc.Driver  
**解决**：使用旧版本驱动类名或升级到新版本驱动

### 错误3：Could not resolve dependencies
**解决**：检查网络连接，配置Maven镜像，或使用本地依赖

### 错误4：Connection refused
**解决**：检查数据库服务是否启动，网络是否可达

## 推荐的测试流程

1. **首先运行简单测试**：`SimpleDatabaseTest`
2. **然后运行框架测试**：`DatabaseFrameworkTest`  
3. **最后运行业务测试**：`RunOnlyWithDatabaseTest`

这样可以逐步排查问题，确保每一层都正常工作。
