<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.hisome</groupId>
	<artifactId>eeip-standalone</artifactId>
	<packaging>jar</packaging>
	<name>eeip-standalone</name>
	<url>http://www.hisome.com</url>
	<description>单体服务</description>
	<!-- 父类信息 -->
	<parent>
		<groupId>com.xcwlkj</groupId>
		<artifactId>xcCloud-root</artifactId>
		<version>0.0.5</version>
	</parent>
	<!-- 依赖明细 -->
	<dependencies>
		<dependency>
			<groupId>com.xcwlkj</groupId>
			<artifactId>xc-provider-pubc</artifactId>
			<version>0.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-basicinfo</artifactId>
			<version>0.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-biz</artifactId>
			<version>0.0.5</version>
		</dependency>
		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-evaluation</artifactId>
			<version>0.0.5</version>
		</dependency>

		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-attendance</artifactId>
			<version>0.0.5</version>
		</dependency>

		<dependency>
			<groupId>com.hisome</groupId>
			<artifactId>eeip-provider-identity-verify</artifactId>
			<version>0.0.5</version>
		</dependency>

		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>5.11.0</version>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna-platform</artifactId>
			<version>5.11.0</version>
		</dependency>
		<dependency>
			<groupId>com.aspose</groupId>
			<artifactId>aspose-words</artifactId>
			<version>18.8</version>
			<classifier>jdk16</classifier>
		</dependency>


		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>********</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-api</artifactId>
			<version>5.11.0</version> <!-- Use the latest stable version -->
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<version>5.11.0</version> <!-- Use the latest stable version -->
			<scope>test</scope>
		</dependency>
	</dependencies>
	<!-- 单体环境-->
	<profiles>
		<profile>
			<id>alone</id>
		</profile>
	</profiles>
	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.xcwlkj.standalone.StandaloneApplication</mainClass>
				</configuration>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>${maven.resources.plugin.version}</version>
			</plugin>
		</plugins>
	</build>

</project>
