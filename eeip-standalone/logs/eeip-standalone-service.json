{"@timestamp":"2025-07-25T05:42:09.121Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9a4b8986] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:42:09.559Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:42:09.565Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:42:18.906Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:42:18.908Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:42:18.908Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:42:18.908Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:42:19.887Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:42:21.361Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:42:23.025Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:42:23.038Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T05:42:23.904Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@2e836434],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@36288e31],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@e8d35e4],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@1920a0a5],]"}
{"@timestamp":"2025-07-25T05:42:23.904Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T05:42:29.650Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T05:42:29.659Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T05:42:30.961Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T05:42:31.062Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T05:42:31.372Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:42:31.388Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T05:42:31.406Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:42:31.419Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T05:42:31.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:42:31.421Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T05:42:31.429Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:42:31.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:42:31.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T05:42:31.439Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:42:31.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:42:31.441Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T05:42:31.448Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:42:31.449Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:42:31.450Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T05:42:31.456Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:42:31.469Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T05:42:31.469Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T05:42:38.069Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'BaseinfoManagerController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'baseInfoService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'xk.sfzsyljt' in value \"${xk.sfzsyljt}\""}
{"@timestamp":"2025-07-25T05:42:38.100Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:42:38.511Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$9a4b8986] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:42:38.915Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:42:38.916Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:42:43.288Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:42:43.289Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:42:43.289Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:42:43.289Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:42:43.709Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:42:44.291Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:42:45.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:42:45.408Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'smsServiceImpl': Unsatisfied dependency expressed through field 'iAcsClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acsClient' defined in class path resource [com/xcwlkj/pubc/config/SmsConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyuncs.IAcsClient]: Factory method 'acsClient' threw exception; nested exception is java.lang.NullPointerException"}
{"@timestamp":"2025-07-25T05:42:45.416Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:42:45.417Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17840","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@683dbc2c] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@776b83cc]"}
{"@timestamp":"2025-07-25T05:47:28.224Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bf13c210] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:47:28.636Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:47:28.643Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:47:39.279Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:47:39.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:47:39.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:47:39.281Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:47:40.298Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:47:41.838Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:47:43.450Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:47:43.463Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T05:47:44.319Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@408c030b],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@2a88880d],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@3474184e],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@679f1314],]"}
{"@timestamp":"2025-07-25T05:47:44.320Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T05:47:49.752Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T05:47:49.760Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T05:47:51.083Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T05:47:51.181Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T05:47:51.475Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:47:51.493Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T05:47:51.512Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:47:51.527Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T05:47:51.528Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:47:51.529Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T05:47:51.538Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:47:51.539Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:47:51.540Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T05:47:51.546Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:47:51.548Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:47:51.549Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T05:47:51.557Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:47:51.558Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:47:51.559Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T05:47:51.567Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:47:51.579Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T05:47:51.579Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T05:47:58.313Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'TjglManagerController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'pktjBpdxpmService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'pj.jspm.defaultScore' in value \"${pj.jspm.defaultScore}\""}
{"@timestamp":"2025-07-25T05:47:58.347Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:47:58.703Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bf13c210] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:47:59.020Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:47:59.022Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:48:03.411Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:48:03.411Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:48:03.412Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:48:03.412Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:48:03.845Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:48:04.431Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:48:05.609Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:48:05.611Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'smsServiceImpl': Unsatisfied dependency expressed through field 'iAcsClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acsClient' defined in class path resource [com/xcwlkj/pubc/config/SmsConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyuncs.IAcsClient]: Factory method 'acsClient' threw exception; nested exception is java.lang.NullPointerException"}
{"@timestamp":"2025-07-25T05:48:05.618Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:48:05.619Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"6796","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1dac5ef] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@6497b078]"}
{"@timestamp":"2025-07-25T05:50:43.915Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a5ad58f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:50:44.347Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:50:44.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:50:54.238Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:50:54.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:50:54.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:50:54.241Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:50:55.367Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:50:56.841Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:50:58.526Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:50:58.541Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T05:50:59.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@2175ecb4],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@290150cb],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@48cab21a],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@4a57c157],]"}
{"@timestamp":"2025-07-25T05:50:59.405Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T05:51:04.728Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T05:51:04.736Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T05:51:06.155Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T05:51:06.247Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T05:51:06.534Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:51:06.549Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T05:51:06.566Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:51:06.577Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T05:51:06.579Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:51:06.579Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T05:51:06.587Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:51:06.588Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:51:06.589Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T05:51:06.597Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:51:06.599Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:51:06.599Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T05:51:06.606Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:51:06.607Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:51:06.608Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T05:51:06.616Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:51:06.628Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T05:51:06.629Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T05:51:13.505Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'TjglManagerController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'pktjBpdxpmService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'pj.jspm.defaultScore' in value \"${pj.jspm.defaultScore}\""}
{"@timestamp":"2025-07-25T05:51:13.540Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:51:13.912Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$a5ad58f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:51:14.227Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:51:14.229Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:51:18.582Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:51:18.582Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:51:18.582Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:51:18.582Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:51:19.092Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:51:19.740Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:51:20.914Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:51:20.916Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'smsServiceImpl': Unsatisfied dependency expressed through field 'iAcsClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acsClient' defined in class path resource [com/xcwlkj/pubc/config/SmsConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyuncs.IAcsClient]: Factory method 'acsClient' threw exception; nested exception is java.lang.NullPointerException"}
{"@timestamp":"2025-07-25T05:51:20.924Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:51:20.925Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"32412","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@37883b97] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@55f616cf]"}
{"@timestamp":"2025-07-25T05:54:07.662Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bf13c210] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:54:08.072Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:54:08.080Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:54:17.455Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:54:19.963Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T05:54:20.083Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T05:54:21.470Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T05:54:21.615Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T05:54:21.891Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:21.908Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T05:54:21.925Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:21.937Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T05:54:21.938Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:21.938Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T05:54:21.946Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:21.948Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:21.948Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T05:54:21.955Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:21.957Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:21.957Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T05:54:21.965Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:21.966Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:21.966Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T05:54:21.974Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:21.987Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T05:54:21.988Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T05:54:25.173Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:54:25.174Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:54:25.174Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:54:25.174Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:54:26.018Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:54:27.698Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:54:27.713Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T05:54:28.664Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@5a94ad4b],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@2f4ab8c6],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@109bb67e],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@7f28a29],]"}
{"@timestamp":"2025-07-25T05:54:28.664Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T05:54:36.500Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'TjglManagerController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'pktjBpdxpmService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'pj.jspm.defaultScore' in value \"${pj.jspm.defaultScore}\""}
{"@timestamp":"2025-07-25T05:54:36.541Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:54:36.969Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bf13c210] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:54:37.332Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:54:37.335Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:54:41.916Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T05:54:43.130Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T05:54:43.137Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T05:54:44.479Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T05:54:44.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T05:54:44.561Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:44.562Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T05:54:44.570Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:44.570Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T05:54:44.572Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:44.572Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T05:54:44.579Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:44.582Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:44.582Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T05:54:44.591Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:44.592Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:44.593Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T05:54:44.600Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:44.602Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T05:54:44.602Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T05:54:44.610Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T05:54:44.618Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T05:54:44.618Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T05:54:47.406Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T05:54:47.407Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T05:54:47.407Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T05:54:47.407Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T05:54:47.959Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T05:54:49.194Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T05:54:49.196Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'smsServiceImpl': Unsatisfied dependency expressed through field 'iAcsClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acsClient' defined in class path resource [com/xcwlkj/pubc/config/SmsConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyuncs.IAcsClient]: Factory method 'acsClient' threw exception; nested exception is java.lang.NullPointerException"}
{"@timestamp":"2025-07-25T05:54:49.207Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T05:54:49.208Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"15368","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1dac5ef] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@6497b078]"}
{"@timestamp":"2025-07-25T05:56:05.596Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bf13c210] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:56:06.032Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:56:06.038Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:56:10.318Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.core.util.RedisUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T05:56:10.643Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.core.util.RedisUtil' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.core.util.RedisUtil' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T05:56:10.988Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$bf13c210] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T05:56:11.309Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T05:56:11.310Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T05:56:12.431Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.core.util.RedisUtil' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T05:56:12.601Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.core.util.RedisUtil' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.core.util.RedisUtil' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T05:56:12.603Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"27784","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1dac5ef] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@6497b078]"}
{"@timestamp":"2025-07-25T06:00:33.202Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8f37d806] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:00:33.608Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T06:00:33.615Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T06:00:38.386Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T06:00:38.777Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T06:00:39.153Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$8f37d806] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:00:39.530Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T06:00:39.532Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T06:00:41.000Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T06:00:41.218Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T06:00:41.221Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34356","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@1dac5ef] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@6497b078]"}
{"@timestamp":"2025-07-25T06:06:41.317Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$7a4cb211] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:06:41.730Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T06:06:41.735Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T06:06:51.324Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T06:06:51.327Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T06:06:51.327Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T06:06:51.328Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T06:06:52.462Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T06:06:54.316Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T06:06:56.190Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T06:06:56.206Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T06:06:57.494Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@bc21b3f],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@408880cc],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@28c14224],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@754a9dfa],]"}
{"@timestamp":"2025-07-25T06:06:57.494Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T06:07:07.506Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T06:07:07.516Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T06:07:09.199Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T06:07:09.313Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T06:07:09.828Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:07:09.845Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: defaultPlat(String)"}
{"@timestamp":"2025-07-25T06:07:09.861Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:07:09.875Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T06:07:09.877Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:07:09.877Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerUrl(String)"}
{"@timestamp":"2025-07-25T06:07:09.886Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:07:09.888Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:07:09.888Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServerChannel(String)"}
{"@timestamp":"2025-07-25T06:07:09.896Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:07:09.897Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:07:09.898Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppId(String)"}
{"@timestamp":"2025-07-25T06:07:09.905Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:07:09.907Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==>  Preparing: SELECT id,t_code,t_name,t_type,t_value,t_catalog,t_value_ext1,t_value_ext2,t_desc,t_sort,create_time,update_time FROM jy_sys_dict WHERE ( ( t_code = ? ) ) "}
{"@timestamp":"2025-07-25T06:07:09.907Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"==> Parameters: HISOME_fileServiceAppSecret(String)"}
{"@timestamp":"2025-07-25T06:07:09.914Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.m.J.selectOneByExample","rest":"<==      Total: 1"}
{"@timestamp":"2025-07-25T06:07:09.928Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T06:07:09.928Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T06:07:11.418Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动中...."}
{"@timestamp":"2025-07-25T06:07:11.419Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==>  Preparing: SELECT job_id,job_name,bean_name,params,cron_expression,status,remark,create_time,update_time FROM schedule_job "}
{"@timestamp":"2025-07-25T06:07:11.420Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"==> Parameters: "}
{"@timestamp":"2025-07-25T06:07:11.430Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.mapper.ScheduleJobMapper.selectAll","rest":"<==      Total: 10"}
{"@timestamp":"2025-07-25T06:07:11.450Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[teskTask-jobTest111]"}
{"@timestamp":"2025-07-25T06:07:11.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbzxztCxAndGxTaskService-jobSbzxztGx]"}
{"@timestamp":"2025-07-25T06:07:11.452Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[onlineStatusTaskService-jobOnlineStatus]"}
{"@timestamp":"2025-07-25T06:07:11.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[subscriberDevInfoToMqttTaskService-jobSubscribeMqtt]"}
{"@timestamp":"2025-07-25T06:07:11.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sjxfsbCxxfTaskService-sjxfsbCxxf]"}
{"@timestamp":"2025-07-25T06:07:11.453Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[sbxxReportTaskService-设备信息上报]"}
{"@timestamp":"2025-07-25T06:07:11.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[ksjhSjdsxfTaskService-ksjhSjdsxf]"}
{"@timestamp":"2025-07-25T06:07:11.454Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceVersionUpdateTaskService-设备固件版本更新]"}
{"@timestamp":"2025-07-25T06:07:11.455Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[deviceEventDeleteTaskService-设备事件删除]"}
{"@timestamp":"2025-07-25T06:07:11.455Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动[kssjDsscsjptTaskService-考试数据定时上传上级平台]"}
{"@timestamp":"2025-07-25T06:07:11.459Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.i.s.impl.ScheduleJobServiceImpl","rest":"###定时任务初始化启动完成"}
{"@timestamp":"2025-07-25T06:07:16.117Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'TjglManagerController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'pktjBpdxpmService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'pj.jspm.defaultScore' in value \"${pj.jspm.defaultScore}\""}
{"@timestamp":"2025-07-25T06:07:16.164Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T06:07:16.591Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$7a4cb211] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:07:16.952Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T06:07:16.954Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T06:07:21.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T06:07:21.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T06:07:21.631Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T06:07:21.632Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T06:07:22.073Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T06:07:22.692Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T06:07:23.832Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T06:07:23.833Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'smsServiceImpl': Unsatisfied dependency expressed through field 'iAcsClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acsClient' defined in class path resource [com/xcwlkj/pubc/config/SmsConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyuncs.IAcsClient]: Factory method 'acsClient' threw exception; nested exception is java.lang.NullPointerException"}
{"@timestamp":"2025-07-25T06:07:23.840Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T06:07:23.842Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30820","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@484970b0] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@51f116b8]"}
{"@timestamp":"2025-07-25T06:20:36.954Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$755828c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:20:37.350Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T06:20:37.356Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T06:20:46.398Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T06:20:46.402Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T06:20:46.403Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T06:20:46.404Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T06:20:47.315Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T06:20:48.773Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T06:20:50.553Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T06:20:50.567Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"加载SMS Bean IAcsClient OK"}
{"@timestamp":"2025-07-25T06:20:51.646Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Current Hystrix plugins configuration is [concurrencyStrategy [com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategyDefault@25705886],eventNotifier [com.netflix.hystrix.strategy.eventnotifier.HystrixEventNotifierDefault@1537e961],metricPublisher [com.netflix.hystrix.strategy.metrics.HystrixMetricsPublisherDefault@647df9e6],propertiesStrategy [com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategyDefault@a129049],]"}
{"@timestamp":"2025-07-25T06:20:51.647Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.c.f.FeignHystrixConcurrencyStrategyIntellif","rest":"Registering Sleuth Hystrix Concurrency Strategy."}
{"@timestamp":"2025-07-25T06:21:00.868Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'TjglManagerController': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'pktjBpdxpmService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'pj.jspm.defaultScore' in value \"${pj.jspm.defaultScore}\""}
{"@timestamp":"2025-07-25T06:21:00.892Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T06:21:01.262Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$755828c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T06:21:01.610Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T06:21:01.612Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.t.example.ExampleServiceDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T06:21:06.164Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsServerUrl=http://************:8811"}
{"@timestamp":"2025-07-25T06:21:06.165Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsChannel=ZJKSZHPT"}
{"@timestamp":"2025-07-25T06:21:06.165Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppId=e046e7c7e7fbf1f1a4183e00c76e0182"}
{"@timestamp":"2025-07-25T06:21:06.165Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.standalone.config.XcDfsConfig","rest":"###dfsAppSecret=6d0dc6d02af7bab1f1ed39a7baadbb55"}
{"@timestamp":"2025-07-25T06:21:06.611Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.core.interceptor.SqlLogInterceptor","rest":"[打印SQL拦截器创建]noticeTime=5.0秒"}
{"@timestamp":"2025-07-25T06:21:07.274Z","severity":"DEBUG","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"c.x.c.c.AsyncTaskExecutorConfiguration","rest":"Creating Async Task Executor"}
{"@timestamp":"2025-07-25T06:21:08.408Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"com.xcwlkj.pubc.config.SmsConfig","rest":"SMS Bean IAcsClient Start"}
{"@timestamp":"2025-07-25T06:21:08.410Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.c.a.AnnotationConfigApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'smsServiceImpl': Unsatisfied dependency expressed through field 'iAcsClient'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'acsClient' defined in class path resource [com/xcwlkj/pubc/config/SmsConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.aliyuncs.IAcsClient]: Factory method 'acsClient' threw exception; nested exception is java.lang.NullPointerException"}
{"@timestamp":"2025-07-25T06:21:08.418Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T06:21:08.419Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13288","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3e96bacf] to prepare test instance [com.xcwlkj.test.example.ExampleServiceDatabaseTest@4cc8eb05]"}
{"@timestamp":"2025-07-25T07:52:50.906Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22868","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T07:52:50.920Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22868","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T07:52:55.298Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22868","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.cloud.autoconfigure.RefreshAutoConfiguration$JpaInvokerConfiguration': Invocation of init method failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker': Invocation of init method failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [com/alibaba/druid/spring/boot/autoconfigure/DruidDataSourceAutoConfigure.class]: Invocation of init method failed; nested exception is java.sql.SQLException: com.mysql.cj.jdbc.Driver"}
{"@timestamp":"2025-07-25T07:52:55.317Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22868","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T07:52:55.321Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22868","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@21129f1f] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@6a4ba6f4]"}
{"@timestamp":"2025-07-25T08:08:23.812Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24364","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:08:23.826Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24364","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:08:28.140Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24364","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:08:28.544Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24364","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:08:28.549Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24364","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@21129f1f] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@27002c5a]"}
{"@timestamp":"2025-07-25T08:10:16.612Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11104","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:10:16.623Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11104","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:10:20.930Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11104","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:10:21.364Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11104","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.identityverify.mapper.SbSbxxMapper' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:10:21.369Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"11104","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@41005828] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@3701acee]"}
{"@timestamp":"2025-07-25T08:11:02.314Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7112","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:11:02.327Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7112","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:11:08.639Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7112","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKcxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'attachmentHandler': Unsatisfied dependency expressed through field 'attachmentService'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'idvAttachmentService': Unsatisfied dependency expressed through field 'ossFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'idvOssFactory': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'idvDfsOssService': Unsatisfied dependency expressed through field 'xcDfsConfig'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'xcDfsConfig': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'xc.xcDfs.channel' in value \"${xc.xcDfs.channel}\""}
{"@timestamp":"2025-07-25T08:11:08.658Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7112","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T08:11:08.663Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"7112","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@41005828] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@6d50b8c2]"}
{"@timestamp":"2025-07-25T08:12:31.356Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:12:31.368Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:12:38.084Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:12:38.214Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:12:38.453Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKcxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjPkgStatusService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'xc.temp.webUrl' in value \"${xc.temp.webUrl}\""}
{"@timestamp":"2025-07-25T08:12:38.472Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T08:12:38.477Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"25876","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@41005828] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@4a83740e]"}
{"@timestamp":"2025-07-25T08:14:21.128Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:14:21.141Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:14:27.542Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:14:27.649Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:14:27.981Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKcxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjPkgStatusService': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'xc.identityVerify.pkg.secret.enable' in value \"${xc.identityVerify.pkg.secret.enable}\""}
{"@timestamp":"2025-07-25T08:14:28.000Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T08:14:28.004Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"34060","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@44c73c26] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@435b944c]"}
{"@timestamp":"2025-07-25T08:15:17.449Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:15:17.460Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:15:24.332Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:15:24.496Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:15:25.631Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:15:26.247Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\nThe following candidates were found but could not be injected:\r\n\t- Bean method 'redisUtilAdapter' in 'AppConfig' not loaded because @ConditionalOnProperty (spring.application.name=identityverify-service) found different value in property 'spring.application.name'\r\n\r\n\r\nAction:\r\n\r\nConsider revisiting the entries above or defining a bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:15:26.253Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4476","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@41005828] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@379ddab2]"}
{"@timestamp":"2025-07-25T08:29:32.242Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:29:32.256Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"com.xcwlkj.junit.DatabaseFrameworkTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:29:39.968Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:29:40.081Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:29:40.976Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:29:41.365Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\nThe following candidates were found but could not be injected:\r\n\t- Bean method 'redisUtilAdapter' in 'AppConfig' not loaded because @ConditionalOnProperty (spring.application.name=identityverify-service) found different value in property 'spring.application.name'\r\n\r\n\r\nAction:\r\n\r\nConsider revisiting the entries above or defining a bean of type 'com.xcwlkj.identityverify.util.RedisUtilAdapter' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:29:41.373Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"24912","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@2141a12] to prepare test instance [com.xcwlkj.junit.DatabaseFrameworkTest@60877629]"}
{"@timestamp":"2025-07-25T08:30:27.180Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$966e2f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:30:29.785Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:30:29.792Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:30:37.222Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:30:37.319Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:30:38.293Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksBmxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hsFaceFeatureUtils': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'HsFaceFeature.HsFaceFeatureCreatorPath' in value \"${HsFaceFeature.HsFaceFeatureCreatorPath}\""}
{"@timestamp":"2025-07-25T08:30:38.314Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T08:30:38.319Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33788","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@f6b85c3]"}
{"@timestamp":"2025-07-25T08:31:16.405Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$18a9a10c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:31:19.019Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:31:19.026Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:31:26.349Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:31:26.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:31:27.771Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.service.XcMsgService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:31:28.173Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.service.XcMsgService' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.service.XcMsgService' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:31:28.179Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"12860","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@225129c] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@763a46fe]"}
{"@timestamp":"2025-07-25T08:32:50.451Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$19716b6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:32:53.080Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:32:53.087Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:33:00.839Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:33:00.942Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:33:02.291Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.service.XcMsgService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:33:02.686Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.service.XcMsgService' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.service.XcMsgService' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:33:02.693Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"16772","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@1b2092fe]"}
{"@timestamp":"2025-07-25T08:33:53.357Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$19716b6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:33:55.971Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:33:55.980Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:34:03.792Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:34:03.890Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:34:05.216Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.service.XcMsgService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:34:05.598Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.service.XcMsgService' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.service.XcMsgService' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:34:05.604Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"22656","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@38913b95]"}
{"@timestamp":"2025-07-25T08:42:25.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$19716b6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:42:28.338Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:42:28.345Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:42:35.890Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:42:35.986Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:42:37.480Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mockXcMsgService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.base.XcMqRedisService' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:42:38.034Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.base.XcMqRedisService' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.base.XcMqRedisService' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:42:38.046Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"33932","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@35caab4c]"}
{"@timestamp":"2025-07-25T08:44:27.490Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$859ebebe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:44:30.109Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:44:30.117Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:44:37.507Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:44:37.603Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:44:38.925Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mockXcMsgService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:44:39.366Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:44:39.373Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"35472","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6d07a63d] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@698df5e8]"}
{"@timestamp":"2025-07-25T08:47:35.763Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$3b0802dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:47:38.492Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:47:38.499Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:47:45.941Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:47:46.035Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:47:47.338Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mockXcMsgService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:47:47.784Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:47:47.790Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"26932","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@3d6f549c]"}
{"@timestamp":"2025-07-25T08:50:39.740Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$842f0c7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:50:42.337Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:50:42.344Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:50:49.757Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:50:49.852Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:50:51.149Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'mockXcMsgService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:50:51.738Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nField jyCommonPlatformService in com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient required a bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' that could not be found.\r\n\r\nThe injection point has the following annotations:\r\n\t- @org.springframework.beans.factory.annotation.Autowired(required=true)\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.msgque.que.XcMqProducerFactory' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:50:51.745Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31840","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@6d07a63d] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@1727d525]"}
{"@timestamp":"2025-07-25T08:52:07.417Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$19716b6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:52:10.017Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:52:10.024Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:52:17.545Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:52:17.638Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:52:18.966Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csXxjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'csJsjbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'provincePlatformClient': Unsatisfied dependency expressed through field 'jyCommonPlatformService'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'jyCommonPlatformService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'hbTHServiceImpl': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeStatusService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksProcessEventService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKsjhService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'ksKssjDistributeTaskService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'examDataServiceImpl': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'xc.identityVerify.distributePkg.max_retry_count' in value \"${xc.identityVerify.distributePkg.max_retry_count}\""}
{"@timestamp":"2025-07-25T08:52:18.987Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-25T08:52:18.992Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"17928","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@3564caf8]"}
{"@timestamp":"2025-07-25T08:53:25.893Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$842f0c7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-25T08:53:28.508Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-25T08:53:28.515Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-25T08:53:35.951Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-25T08:53:36.050Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-25T08:53:37.353Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-25T08:53:37.545Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-25T08:53:37.888Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-25T08:53:37.930Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-25T08:53:37.931Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-25T08:53:38.039Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"o.s.w.c.s.GenericWebApplicationContext","rest":"Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbmyCache': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sbSbxxService': Injection of resource dependencies failed; nested exception is org.springframework.beans.factory.NoSuchBeanDefinitionException: No qualifying bean of type 'com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi' available: expected at least 1 bean which qualifies as autowire candidate. Dependency annotations: {@javax.annotation.Resource(shareable=true, lookup=, name=, description=, authenticationType=CONTAINER, type=class java.lang.Object, mappedName=)}"}
{"@timestamp":"2025-07-25T08:53:38.469Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"o.s.b.d.LoggingFailureAnalysisReporter","rest":"\r\n\r\n***************************\r\nAPPLICATION FAILED TO START\r\n***************************\r\n\r\nDescription:\r\n\r\nA component required a bean of type 'com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi' that could not be found.\r\n\r\n\r\nAction:\r\n\r\nConsider defining a bean of type 'com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi' in your configuration.\r\n"}
{"@timestamp":"2025-07-25T08:53:38.475Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"4632","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@20435c40] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@5c8b15f5]"}
