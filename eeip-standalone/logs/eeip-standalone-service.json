{"@timestamp":"2025-07-28T00:49:07.037Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$57fa7fab] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T00:49:09.790Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T00:49:09.816Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T00:49:20.462Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T00:49:20.571Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T00:49:21.905Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T00:49:22.136Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T00:49:22.488Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T00:49:22.533Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T00:49:22.534Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T00:49:34.562Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T00:49:37.924Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 32.627 seconds (JVM running for 34.077)"}
{"@timestamp":"2025-07-28T00:49:38.006Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T00:49:38.006Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T00:49:38.007Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-database-test"}
{"@timestamp":"2025-07-28T00:49:39.056Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-28T00:49:39.059Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"30632","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@571c5681] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@65ed2da6]"}
{"@timestamp":"2025-07-28T00:58:42.354Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$61e96d89] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T00:58:46.098Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T00:58:46.105Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T00:58:54.746Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T00:59:55.114Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"初始化兴畅DFS异常[发送 POST请求出现异常！]"}
{"@timestamp":"2025-07-28T00:59:55.117Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T00:59:56.548Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T00:59:56.765Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:00:35.177Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:00:35.322Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:00:35.322Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:00:46.960Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:00:50.057Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 130.299 seconds (JVM running for 131.545)"}
{"@timestamp":"2025-07-28T01:00:50.128Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T01:00:50.128Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:00:50.130Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-database-test"}
{"@timestamp":"2025-07-28T01:00:51.174Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-28T01:00:51.176Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"13948","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@571c5681] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@12ebc4]"}
{"@timestamp":"2025-07-28T01:06:42.784Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","rest":"Bean 'org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration' of type [org.springframework.cloud.autoconfigure.ConfigurationPropertiesRebinderAutoConfiguration$$EnhancerBySpringCGLIB$$10501d42] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)"}
{"@timestamp":"2025-07-28T01:06:45.413Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.b.t.j.DuplicateJsonObjectContextCustomizerFactory$DuplicateJsonObjectContextCustomizer","rest":"\r\n\r\nFound multiple occurrences of org.json.JSONObject on the class path:\r\n\r\n\tjar:file:/D:/maven/repository/org/json/json/20170516/json-20170516.jar!/org/json/JSONObject.class\r\n\tjar:file:/D:/maven/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar!/org/json/JSONObject.class\r\n\r\nYou may wish to exclude one of them to ensure predictable runtime behavior\r\n"}
{"@timestamp":"2025-07-28T01:06:45.420Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"The following profiles are active: database-test"}
{"@timestamp":"2025-07-28T01:06:53.927Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化####"}
{"@timestamp":"2025-07-28T01:06:54.025Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.identityverify.config.XcDfsConfig","rest":"####文件系统初始化成功####"}
{"@timestamp":"2025-07-28T01:06:55.297Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.taskcenter.DefaultHandleFactory","rest":"配置线程池工作线程数量[16]"}
{"@timestamp":"2025-07-28T01:06:55.489Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"开始检查平台类型和初始化上级平台DFS客户端..."}
{"@timestamp":"2025-07-28T01:06:55.779Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"当前平台类型为：HISOME，开始初始化上级平台DFS客户端"}
{"@timestamp":"2025-07-28T01:06:55.823Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端初始化成功"}
{"@timestamp":"2025-07-28T01:06:55.823Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.x.i.handler.UpperHsDfsHandler","rest":"上级平台DFS客户端自动初始化完成"}
{"@timestamp":"2025-07-28T01:07:06.837Z","severity":"WARN","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.b.a.f.FreeMarkerAutoConfiguration","rest":"Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.checkTemplateLocation=false)"}
{"@timestamp":"2025-07-28T01:07:09.843Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"com.xcwlkj.junit.MinimalDatabaseTest","rest":"Started MinimalDatabaseTest in 28.627 seconds (JVM running for 29.813)"}
{"@timestamp":"2025-07-28T01:07:09.912Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，init. workerId=0,datacenterId=0"}
{"@timestamp":"2025-07-28T01:07:09.912Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###SnowflakeIdGenerator，finish<<<<<<<<<<<<<"}
{"@timestamp":"2025-07-28T01:07:09.913Z","severity":"INFO","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"c.xcwlkj.core.config.ZookeeperInitRunner","rest":"###ZookeeperInitRunner，init. HostAddress=**************:-1, applicationName=eeip-database-test"}
{"@timestamp":"2025-07-28T01:07:10.963Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.springframework.boot.SpringApplication","rest":"Application run failed"}
{"@timestamp":"2025-07-28T01:07:10.966Z","severity":"ERROR","service":"eeip-standalone-service","trace":"","span":"","parent":"","exportable":"","pid":"31540","thread":"main","class":"o.s.test.context.TestContextManager","rest":"Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@488d1cd7] to prepare test instance [com.xcwlkj.junit.MinimalDatabaseTest@69176296]"}
