# 数据库测试框架使用说明

## 概述

这是一个专门用于数据库测试的轻量级框架，只加载数据库相关的组件，排除MQTT、定时任务、消息队列等不必要的功能，以提高测试启动速度和稳定性。

## 框架特性

- ✅ **轻量级启动**：只加载数据库相关组件，启动速度快
- ✅ **自动配置**：使用专门的测试配置，无需手动配置复杂的环境
- ✅ **事务支持**：支持事务回滚，测试不会污染数据库
- ✅ **工具方法**：提供常用的测试工具方法和断言
- ✅ **日志优化**：简化日志输出，突出测试关键信息

## 核心组件

### 1. DatabaseTestApplication
测试专用的Spring Boot启动类，只加载必要的组件。

### 2. BaseDatabaseTest
基础测试类，提供：
- JSON格式化输出
- 常用断言方法
- 测试步骤记录
- 自动配置测试环境

### 3. TransactionalDatabaseTest
支持事务回滚的测试基类，适用于需要修改数据但不想影响数据库状态的测试。

## 使用方法

### 基础测试

```java
public class MyDatabaseTest extends BaseDatabaseTest {
    
    @Autowired
    private MyService myService;
    
    @Test
    public void testQuery() {
        logStep("开始测试查询功能");
        
        // 执行测试
        MyDTO dto = new MyDTO();
        dto.setParam("test");
        MyVO result = myService.query(dto);
        
        // 验证结果
        assertNotNull(result, "查询结果");
        
        // 打印结果
        printJson("查询结果", result);
        
        logStep("测试完成");
    }
}
```

### 事务回滚测试

```java
public class MyTransactionalTest extends TransactionalDatabaseTest {
    
    @Autowired
    private MyService myService;
    
    @Test
    public void testInsert() {
        logStep("开始测试插入功能（会自动回滚）");
        
        // 执行插入操作
        MyEntity entity = new MyEntity();
        entity.setName("测试数据");
        myService.insert(entity);
        
        // 验证插入成功
        MyEntity saved = myService.findById(entity.getId());
        assertNotNull(saved, "插入的数据");
        
        logStep("插入测试完成，数据将自动回滚");
    }
}
```

## 配置说明

### 测试配置文件
`application-database-test.yml` 包含：
- 数据库连接配置
- Redis配置（可选）
- 禁用的组件配置
- 日志配置

### 自定义配置
如需添加自定义配置，可以在测试类上使用：

```java
@TestPropertySource(properties = {
    "my.custom.property=value"
})
public class MyTest extends BaseDatabaseTest {
    // 测试代码
}
```

## 快速开始

### 1. 验证框架是否正常工作

**Windows用户：**
```cmd
cd eeip-standalone
run-database-tests.bat
```

**Linux/Mac用户：**
```bash
cd eeip-standalone
./run-database-tests.sh
```

**或者手动运行验证：**
```bash
mvn test -Dtest=DatabaseFrameworkTest -Dspring.profiles.active=database-test
```

### 2. 运行示例测试
```bash
mvn test -Dtest=DatabaseTestExample -Dspring.profiles.active=database-test
```

## 运行测试

### IDE中运行
1. 在IDE中打开测试类
2. 右键选择"Run Test"或"Debug Test"
3. 框架会自动使用`database-test`配置

### Maven命令行运行

**运行单个测试类：**
```bash
mvn test -Dtest=MyDatabaseTest -Dspring.profiles.active=database-test
```

**运行单个测试方法：**
```bash
mvn test -Dtest=MyDatabaseTest#testMethod -Dspring.profiles.active=database-test
```

**运行所有数据库测试：**
```bash
mvn test -Dspring.profiles.active=database-test
```

**运行测试并生成详细报告：**
```bash
mvn test -Dspring.profiles.active=database-test -Dmaven.test.failure.ignore=true
```

## 注意事项

1. **数据库连接**：确保测试数据库可以正常连接
2. **表结构**：确保测试涉及的表在数据库中存在
3. **测试数据**：建议使用专门的测试数据库，避免影响开发环境
4. **事务回滚**：使用`TransactionalDatabaseTest`时，所有数据修改都会回滚
5. **性能考虑**：框架已优化启动速度，但首次启动仍需要一些时间

## 故障排除

### 常见问题

1. **启动失败**
   - 检查数据库连接配置
   - 确认数据库服务正在运行
   - 检查依赖的表是否存在

2. **Bean注入失败**
   - 确认Service类在扫描包路径内
   - 检查是否有循环依赖
   - 查看是否缺少必要的配置

3. **测试运行缓慢**
   - 检查是否意外加载了不必要的组件
   - 确认使用了正确的测试配置文件

### 调试技巧

1. 启用详细日志：
```yaml
logging:
  level:
    com.xcwlkj: DEBUG
```

2. 查看加载的Bean：
```java
@Test
public void printBeans() {
    String[] beanNames = applicationContext.getBeanDefinitionNames();
    Arrays.sort(beanNames);
    for (String beanName : beanNames) {
        System.out.println(beanName);
    }
}
```

## 扩展

如需扩展框架功能，可以：

1. 在`DatabaseTestConfig`中添加新的Bean配置
2. 在`BaseDatabaseTest`中添加新的工具方法
3. 创建新的测试基类继承现有基类

## 测试类示例

### 1. 框架验证测试
- `DatabaseFrameworkTest` - 验证框架是否正常工作
- `DatabaseTestExample` - 基础数据库操作示例

### 2. 业务测试示例
- `RunOnlyWithDatabaseTest` - 业务Service测试示例
- `TransactionalDatabaseTestExample` - 事务回滚测试示例

### 3. 创建自己的测试类

**基础查询测试：**
```java
public class MyServiceTest extends BaseDatabaseTest {

    @Autowired
    private MyService myService;

    @Test
    public void testQueryData() {
        logStep("测试数据查询");

        MyDTO dto = new MyDTO();
        dto.setParam("test");

        MyVO result = myService.queryData(dto);

        assertNotNull(result, "查询结果");
        printJson("查询结果", result);

        logStep("测试完成");
    }
}
```

**事务回滚测试：**
```java
public class MyTransactionalTest extends TransactionalDatabaseTest {

    @Autowired
    private MyService myService;

    @Test
    public void testInsertData() {
        logStep("测试数据插入（会自动回滚）");

        MyEntity entity = new MyEntity();
        entity.setName("测试数据");

        myService.insertData(entity);

        // 验证插入成功
        MyEntity saved = myService.findById(entity.getId());
        assertNotNull(saved, "插入的数据");

        logStep("插入测试完成，数据将自动回滚");
    }
}
```
