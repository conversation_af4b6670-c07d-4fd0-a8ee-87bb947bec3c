package com.xcwlkj.junit;

import com.xcwlkj.config.MinimalDatabaseTestApplication;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.identityverify.util.RedisUtilAdapter;
import com.xcwlkj.msgque.service.XcMsgService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import javax.sql.DataSource;
import java.sql.Connection;

/**
 * 最小化数据库测试
 * 使用最简单的配置，只测试基本的数据库连接和Bean注入
 */
@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class MinimalDatabaseTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private DataSource dataSource;

    @Test
    public void testBasicConfiguration() {
        System.out.println("=== 测试基本配置 ===");
        
        // 验证Spring上下文
        System.out.println("✓ Spring应用上下文加载成功");
        
        // 验证数据源
        try {
            Connection connection = dataSource.getConnection();
            System.out.println("✓ 数据库连接成功: " + connection.getMetaData().getURL());
            connection.close();
        } catch (Exception e) {
            System.err.println("✗ 数据库连接失败: " + e.getMessage());
        }
        
        System.out.println("基本配置测试完成");
    }

    @Test
    public void testRedisUtilBean() {
        System.out.println("=== 测试RedisUtil Bean ===");
        
        try {
            RedisUtil redisUtil = applicationContext.getBean(RedisUtil.class);
            System.out.println("✓ RedisUtil Bean创建成功: " + redisUtil.getClass().getName());
            
            // 测试Mock功能
            redisUtil.set("test-key", "test-value");
            Object value = redisUtil.get("test-key");
            System.out.println("✓ Mock Redis操作正常");
            
        } catch (Exception e) {
            System.err.println("✗ RedisUtil Bean获取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testRedisUtilAdapterBean() {
        System.out.println("=== 测试RedisUtilAdapter Bean ===");
        
        try {
            RedisUtilAdapter adapter = applicationContext.getBean(RedisUtilAdapter.class);
            System.out.println("✓ RedisUtilAdapter Bean创建成功: " + adapter.getClass().getName());
            
            // 测试适配器功能
            adapter.zAdd("test-zset", "member1", 1.0);
            System.out.println("✓ RedisUtilAdapter操作正常");
            
        } catch (Exception e) {
            System.err.println("✗ RedisUtilAdapter Bean获取失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testBeanList() {
        System.out.println("=== 加载的Bean列表 ===");
        
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        System.out.println("总共加载了 " + beanNames.length + " 个Bean");
        
        // 只显示关键的Bean
        System.out.println("\n关键Bean:");
        for (String beanName : beanNames) {
            if (beanName.contains("redis") || beanName.contains("dataSource") || 
                beanName.contains("transactionManager") || beanName.contains("jdbcTemplate")) {
                try {
                    Object bean = applicationContext.getBean(beanName);
                    System.out.println("- " + beanName + ": " + bean.getClass().getSimpleName());
                } catch (Exception e) {
                    System.out.println("- " + beanName + ": [获取失败] " + e.getMessage());
                }
            }
        }
    }
}
