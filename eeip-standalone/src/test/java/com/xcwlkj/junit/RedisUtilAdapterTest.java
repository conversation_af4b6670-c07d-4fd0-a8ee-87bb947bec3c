package com.xcwlkj.junit;

import com.xcwlkj.config.DatabaseTestApplication;
import com.xcwlkj.identityverify.util.RedisUtilAdapter;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

/**
 * 专门测试RedisUtilAdapter Bean注入问题
 */
@SpringBootTest(classes = DatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class RedisUtilAdapterTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testRedisUtilAdapterBeanExists() {
        System.out.println("=== 测试RedisUtilAdapter Bean是否存在 ===");
        
        // 检查Bean是否存在
        boolean hasRedisUtilAdapter = applicationContext.containsBean("redisUtilAdapter");
        System.out.println("RedisUtilAdapter Bean存在: " + hasRedisUtilAdapter);
        
        // 尝试获取Bean
        try {
            RedisUtilAdapter adapter = applicationContext.getBean(RedisUtilAdapter.class);
            System.out.println("✓ 成功获取RedisUtilAdapter Bean: " + adapter.getClass().getName());
        } catch (Exception e) {
            System.err.println("✗ 获取RedisUtilAdapter Bean失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 列出所有相关的Bean
        System.out.println("\n相关Bean列表:");
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("redis")) {
                System.out.println("- " + beanName + ": " + applicationContext.getBean(beanName).getClass().getName());
            }
        }
    }

    @Test
    public void testDirectInjection() {
        System.out.println("=== 测试直接注入RedisUtilAdapter ===");
        
        try {
            RedisUtilAdapter adapter = applicationContext.getBean("redisUtilAdapter", RedisUtilAdapter.class);
            System.out.println("✓ 直接注入成功: " + adapter);
        } catch (Exception e) {
            System.err.println("✗ 直接注入失败: " + e.getMessage());
            
            // 提供解决建议
            System.err.println("\n可能的解决方案:");
            System.err.println("1. 检查RedisUtil Bean是否正确创建");
            System.err.println("2. 检查组件扫描路径是否包含相关类");
            System.err.println("3. 检查是否有Bean创建的条件限制");
        }
    }
}
