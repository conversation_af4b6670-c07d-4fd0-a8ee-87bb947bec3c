package com.xcwlkj.junit;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.xcwlkj.config.DatabaseTestApplication;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数据库测试基础类
 * 
 * 功能特性：
 * 1. 只加载数据库相关组件，不加载MQTT、定时任务等
 * 2. 使用database-test配置文件
 * 3. 支持事务回滚（可选）
 * 4. 提供常用的测试工具方法
 * 
 * 使用方式：
 * 继承此类，然后编写具体的测试方法
 * 
 * <AUTHOR>
 */
@SpringBootTest(classes = DatabaseTestApplication.class)
@ActiveProfiles("database-test")
@TestPropertySource(properties = {
    // 禁用不需要的功能
    "xc.mqtt.enable=false",
    "xxl.job.admin.addresses=",
    "spring.cloud.discovery.enabled=false",
    "spring.cloud.config.enabled=false",
    "eureka.client.enabled=false",
    "feign.client.enabled=false",
    // 加快测试启动速度
    "spring.jpa.show-sql=false",
    "logging.level.org.springframework=WARN",
    "logging.level.com.zaxxer.hikari=WARN"
})
public abstract class BaseDatabaseTest {

    /**
     * JSON工具，用于打印测试结果
     */
    protected final Gson gson = new GsonBuilder()
            .setPrettyPrinting()
            .setDateFormat("yyyy-MM-dd HH:mm:ss")
            .create();

    @BeforeEach
    public void setUp() {
        System.out.println("=== 开始执行数据库测试 ===");
    }

    /**
     * 打印对象为JSON格式
     */
    protected void printJson(Object obj) {
        System.out.println("测试结果：");
        System.out.println(gson.toJson(obj));
    }

    /**
     * 打印对象为JSON格式，带标题
     */
    protected void printJson(String title, Object obj) {
        System.out.println(title + "：");
        System.out.println(gson.toJson(obj));
    }

    /**
     * 断言对象不为空
     */
    protected void assertNotNull(Object obj, String message) {
        if (obj == null) {
            throw new AssertionError(message + " 不能为空");
        }
    }

    /**
     * 断言集合不为空
     */
    protected void assertNotEmpty(java.util.Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new AssertionError(message + " 不能为空");
        }
    }

    /**
     * 记录测试步骤
     */
    protected void logStep(String step) {
        System.out.println(">>> " + step);
    }
}

/**
 * 支持事务回滚的数据库测试基础类
 * 
 * 继承此类的测试方法会在事务中执行，测试完成后自动回滚
 * 适用于需要修改数据但不想影响数据库状态的测试
 */
@Transactional
abstract class TransactionalDatabaseTest extends BaseDatabaseTest {
    
    @BeforeEach
    @Override
    public void setUp() {
        super.setUp();
        System.out.println(">>> 事务已开启，测试完成后将自动回滚");
    }
}
