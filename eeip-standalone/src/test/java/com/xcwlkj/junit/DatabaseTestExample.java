package com.xcwlkj.junit;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.List;
import java.util.Map;

/**
 * 数据库测试示例
 * 展示如何使用数据库测试框架进行各种类型的数据库测试
 */
public class DatabaseTestExample extends BaseDatabaseTest {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 基础查询测试示例
     */
    @Test
    public void testBasicQuery() {
        logStep("开始基础查询测试");

        try {
            // 查询数据库版本信息
            String version = jdbcTemplate.queryForObject("SELECT VERSION()", String.class);
            logStep("数据库版本: " + version);

            // 查询当前时间
            String currentTime = jdbcTemplate.queryForObject("SELECT NOW()", String.class);
            logStep("当前时间: " + currentTime);

            // 验证结果
            assertNotNull(version, "数据库版本");
            assertNotNull(currentTime, "当前时间");

            logStep("✓ 基础查询测试成功");

        } catch (Exception e) {
            logStep("基础查询测试失败: " + e.getMessage());
            throw new RuntimeException("基础查询测试失败", e);
        }
    }

    /**
     * 表结构查询测试示例
     */
    @Test
    public void testTableStructureQuery() {
        logStep("开始表结构查询测试");

        try {
            // 查询数据库中的表
            String sql = "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES " +
                        "WHERE TABLE_SCHEMA = DATABASE() " +
                        "ORDER BY TABLE_NAME LIMIT 10";
            
            List<Map<String, Object>> tables = jdbcTemplate.queryForList(sql);
            
            logStep("数据库中的表（前10个）:");
            for (Map<String, Object> table : tables) {
                logStep("- " + table.get("TABLE_NAME"));
            }

            assertNotEmpty(tables, "数据库表列表");
            logStep("✓ 表结构查询测试成功");

        } catch (Exception e) {
            logStep("表结构查询测试失败: " + e.getMessage());
            // 这里不抛出异常，因为可能是权限问题
            logStep("注意：如果是权限问题，这是正常的");
        }
    }

    /**
     * 条件查询测试示例
     */
    @Test
    public void testConditionalQuery() {
        logStep("开始条件查询测试");

        try {
            // 使用参数化查询
            String sql = "SELECT ? as test_value, ? as test_name";
            Map<String, Object> result = jdbcTemplate.queryForMap(sql, 123, "测试数据");

            logStep("查询结果:");
            printJson(result);

            // 验证结果
            assertNotNull(result, "查询结果");
            logStep("✓ 条件查询测试成功");

        } catch (Exception e) {
            logStep("条件查询测试失败: " + e.getMessage());
            throw new RuntimeException("条件查询测试失败", e);
        }
    }

    /**
     * 数据统计测试示例
     */
    @Test
    public void testDataStatistics() {
        logStep("开始数据统计测试");

        try {
            // 统计数据库中的表数量
            String countSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES " +
                             "WHERE TABLE_SCHEMA = DATABASE()";
            
            Integer tableCount = jdbcTemplate.queryForObject(countSql, Integer.class);
            logStep("数据库中共有 " + tableCount + " 个表");

            // 验证结果
            assertNotNull(tableCount, "表数量");
            logStep("✓ 数据统计测试成功");

        } catch (Exception e) {
            logStep("数据统计测试失败: " + e.getMessage());
            // 权限问题时不抛出异常
            logStep("注意：如果是权限问题，这是正常的");
        }
    }

    /**
     * 复杂查询测试示例
     */
    @Test
    public void testComplexQuery() {
        logStep("开始复杂查询测试");

        try {
            // 构建复杂的测试数据
            TestQueryData queryData = new TestQueryData();
            queryData.setId(1L);
            queryData.setName("复杂查询测试");
            queryData.setStatus("ACTIVE");

            // 模拟复杂查询逻辑
            String complexSql = "SELECT " +
                               "? as id, " +
                               "? as name, " +
                               "? as status, " +
                               "NOW() as query_time, " +
                               "DATABASE() as database_name";

            Map<String, Object> result = jdbcTemplate.queryForMap(
                complexSql, 
                queryData.getId(), 
                queryData.getName(), 
                queryData.getStatus()
            );

            logStep("复杂查询结果:");
            printJson(result);

            // 验证结果
            assertNotNull(result, "复杂查询结果");
            logStep("✓ 复杂查询测试成功");

        } catch (Exception e) {
            logStep("复杂查询测试失败: " + e.getMessage());
            throw new RuntimeException("复杂查询测试失败", e);
        }
    }

    /**
     * 测试查询数据类
     */
    public static class TestQueryData {
        private Long id;
        private String name;
        private String status;

        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
    }
}
