package com.xcwlkj.junit;

import org.junit.jupiter.api.Test;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 简单的数据库连接测试
 * 不依赖Spring框架，直接测试JDBC连接
 */
public class SimpleDatabaseTest {

    private static final String URL = "******************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "cd_hisome";
    private static final String DRIVER_CLASS = "com.mysql.jdbc.Driver";

    @Test
    public void testMySQLDriverExists() {
        System.out.println("=== 测试MySQL驱动是否存在 ===");
        
        try {
            // 尝试加载MySQL驱动
            Class.forName(DRIVER_CLASS);
            System.out.println("✓ MySQL驱动加载成功: " + DRIVER_CLASS);
        } catch (ClassNotFoundException e) {
            System.err.println("✗ MySQL驱动未找到: " + e.getMessage());
            System.err.println("请确保在pom.xml中添加了mysql-connector-java依赖");
            throw new RuntimeException("MySQL驱动未找到", e);
        }
    }

    @Test
    public void testDatabaseConnection() {
        System.out.println("=== 测试数据库连接 ===");
        
        try {
            // 加载驱动
            Class.forName(DRIVER_CLASS);
            System.out.println("✓ 驱动加载成功");
            
            // 建立连接
            Connection connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            System.out.println("✓ 数据库连接成功");
            
            // 测试查询
            Statement statement = connection.createStatement();
            ResultSet resultSet = statement.executeQuery("SELECT VERSION() as version, NOW() as current_time");
            
            if (resultSet.next()) {
                String version = resultSet.getString("version");
                String currentTime = resultSet.getString("current_time");
                System.out.println("数据库版本: " + version);
                System.out.println("当前时间: " + currentTime);
            }
            
            // 关闭连接
            resultSet.close();
            statement.close();
            connection.close();
            System.out.println("✓ 数据库连接测试完成");
            
        } catch (Exception e) {
            System.err.println("✗ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
            
            // 提供一些常见问题的解决建议
            System.err.println("\n可能的解决方案:");
            System.err.println("1. 检查数据库服务是否启动");
            System.err.println("2. 检查网络连接是否正常");
            System.err.println("3. 检查数据库连接参数是否正确");
            System.err.println("4. 检查MySQL驱动是否正确添加到classpath");
            
            throw new RuntimeException("数据库连接测试失败", e);
        }
    }

    @Test
    public void testDriverClassOnly() {
        System.out.println("=== 仅测试驱动类加载 ===");
        
        try {
            Class<?> driverClass = Class.forName(DRIVER_CLASS);
            System.out.println("✓ 驱动类加载成功: " + driverClass.getName());
            System.out.println("驱动类位置: " + driverClass.getProtectionDomain().getCodeSource().getLocation());
        } catch (ClassNotFoundException e) {
            System.err.println("✗ 驱动类加载失败: " + e.getMessage());
            
            // 尝试加载新版本的驱动
            try {
                Class<?> newDriverClass = Class.forName("com.mysql.cj.jdbc.Driver");
                System.out.println("✓ 找到新版本MySQL驱动: " + newDriverClass.getName());
                System.out.println("建议更新配置文件中的驱动类名为: com.mysql.cj.jdbc.Driver");
            } catch (ClassNotFoundException e2) {
                System.err.println("✗ 新版本驱动也未找到");
                System.err.println("请检查pom.xml中是否正确添加了mysql-connector-java依赖");
            }
            
            throw new RuntimeException("驱动类加载失败", e);
        }
    }
}
