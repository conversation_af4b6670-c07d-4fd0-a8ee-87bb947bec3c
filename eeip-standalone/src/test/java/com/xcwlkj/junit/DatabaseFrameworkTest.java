package com.xcwlkj.junit;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.Arrays;

/**
 * 数据库测试框架验证测试
 * 验证框架是否正常工作，不依赖具体的业务Service
 */
public class DatabaseFrameworkTest extends BaseDatabaseTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private Environment environment;

    @Autowired
    private DataSource dataSource;

    /**
     * 测试框架基础功能
     */
    @Test
    public void testFrameworkBasics() {
        logStep("测试数据库测试框架基础功能");

        // 验证Spring上下文已加载
        assertNotNull(applicationContext, "Spring应用上下文");
        logStep("✓ Spring应用上下文加载成功");

        // 验证环境配置
        assertNotNull(environment, "环境配置");
        String[] activeProfiles = environment.getActiveProfiles();
        logStep("当前激活的配置文件: " + Arrays.toString(activeProfiles));

        // 验证数据源
        assertNotNull(dataSource, "数据源");
        logStep("✓ 数据源配置成功");

        logStep("数据库测试框架基础功能验证完成");
    }

    /**
     * 测试数据库连接
     */
    @Test
    public void testDatabaseConnection() {
        logStep("测试数据库连接");

        try {
            Connection connection = dataSource.getConnection();
            assertNotNull(connection, "数据库连接");
            
            String url = connection.getMetaData().getURL();
            String userName = connection.getMetaData().getUserName();
            
            logStep("数据库URL: " + url);
            logStep("数据库用户: " + userName);
            
            connection.close();
            logStep("✓ 数据库连接测试成功");
            
        } catch (Exception e) {
            logStep("数据库连接失败: " + e.getMessage());
            throw new RuntimeException("数据库连接测试失败", e);
        }
    }

    /**
     * 测试加载的Bean数量（验证是否成功排除了不必要的组件）
     */
    @Test
    public void testLoadedBeans() {
        logStep("检查加载的Bean数量");

        String[] beanNames = applicationContext.getBeanDefinitionNames();
        logStep("总共加载了 " + beanNames.length + " 个Bean");

        // 检查是否成功排除了MQTT相关的Bean
        long mqttBeans = Arrays.stream(beanNames)
                .filter(name -> name.toLowerCase().contains("mqtt"))
                .count();
        logStep("MQTT相关Bean数量: " + mqttBeans + " (应该为0)");

        // 检查是否成功排除了Job相关的Bean
        long jobBeans = Arrays.stream(beanNames)
                .filter(name -> name.toLowerCase().contains("job"))
                .count();
        logStep("Job相关Bean数量: " + jobBeans + " (应该很少)");

        // 打印一些关键Bean的存在情况
        logStep("关键Bean检查:");
        checkBean("dataSource", "数据源");
        checkBean("jdbcTemplate", "JDBC模板");
        checkBean("transactionManager", "事务管理器");

        logStep("Bean加载检查完成");
    }

    /**
     * 测试工具方法
     */
    @Test
    public void testUtilityMethods() {
        logStep("测试框架提供的工具方法");

        // 测试JSON打印
        TestData testData = new TestData("测试", 123);
        printJson("测试数据", testData);

        // 测试断言方法
        assertNotNull(testData, "测试数据对象");
        logStep("✓ 断言方法工作正常");

        logStep("工具方法测试完成");
    }

    private void checkBean(String beanName, String description) {
        if (applicationContext.containsBean(beanName)) {
            logStep("✓ " + description + " (" + beanName + ") 已加载");
        } else {
            logStep("✗ " + description + " (" + beanName + ") 未加载");
        }
    }

    /**
     * 测试数据类
     */
    public static class TestData {
        private String name;
        private Integer value;

        public TestData(String name, Integer value) {
            this.name = name;
            this.value = value;
        }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getValue() { return value; }
        public void setValue(Integer value) { this.value = value; }
    }
}
