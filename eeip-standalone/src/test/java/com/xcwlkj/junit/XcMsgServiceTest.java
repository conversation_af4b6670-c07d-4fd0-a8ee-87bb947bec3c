package com.xcwlkj.junit;

import com.xcwlkj.config.MinimalDatabaseTestApplication;
import com.xcwlkj.msgque.service.XcMsgService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.test.context.ActiveProfiles;

/**
 * 专门测试XcMsgService Bean注入问题
 */
@SpringBootTest(classes = MinimalDatabaseTestApplication.class)
@ActiveProfiles("database-test")
public class XcMsgServiceTest {

    @Autowired
    private ApplicationContext applicationContext;

    @Test
    public void testXcMsgServiceBeanExists() {
        System.out.println("=== 测试XcMsgService Bean是否存在 ===");
        
        // 检查Bean是否存在
        boolean hasXcMsgService = applicationContext.containsBean("mockXcMsgService");
        System.out.println("XcMsgService Bean存在: " + hasXcMsgService);
        
        // 尝试获取Bean
        try {
            XcMsgService msgService = applicationContext.getBean(XcMsgService.class);
            System.out.println("✓ 成功获取XcMsgService Bean: " + msgService.getClass().getName());
        } catch (Exception e) {
            System.err.println("✗ 获取XcMsgService Bean失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 列出所有相关的Bean
        System.out.println("\n相关Bean列表:");
        String[] beanNames = applicationContext.getBeanDefinitionNames();
        for (String beanName : beanNames) {
            if (beanName.toLowerCase().contains("msg") || beanName.toLowerCase().contains("message")) {
                try {
                    Object bean = applicationContext.getBean(beanName);
                    System.out.println("- " + beanName + ": " + bean.getClass().getName());
                } catch (Exception e) {
                    System.out.println("- " + beanName + ": [获取失败] " + e.getMessage());
                }
            }
        }
    }

    @Test
    public void testDirectInjection() {
        System.out.println("=== 测试直接注入XcMsgService ===");
        
        try {
            XcMsgService msgService = applicationContext.getBean("mockXcMsgService", XcMsgService.class);
            System.out.println("✓ 直接注入成功: " + msgService);
            
            // 测试Mock功能
            msgService.pushNoIsolateMsg("TEST_TOPIC", "test message", "TEST_CHANNEL");
            System.out.println("✓ Mock消息发送功能正常");
            
        } catch (Exception e) {
            System.err.println("✗ 直接注入失败: " + e.getMessage());
            
            // 提供解决建议
            System.err.println("\n可能的解决方案:");
            System.err.println("1. 检查XcMsgService接口是否在classpath中");
            System.err.println("2. 检查Mock实现是否正确");
            System.err.println("3. 检查组件扫描路径是否包含相关类");
        }
    }

    @Test
    public void testMockFunctionality() {
        System.out.println("=== 测试Mock功能 ===");
        
        try {
            XcMsgService msgService = applicationContext.getBean(XcMsgService.class);
            
            // 测试各种消息发送方法
            System.out.println("测试pushNoIsolateMsg方法:");
            msgService.pushNoIsolateMsg("TOPIC1", "消息内容1", "CHANNEL1");
            
            System.out.println("\n测试完成，Mock功能正常");
            
        } catch (Exception e) {
            System.err.println("✗ Mock功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
