package com.xcwlkj.junit;


import com.google.gson.Gson;
import com.xcwlkj.identityverify.model.dto.ksgl.CxjkrylbDTO;
import com.xcwlkj.identityverify.model.vo.ksgl.CxjkrylbVO;
import com.xcwlkj.identityverify.service.KsJkryJbxxService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;


//@ComponentScan("com.xcwlkj.identityverify")
public class RunOnlyWithDatabase {
    @Autowired
    private KsJkryJbxxService ksJkryJbxxService;
    Gson gson = new Gson();
    @Test
    public void query_invigilator_list_with_pic(){
//        {
//    "ksjhbh": "23122911394301850704626798610432",
//    "pageNum": 1,
//    "pageSize": 78
//}
        CxjkrylbDTO cxjkrylbDTO = new CxjkrylbDTO();
        cxjkrylbDTO.setKsjhbh("23122911394301850704626798610432");
        cxjkrylbDTO.setPageNum(1);
        cxjkrylbDTO.setPageSize(78);
        CxjkrylbVO cxjkrylb = ksJkryJbxxService.cxjkrylb(cxjkrylbDTO);
        System.out.println(gson.toJson(cxjkrylb));
    }
    @Test
    public void testSout(){
        System.out.println("OK");
    }
}
