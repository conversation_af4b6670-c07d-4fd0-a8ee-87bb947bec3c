package com.xcwlkj.junit;

import com.xcwlkj.identityverify.model.dto.ksgl.CxjkrylbDTO;
import com.xcwlkj.identityverify.model.vo.ksgl.CxjkrylbVO;
import com.xcwlkj.identityverify.service.KsJkryJbxxService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 数据库测试示例
 * 继承BaseDatabaseTest，自动获得数据库测试环境
 */
public class RunOnlyWithDatabaseTest extends BaseDatabaseTest {

    @Autowired
    private KsJkryJbxxService ksJkryJbxxService;

    @Test
    public void query_invigilator_list_with_pic() {
        logStep("开始测试查询监考人员列表");

        // 准备测试数据
        CxjkrylbDTO cxjkrylbDTO = new CxjkrylbDTO();
        cxjkrylbDTO.setKsjhbh("23122911394301850704626798610432");
        cxjkrylbDTO.setPageNum(1);
        cxjkrylbDTO.setPageSize(78);

        logStep("调用服务方法查询监考人员列表");
        CxjkrylbVO result = ksJkryJbxxService.cxjkrylb(cxjkrylbDTO);

        // 验证结果
        assertNotNull(result, "查询结果");

        // 打印结果
        printJson("监考人员列表查询结果", result);

        logStep("测试完成");
    }

    @Test
    public void testBasicConnection() {
        logStep("测试基础连接");
        System.out.println("数据库测试框架运行正常！");
        logStep("基础连接测试完成");
    }
}
