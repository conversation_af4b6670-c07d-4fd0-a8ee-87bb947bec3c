package com.xcwlkj.config;

import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.identityverify.util.RedisUtilAdapter;
import com.xcwlkj.mock.MockXcMsgService;
import com.xcwlkj.msgque.service.XcMsgService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.RestTemplate;

/**
 * 数据库测试专用配置类
 * 只配置数据库测试必需的Bean，避免加载不必要的组件
 */
@Configuration
@Profile("database-test")
public class DatabaseTestConfig {

    /**
     * 密码编码器 - 数据库测试可能需要
     */
    @Bean
    @ConditionalOnMissingBean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * RestTemplate - 如果测试中需要HTTP调用
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * Mock的RedisUtil - 测试环境不需要真正的Redis连接
     */
    @Bean
    @ConditionalOnMissingBean
    @Primary
    public RedisUtil mockRedisUtil() {
        return new MockRedisUtil();
    }

    /**
     * Redis工具适配器 - 测试环境必需的Bean
     * 移除条件限制，确保在测试环境中总是创建这个Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisUtilAdapter redisUtilAdapter(RedisUtil redisUtil) {
        return new RedisUtilAdapter(redisUtil);
    }

    /**
     * Mock的事件发送器 - 替代真实的事件发送功能
     */
    @Bean
    @Primary
    public MockEventSender mockEventSender() {
        return new MockEventSender();
    }

    /**
     * Mock的XcMsgService - 替代真实的消息队列服务
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public XcMsgService mockXcMsgService() {
        return new MockXcMsgService();
    }

    /**
     * Mock事件发送器实现
     * 在测试环境中不实际发送事件，只记录日志
     */
    public static class MockEventSender {
        public void sendEvent(Object event) {
            System.out.println("Mock Event Sent: " + event.toString());
        }

        public void sendEvent(String topic, Object event) {
            System.out.println("Mock Event Sent to topic [" + topic + "]: " + event.toString());
        }
    }

    /**
     * Mock的RedisUtil实现
     * 在测试环境中不实际连接Redis，只提供基本的模拟功能
     */
    public static class MockRedisUtil extends RedisUtil {

        public MockRedisUtil() {
            // 不需要真正的Redis连接
        }

        @Override
        public boolean set(String key, Object value) {
            System.out.println("Mock Redis SET: " + key + " = " + value);
            return true;
        }

        @Override
        public boolean set(String key, Object value, long time) {
            System.out.println("Mock Redis SET with TTL: " + key + " = " + value + " (TTL: " + time + "s)");
            return true;
        }

        @Override
        public Object get(String key) {
            System.out.println("Mock Redis GET: " + key);
            return null; // 测试环境返回null
        }

        @Override
        public void del(String... key) {
            System.out.println("Mock Redis DEL: " + String.join(", ", key));
        }

        // 可以根据需要添加更多方法的Mock实现
    }


}
