package com.xcwlkj.config;

import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.identityverify.util.RedisUtilAdapter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.client.RestTemplate;

/**
 * 数据库测试专用配置类
 * 只配置数据库测试必需的Bean，避免加载不必要的组件
 */
@Configuration
@Profile("database-test")
public class DatabaseTestConfig {

    /**
     * 密码编码器 - 数据库测试可能需要
     */
    @Bean
    @ConditionalOnMissingBean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * RestTemplate - 如果测试中需要HTTP调用
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    /**
     * Redis工具适配器 - 如果测试需要Redis功能
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(name = "spring.redis.host")
    public RedisUtilAdapter redisUtilAdapter(RedisUtil redisUtil) {
        return new RedisUtilAdapter(redisUtil);
    }

    /**
     * Mock的事件发送器 - 替代真实的事件发送功能
     */
    @Bean
    @Primary
    public MockEventSender mockEventSender() {
        return new MockEventSender();
    }

    /**
     * Mock事件发送器实现
     * 在测试环境中不实际发送事件，只记录日志
     */
    public static class MockEventSender {
        public void sendEvent(Object event) {
            System.out.println("Mock Event Sent: " + event.toString());
        }
        
        public void sendEvent(String topic, Object event) {
            System.out.println("Mock Event Sent to topic [" + topic + "]: " + event.toString());
        }
    }
}
