package com.xcwlkj.config;

import com.xcwlkj.biz.service.DataDistributeFeignApi;
import com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi;
import com.xcwlkj.service.UserFeignApi;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * Mock的Feign客户端配置
 * 提供各种Feign客户端的Mock实现，避免在测试环境中进行远程调用
 */
@Configuration
@Profile("database-test")
public class MockFeignConfig {

    /**
     * 创建通用的Feign客户端Mock
     * 使用动态代理，对所有方法返回成功的空结果
     */
    @SuppressWarnings("unchecked")
    private <T> T createMockFeignClient(Class<T> feignInterface, String name) {
        return (T) Proxy.newProxyInstance(
                this.getClass().getClassLoader(),
                new Class[]{feignInterface},
                new MockFeignInvocationHandler(name)
        );
    }

    /**
     * Mock的UserFeignApi
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public UserFeignApi mockUserFeignApi() {
        return createMockFeignClient(UserFeignApi.class, "UserFeignApi");
    }

    /**
     * Mock的BasicDeviceFeignApi
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public BasicDeviceFeignApi mockBasicDeviceFeignApi() {
        return createMockFeignClient(BasicDeviceFeignApi.class, "BasicDeviceFeignApi");
    }

    /**
     * Mock的DataDistributeFeignApi
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public DataDistributeFeignApi mockDataDistributeFeignApi() {
        return createMockFeignClient(DataDistributeFeignApi.class, "DataDistributeFeignApi");
    }

    /**
     * 通用的Feign客户端Mock处理器
     * 对所有方法调用返回成功的空结果
     */
    private static class MockFeignInvocationHandler implements InvocationHandler {
        private final String clientName;

        public MockFeignInvocationHandler(String clientName) {
            this.clientName = clientName;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) {
            // 如果是Object类的方法，直接调用
            if (method.getDeclaringClass() == Object.class) {
                try {
                    return method.invoke(this, args);
                } catch (Exception e) {
                    return null;
                }
            }

            // 打印调用信息
            System.out.println("Mock " + clientName + " - 方法: " + method.getName());
            if (args != null && args.length > 0) {
                System.out.println("参数: " + java.util.Arrays.toString(args));
            }

            // 获取返回类型
            Class<?> returnType = method.getReturnType();
            
            // 如果返回类型是Wrapper，返回成功的空结果
            if (returnType == Wrapper.class) {
                return WrapMapper.ok();
            }
            
            // 如果返回类型是void，返回null
            if (returnType == void.class || returnType == Void.class) {
                return null;
            }
            
            // 对于其他返回类型，尝试创建一个空对象
            try {
                return returnType.newInstance();
            } catch (Exception e) {
                // 如果无法创建对象，返回null
                return null;
            }
        }
    }
}
