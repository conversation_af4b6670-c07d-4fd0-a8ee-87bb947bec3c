package com.xcwlkj.config;

import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.identityverify.util.RedisUtilAdapter;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * 最小化的数据库测试应用配置
 * 只加载最基本的数据库组件，提供Mock的Redis支持
 */
@SpringBootApplication(exclude = {
    // 排除消息队列自动配置
    ActiveMQAutoConfiguration.class,
    KafkaAutoConfiguration.class,
    // 排除定时任务自动配置
    TaskSchedulingAutoConfiguration.class,
    // 排除Redis自动配置，使用Mock
    RedisAutoConfiguration.class
})
@EnableTransactionManagement
@MapperScan(basePackages = {"com.xcwlkj.*.mapper"})
@ComponentScan(
    basePackages = {
        "com.xcwlkj.core",           // 核心模块
        "com.xcwlkj.config"          // 测试配置
    },
    excludeFilters = {
        // 排除所有可能引起问题的组件
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Mm]qtt.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Jj]ob.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Ss]chedule.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Mm]qConfig.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Rr]ocketMq.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Ll]istener.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Ff]eign.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Cc]lient.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Cc]ontroller.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Ww]eb.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Xx]cMsg.*")
    }
)
public class MinimalDatabaseTestApplication {
    
    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "database-test");
        SpringApplication.run(MinimalDatabaseTestApplication.class, args);
    }

    /**
     * Mock的RedisUtil - 不需要真实Redis连接
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean
    public RedisUtil mockRedisUtil() {
        return new MockRedisUtil();
    }

    /**
     * RedisUtilAdapter - 使用Mock的RedisUtil
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisUtilAdapter redisUtilAdapter(RedisUtil redisUtil) {
        return new RedisUtilAdapter(redisUtil);
    }

    /**
     * Mock的RedisUtil实现
     */
    public static class MockRedisUtil extends RedisUtil {
        
        @Override
        public boolean set(String key, Object value) {
            System.out.println("Mock Redis SET: " + key + " = " + value);
            return true;
        }
        
        @Override
        public boolean set(String key, Object value, long time) {
            System.out.println("Mock Redis SET with TTL: " + key + " = " + value + " (TTL: " + time + "s)");
            return true;
        }
        
        @Override
        public Object get(String key) {
            System.out.println("Mock Redis GET: " + key);
            return null;
        }
        
        @Override
        public void del(String... key) {
            System.out.println("Mock Redis DEL: " + String.join(", ", key));
        }
        
        // 添加其他可能需要的方法
        public void zAdd(String key, Object value, double score) {
            System.out.println("Mock Redis ZADD: " + key + " " + score + " " + value);
        }
        
        public java.util.Set<Object> rangeByScore(String key, double min, double max) {
            System.out.println("Mock Redis ZRANGEBYSCORE: " + key + " " + min + " " + max);
            return new java.util.HashSet<>();
        }
    }
}
