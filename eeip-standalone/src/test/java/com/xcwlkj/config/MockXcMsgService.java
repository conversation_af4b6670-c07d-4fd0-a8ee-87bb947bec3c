package com.xcwlkj.mock;

import com.xcwlkj.msgque.model.domain.XcExecCircleModel;
import com.xcwlkj.msgque.model.domain.XcMsgModel;
import com.xcwlkj.msgque.service.XcMsgService;

import java.util.Date;
import java.util.function.Consumer;

public class MockXcMsgService implements XcMsgService {
    @Override
    public void stopCircleMsg(String s) {

    }

    @Override
    public void resetCircleMsg(String s, XcExecCircleModel xcExecCircleModel) {

    }

    @Override
    public void pushNoIsolateMsg(String s, String s1, String... strings) {

    }

    @Override
    public void pushMsg(String s, String s1, String... strings) {

    }

    @Override
    public void pushMsg(String s, String s1, Date date, String... strings) {

    }

    @Override
    public void pushMsg(String s, String s1, String s2, Date date, String... strings) {

    }

    @Override
    public void pushMsgDelayOfRocket(String s, String s1, Date date, String... strings) {

    }

    @Override
    public void delayMsgRemove(String s, String s1) {

    }

    @Override
    public void delayMsgSyncRemove(String s, String s1) {

    }

    @Override
    public void pushMsgNoMonitor(String s, String s1, String... strings) {

    }

    @Override
    public void pushMsgMonitorWhenFail(String s, String s1, String... strings) {

    }

    @Override
    public void pushMsgNoMonitor(String s, String s1, boolean b, String... strings) {

    }

    @Override
    public void pushSelfAsyncMsg(String s, String s1, Consumer<XcMsgModel> consumer) {

    }

    @Override
    public void pushSelfAsyncMsgNoMonitor(String s, String s1, Consumer<XcMsgModel> consumer) {

    }

    @Override
    public void pushSelfAsyncMsgMonitorWhenFail(String s, String s1, Consumer<XcMsgModel> consumer) {

    }

    @Override
    public void pushSelfAsyncMsg(String s, String s1, boolean b, Consumer<XcMsgModel> consumer) {

    }

    @Override
    public void pushSelfAsyncMsg(String s, String s1) {

    }

    @Override
    public void pushSelfDelayMsg(String s, String s1, String s2, Date date, boolean b, Consumer<XcMsgModel> consumer) {

    }

    @Override
    public void pushSelfDelayMsg(String s, String s1, String s2, Date date) {

    }
}
