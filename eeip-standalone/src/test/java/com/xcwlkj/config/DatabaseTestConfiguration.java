package com.xcwlkj.config;

import com.xcwlkj.basicinfo.job.CameraStatusJob;
import com.xcwlkj.biz.config.BizMqConfig;
import com.xcwlkj.biz.unifyaccess.http.service.UnifyAccessService;
import com.xcwlkj.biz.unifyaccess.mqtt.service.IMessageService;
import com.xcwlkj.biz.unifyaccess.mqtt.service.impl.MqttMsgRevHandler;
import com.xcwlkj.identityverify.third.unifyaccess.http.service.impl.UnifyAccessServiceImpl;
import com.xcwlkj.pubc.service.file.impl.AliYunOssServiceImpl;
import com.xcwlkj.pubc.service.hhjt.sso.JWTLogin;
import com.xcwlkj.pubc.service.impl.SsoServiceImpl;
import com.xcwlkj.pubc.service.sms.channel.yimei.service.YiMeiSmsServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.mail.javamail.JavaMailSender;

/**
 * 数据库测试专用配置
 * 用于禁用MQTT等非数据库相关服务
 */
@Configuration
@Profile("database-test")
public class DatabaseTestConfiguration {

    /**
     * 使用@MockBean创建MqttMsgRevHandler的Mock对象
     * 这样Spring Boot会自动��建一个Mockito Mock对象
     */
    @MockBean
    private MqttMsgRevHandler mqttMsgRevHandler;

    /**
     * 使用@MockBean创建阿里云OSS服务的Mock对象
     * 这样在测试中不会实际操作OSS
     */
    @MockBean
    private AliYunOssServiceImpl aliYunOssService;
    @MockBean
    private JavaMailSender javaMailSender;

    /**
     * 使用@MockBean创建SSO服务的Mock对象
     * 这样测试中不会加载真实的SSO服务，避免占位符错误
     */
    @MockBean
    private SsoServiceImpl ssoService;
    @MockBean
    private JWTLogin jwtLogin;
    @MockBean
    private YiMeiSmsServiceImpl yiMeiSmsService;
    @MockBean
    private UnifyAccessService unifyAccessService;
    @MockBean
    private CameraStatusJob  cameraStatusJob;

    /**
     * 使用@MockBean创建RocketMQ配置的Mock对象
     * 避免RocketMQ配置类加载和占位符错误
     */
    @MockBean
    private BizMqConfig bizMqConfig;
    @MockBean
    private UnifyAccessServiceImpl  unifyAccessServiceImpl;
    @MockBean
    private IMessageService iMessageService;
}
