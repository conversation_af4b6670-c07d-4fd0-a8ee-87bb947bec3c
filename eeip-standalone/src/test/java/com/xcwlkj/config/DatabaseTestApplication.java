package com.xcwlkj.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.context.annotation.Import;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * 数据库测试专用的Spring Boot应用配置
 * 只加载必要的组件，排除不需要的模块
 */
@SpringBootApplication
@ComponentScan(
    basePackages = {"com.xcwlkj.identityverify.*","com.xcwlkj.core.*"},
    excludeFilters = {
        // 排除资源中心模块
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.xcwlkj\\.resourcecenter\\..*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.xcwlkj\\.biz\\..*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.xcwlkj\\.campus\\..*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.xcwlkj\\.evaluation\\..*"),
            @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com\\.xcwlkj\\.attendance\\..*"),

        // 排除MQTT相关组件
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Mm]qtt.*"),
        // 排除定时任务相关组件
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Jj]ob.*"),
        // 排除监听器组件
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Ll]istener.*"),
        // 排除RocketMQ相关配置
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Mm]qConfig.*"),
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = ".*[Rr]ocketMq.*"),
        // 可以根据需要继续添加其他排除规则
    }
)
@MapperScan("com.xcwlkj.identityverify.mapper")
public class DatabaseTestApplication {
    public static void main(String[] args) {
        SpringApplication.run(DatabaseTestApplication.class, args);
    }
}
