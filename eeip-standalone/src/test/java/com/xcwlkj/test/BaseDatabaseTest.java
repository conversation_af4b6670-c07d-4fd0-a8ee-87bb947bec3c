package com.xcwlkj.test;

import com.xcwlkj.config.DatabaseTestConfiguration;
import com.xcwlkj.config.DatabaseTestApplication;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * 数据库测试基类
 * 只启动数据库相关组件，禁用MQTT等其他服务
 */
@RunWith(SpringRunner.class)
@SpringBootTest(
    classes = {DatabaseTestApplication.class, DatabaseTestConfiguration.class},
    webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("database-test")
@ComponentScan(
    basePackages = {"com.xcwlkj"},
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.xcwlkj.resourcecenter"),
            @ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.xcwlkj.evaluation")
    }
)
@MapperScan("com.xcwlkj.identityverify.mapper")
@TestPropertySource(properties = {
    "test.database-only=true",
    // 禁用MQTT相关配置
    "spring.mqtt.enabled=false",
    "mqtt.enabled=false",
    // 禁用定时任务
    "spring.task.scheduling.pool.size=0",
    // 禁用消息队列
    "spring.rabbitmq.enabled=false",
    // 禁用Eureka客户端
    "eureka.client.enabled=false",
    "eureka.client.register-with-eureka=false",
    "eureka.client.fetch-registry=false",
    // 禁用Feign相关配置
    "spring.cloud.openfeign.enabled=false",
    "feign.client.enabled=false",
    // 禁用Spring Cloud相关功能
    "spring.cloud.discovery.enabled=false",
    "spring.cloud.config.enabled=false",
    "spring.cloud.loadbalancer.enabled=false",
    // 允许bean定义覆盖
    "spring.main.allow-bean-definition-overriding=true",
    // Redis配置 - 从application-alone.yml复制
    "spring.redis.cluster.host=*************",
    "spring.redis.cluster.port=6379",
    "spring.redis.cluster.passwd=xcwlkj2015",
    "spring.redis.cluster.timeOut=5000",
    "spring.redis.cluster.max-redirects=3",
    "spring.redis.cluster.expire-time=180",
    // MQTT配置 - 提供必要的占位符值，但实际不会使用
    "xc.mqtt.url=tcp://localhost:1883",
    "xc.mqtt.client.id=test-client",
    "xc.mqtt.revTopics=test/topic",
    // XXL-JOB配置 - 提供空值来禁用定时任务
    "xxl.job.admin.addresses=",
    "xxl.job.accessToken=",
    "xxl.job.executor.appname=test-app",
    "xxl.job.executor.address=",
    "xxl.job.executor.ip=",
    "xxl.job.executor.port=9999",
    "xxl.job.executor.logpath=/tmp/xxl-job/logs",
    "xxl.job.executor.logretentiondays=3",
    // 阿里云OSS配置 - 使用Mock服务，只提供必要的占位符
    "xc.aliyun.oss.endpoint=http://mock-oss-endpoint.com",
    "xc.aliyun.key.accessKeyId=mock-access-key",
    "xc.aliyun.key.accessKeySecret=mock-access-secret",
    // 邮件配置 - 提供Mock配置值，解决占位符错误
    "spring.mail.host=smtp.mock.com",
    "spring.mail.username=<EMAIL>",
    "spring.mail.password=mock-password",
    "spring.mail.port=587",
    "spring.mail.protocol=smtp",
    "spring.mail.test-connection=false",
    "spring.mail.default-encoding=UTF-8",
    // 本地OSS路径配置
    "xc.local.oss.path=/tmp/mock-oss/",
    "xk.sfzsyljt=0"
    // 在这里添加你的新参数
    // "your.custom.param=value",
    // "another.param=123"
})
public abstract class BaseDatabaseTest {
    // 这里可以添加一些通用的测试工具方法
}
