@echo off
echo ========================================
echo 数据库测试框架运行脚本
echo ========================================

echo.
echo 正在运行数据库测试框架验证...
echo.

REM 设置Maven参数
set MAVEN_OPTS=-Xmx1024m -XX:MaxPermSize=256m

REM 首先检查MySQL驱动
echo [0/4] 检查MySQL驱动是否可用...
call mvn test -Dtest=SimpleDatabaseTest#testDriverClassOnly -q

if %ERRORLEVEL% NEQ 0 (
    echo MySQL驱动检查失败！请查看"数据库驱动问题解决方案.md"文件获取解决方案。
    pause
    exit /b 1
)

REM 运行框架验证测试
echo [1/4] 运行数据库连接测试...
call mvn test -Dtest=SimpleDatabaseTest#testDatabaseConnection -q

if %ERRORLEVEL% NEQ 0 (
    echo 数据库连接测试失败！请检查数据库配置和网络连接。
    pause
    exit /b 1
)

echo [2/4] 运行框架基础功能测试...
call mvn test -Dtest=DatabaseFrameworkTest#testFrameworkBasics -Dspring.profiles.active=database-test -q

if %ERRORLEVEL% NEQ 0 (
    echo 框架基础功能测试失败！
    pause
    exit /b 1
)

echo [3/4] 运行Spring框架数据库连接测试...
call mvn test -Dtest=DatabaseFrameworkTest#testDatabaseConnection -Dspring.profiles.active=database-test -q

if %ERRORLEVEL% NEQ 0 (
    echo Spring框架数据库连接测试失败！
    pause
    exit /b 1
)

echo [4/4] 运行Bean加载检查...
call mvn test -Dtest=DatabaseFrameworkTest#testLoadedBeans -Dspring.profiles.active=database-test -q

if %ERRORLEVEL% NEQ 0 (
    echo Bean加载检查失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo 所有测试通过！数据库测试框架运行正常。
echo ========================================
echo.

echo 你现在可以：
echo 1. 运行具体的业务测试: mvn test -Dtest=YourTestClass -Dspring.profiles.active=database-test
echo 2. 运行所有数据库测试: mvn test -Dspring.profiles.active=database-test
echo 3. 在IDE中直接运行测试类
echo.

pause
