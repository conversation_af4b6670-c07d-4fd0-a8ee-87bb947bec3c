@echo off
echo ========================================
echo 数据库测试问题诊断工具
echo ========================================

echo.
echo 正在诊断数据库测试环境...
echo.

echo [1/5] 检查Maven是否可用...
call mvn --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ✗ Maven未安装或不在PATH中
    echo   请安装Maven并确保mvn命令可用
    goto :end
) else (
    echo ✓ Maven可用
)

echo.
echo [2/5] 检查项目结构...
if not exist "pom.xml" (
    echo ✗ 未找到pom.xml文件
    echo   请确保在eeip-standalone目录下运行此脚本
    goto :end
) else (
    echo ✓ 找到pom.xml文件
)

if not exist "src\test\java\com\xcwlkj\junit\SimpleDatabaseTest.java" (
    echo ✗ 未找到SimpleDatabaseTest.java文件
    echo   请确保测试文件已正确创建
    goto :end
) else (
    echo ✓ 找到测试文件
)

echo.
echo [3/5] 检查MySQL驱动依赖...
findstr /C:"mysql-connector-java" pom.xml >nul
if %ERRORLEVEL% NEQ 0 (
    echo ✗ pom.xml中未找到mysql-connector-java依赖
    echo   请在pom.xml中添加MySQL驱动依赖
    goto :end
) else (
    echo ✓ 找到MySQL驱动依赖配置
)

echo.
echo [4/5] 检查测试配置文件...
if not exist "src\test\resources\application-database-test.yml" (
    echo ✗ 未找到测试配置文件
    echo   请确保application-database-test.yml文件存在
    goto :end
) else (
    echo ✓ 找到测试配置文件
)

echo.
echo [5/5] 尝试编译项目...
echo 正在编译项目（可能需要一些时间）...
call mvn compile -q
if %ERRORLEVEL% NEQ 0 (
    echo ✗ 项目编译失败
    echo   可能的原因：
    echo   - 网络问题导致依赖下载失败
    echo   - 代码语法错误
    echo   - 依赖冲突
    echo.
    echo   建议解决方案：
    echo   1. 检查网络连接
    echo   2. 配置Maven镜像（如阿里云镜像）
    echo   3. 清理本地仓库：mvn clean
    goto :end
) else (
    echo ✓ 项目编译成功
)

echo.
echo ========================================
echo 诊断完成！环境检查通过。
echo ========================================
echo.

echo 现在可以尝试运行测试：
echo 1. 运行简单驱动测试：mvn test -Dtest=SimpleDatabaseTest#testDriverClassOnly
echo 2. 运行数据库连接测试：mvn test -Dtest=SimpleDatabaseTest#testDatabaseConnection
echo 3. 运行完整测试框架：run-database-tests.bat
echo.

:end
pause
