# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

EEIP (Educational Enterprise Information Platform) is a comprehensive Java-based education management system built with Spring Boot microservices architecture. The platform manages student attendance, course scheduling, evaluations, and administrative functions for educational institutions.

## Build and Development Commands

### Maven Build Commands

**do not execute maven test unless the user requires**

```bash
# Clean and compile all modules
mvn clean compile

# Package all services
mvn clean package

# Build specific module (run from module directory)
mvn clean package -DskipTests

# Build with Docker image
mvn clean package dockerfile:build
```

### Running Services

#### Standalone Mode (All-in-one deployment)
```bash
# Run from eeip-standalone directory
java -jar target/eeip-standalone-*.jar

# With specific profile
java -jar target/eeip-standalone-*.jar --spring.profiles.active=alone
```

#### Individual Microservice Mode
```bash
# Run individual service (from specific provider module)
java -jar target/[service-name]-*.jar --spring.profiles.active=develop
```

### Testing
```bash
# Run all tests
mvn test

# Run tests for specific module
mvn test -pl eeip-attendance

# Skip tests during build
mvn clean package -DskipTests
```

## Architecture Overview

### Multi-Module Maven Structure
The project uses a **parent-child Maven module structure** with `xcCloud-root` as the parent POM (external dependency). Key module types:

- **API Modules** (`*-api`): Define service contracts with `@FeignClient` interfaces
- **Provider Modules** (`*-provider-*`): Implement business logic and expose REST endpoints  
- **Business Modules** (`eeip-*`): Domain-specific functionality
- **Common Modules** (`xc-*`): Shared utilities and public services
- **Standalone Module** (`eeip-standalone`): Bundles all services for monolithic deployment

### Microservice Communication Pattern

**API/Provider Separation:**
```
Service A API Module → Service B Provider Module
     ↓                        ↓
@FeignClient Interface → @RestController Implementation
```

**Key Components:**
- **Feign Clients**: Inter-service communication via Spring Cloud OpenFeign
- **Circuit Breaker**: Hystrix integration for fault tolerance
- **Service Discovery**: Spring Cloud-based service registration
- **Load Balancing**: Ribbon for client-side load balancing

### Service Boundaries

| Domain | Modules | Responsibility |
|--------|---------|----------------|
| **Basic Info** | `eeip-basic/*` | Core data management (students, teachers, courses) |
| **Business Logic** | `eeip-biz/*` | Domain workflows (attendance, campus, teaching) |
| **Attendance** | `eeip-attendance` | Check-in/check-out tracking |
| **Evaluation** | `eeip-evaluation` | Assessment and reporting |
| **Identity** | `eeip-identity-verify` | Authentication and verification |
| **Public Services** | `xc-pubc` | Cross-cutting concerns (messaging, files) |

### Layered Architecture

Each service follows consistent **three-layer architecture**:

1. **Controller Layer** (`facade/remote/`, `facade/manager/`)
   - REST endpoints extending `BaseController`
   - Input validation and response wrapping

2. **Service Layer** (`service/`, `service/impl/`)
   - Business logic implementing base service interfaces
   - Transaction management

3. **Data Access Layer** (`mapper/`)
   - MyBatis XML mappers with standardized patterns
   - Tk.MyBatis for enhanced CRUD operations

## Configuration Management

### Environment Profiles
- `application-develop.yml`: Development configuration
- `application-prod.yml`: Production configuration  
- `application-alone.yml`: Standalone deployment configuration
- `bootstrap.yml`: Spring Cloud bootstrap configuration

### Key Configuration Areas
- **Database**: MySQL connections per environment
- **Redis**: Caching and session management
- **Message Queue**: RocketMQ for async processing
- **External Services**: File storage, SMS, email integration
- **Docker**: Each service includes Dockerfile for containerization

## Database Access Patterns

### MyBatis XML Structure
Standard mapper XML template includes:
```xml
<sql id="Base_Column_List">...</sql>
<sql id="Base_Where_Condition">...</sql>
<sql id="Base_Set_Condition">...</sql>
```

### Common Operations
- **CRUD**: Via base mapper interfaces
- **Dynamic Queries**: Using MyBatis `<if>` conditions
- **Batch Operations**: Dedicated batch insert/update methods
- **Pagination**: PageHelper integration

## Development Patterns

### Service Implementation
- Extend `BaseService<T>` for common CRUD operations
- Implement specific business interfaces
- Use `@Transactional` for data consistency

### Controller Development
- Extend `BaseController` for common functionality
- Use consistent response wrapping patterns
- Input validation with Bean Validation annotations

### Feign Client Integration
- Define clients in API modules with `@FeignClient`
- Use `contextId` for multiple clients to same service
- Implement fallback classes for circuit breaker pattern

## Deployment Modes

### Standalone Deployment
- Single JAR containing all services
- Uses `@EnableAloneFeignClient` for local service calls
- Component scanning: `@ComponentScan(basePackages = {"com.xcwlkj.*"})`
- Ideal for development and small deployments

### Microservice Deployment  
- Independent service deployment
- Service discovery and load balancing
- Docker containerization support
- Ideal for production and scalable deployments

## File Structure Navigation

- **Business Logic**: Look in `[service]/src/main/java/com/xcwlkj/[domain]/service/`
- **REST APIs**: Check `[service]/src/main/java/com/xcwlkj/[domain]/facade/remote/`
- **Data Access**: Find in `[service]/src/main/resources/mapper/`
- **Configuration**: Located in `[service]/src/main/resources/`
- **API Contracts**: Defined in `[service]-api/src/main/java/com/xcwlkj/[domain]/service/`

## External Dependencies

The project relies on `xcCloud-root` parent POM (external) which provides:
- Dependency management for Spring Boot, Cloud, and other frameworks
- Plugin management for Maven build lifecycle
- Common properties and configurations
- Version management across all modules

## Development Workflow Framework

### Requirements Management
Use **version-based requirement files** for organized development planning:

**File Structure:**
```
/requirements/
├── v1.0-用户管理功能.md
├── v1.1-考勤系统优化.md  
├── v1.2-评价系统重构.md
├── v2.0-身份验证升级.md
└── README.md (需求总览)
```

**Naming Convention:**
- `v[版本号]-[功能描述].md`
- Alternative: `[日期]-[功能描述].md`

**Requirement File Template:**
```markdown
# v1.x - [功能名称]

## 版本信息
- 版本号: v1.x
- 创建日期: YYYY-MM-DD
- 状态: 规划中/开发中/已完成
- 负责模块: [相关的eeip模块]

## 需求概述
[详细描述要实现的功能和业务目标]

## 具体需求
- 功能点1: [描述]
- 功能点2: [描述]
- 用户场景: [描述使用流程]
- 数据结构: [相关的数据库表或实体]

## 技术要求
- 性能要求: [响应时间、并发量等]
- 安全要求: [权限控制、数据安全等]
- 集成要求: [与其他模块的集成点]

## 依赖关系
- 基于: [前置需求或版本]
- 影响模块: [受影响的eeip模块列表]
- 外部依赖: [第三方服务或组件]

## 验收标准
- [ ] 功能测试点1
- [ ] 功能测试点2
- [ ] 性能指标达标
- [ ] 集成测试通过

## 开发计划
- 预计工期: [天数]
- 关键里程碑: [重要节点]
- 风险点: [潜在技术难点]
```

### Development Process
1. **需求创建**: 用户创建详细需求文件
2. **需求分析**: Claude分析技术可行性和架构影响
3. **方案规划**: 共同讨论技术方案和实施步骤
4. **开发执行**: Claude按方案完成代码实现
5. **状态更新**: 及时更新需求文件中的开发状态

### Benefits of This Framework
- **版本化管理**: 每个需求独立跟踪，便于版本控制
- **需求清晰**: 详细的需求文档减少沟通成本  
- **方案透明**: 技术方案和实施计划公开讨论
- **进度可视**: 开发状态实时更新
- **知识沉淀**: 需求和方案文档化保存