/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2018 All Rights Reserved.
 */
package com.xcwlkj.gxjybsb;

import com.xcwlkj.gxjybsb.util.SpringUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.cloud.netflix.hystrix.EnableHystrix;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
//import org.springframework.web.filter.CorsFilter;
//import org.springframework.context.annotation.Bean;
//import org.springframework.web.cors.CorsConfiguration;
//import org.springframework.web.cors.UrlBasedCorsConfigurationSource;


/**
 * 
 * <AUTHOR>
 * @version $Id: SbywApplication.java, v 0.1 2018年8月28日 下午3:45:38 danfeng.zhou Exp $
 */
@EnableAsync
@EnableRetry
@EnableHystrix
@EnableFeignClients(basePackages = { "com.xcwlkj.service", "com.xcwlkj.client"  ,"com.xcwlkj.*.service","com.xcwlkj.*.web","com.xcwlkj.*.job" })
@SpringCloudApplication
@EnableTransactionManagement
@ComponentScan(basePackages = { "com.xcwlkj.*"})
public class GxjybsbApplication {

    /**
     * The entry point of application.
     *
     * @param args the input arguments
     */
    public static void main(String[] args) {
        ApplicationContext app = SpringApplication.run(GxjybsbApplication.class, args);
        SpringUtil.setApplicationContext(app);
    }
    
//    @Bean
//    public CorsFilter corsFilter() {
//        final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        final CorsConfiguration config = new CorsConfiguration();
//        // 允许cookies跨域
//        config.setAllowCredentials(true);
//        // #允许向该服务器提交请求的URI，*表示全部允许，在SpringMVC中，如果设成*，会自动转成当前请求头中的Origin
//        config.addAllowedOrigin("*");
//        // #允许访问的头信息,*表示全部
//        config.addAllowedHeader("*");
//        // 预检请求的缓存时间（秒），即在这个时间段里，对于相同的跨域请求不会再预检了
//        config.setMaxAge(18000L);
//        // 允许提交请求的方法，*表示全部允许
//        config.addAllowedMethod("*");
//        source.registerCorsConfiguration("/**", config);
//        return new CorsFilter(source);
//    }

}
