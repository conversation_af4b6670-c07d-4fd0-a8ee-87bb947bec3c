/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.web;

import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.resourcecenter.model.domain.JcxxSbxx;
import com.xcwlkj.resourcecenter.model.dto.device.*;
import com.xcwlkj.resourcecenter.model.vo.device.ListV1VO;
import com.xcwlkj.resourcecenter.model.vo.device.SelectCsbhListVO;
import com.xcwlkj.resourcecenter.model.vo.device.SysListV1VO;
import com.xcwlkj.resourcecenter.model.vo.device.XlhListByJshVO;
import com.xcwlkj.resourcecenter.service.BasicDeviceFeignApi;
import com.xcwlkj.resourcecenter.service.JcxxSbcsgxService;
import com.xcwlkj.resourcecenter.service.JcxxSbxxService;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * Device接口
 * <AUTHOR>
 * @version $Id: DeviceFeignClient.java, v 0.1 2022年10月13日 14时31分 xcwlkj.com Exp $
 */
@RestController
public class DeviceFeignClient extends BaseFeignClient implements BasicDeviceFeignApi {

    @Resource
    private JcxxSbxxService jcxxSbxxService;

	@Resource
	private JcxxSbcsgxService jcxxSbcsgxService;
    
    /** 
     * @see BasicDeviceFeignApi#sysListV1(com.xcwlkj.resourcecenter.model.dto.device.SysListV1DTO)
     */
	@Override
	public Wrapper<SysListV1VO> sysListV1(@RequestBody @Validated SysListV1DTO dto) {
		logger.info("设备列表SysListV1DTO={}", dto);
		ListV1DTO listV1DTO = new ListV1DTO();
		BeanUtil.copyProperties(dto, listV1DTO);
		ListV1VO listV1VO = jcxxSbxxService.listV1(listV1DTO);
		SysListV1VO result = new SysListV1VO();
		BeanUtil.copyProperties(listV1VO, result);

		logger.info("sysListV1 - 设备列表. [OK] SysListV1VO={}", result);
		return WrapMapper.ok(result);
	}
    /** 
     * @see BasicDeviceFeignApi#xlhListByJsh(com.xcwlkj.resourcecenter.model.dto.device.XlhListByJshDTO)
     */
	@Override
	public Wrapper<XlhListByJshVO> xlhListByJsh(@RequestBody @Validated XlhListByJshDTO dto) {
		logger.info("根据教室号返回设备序列号列表XlhListByJshDTO={}", dto);
		XlhListByJshVO result =jcxxSbxxService.xlhListByJsh(dto);
		logger.info("xlhListByJsh - 根据教室号返回设备序列号列表. [OK] XlhListByJshVO={}", result);
		return WrapMapper.ok(result);
	}
	/**
	 * @see BasicDeviceFeignApi#selectCsbhListBindSxj()
	 */
	@Override
	public Wrapper<SelectCsbhListVO> selectCsbhListBindSxj() {
		logger.info("查询所有绑定设备的场所编号列表");
		SelectCsbhListVO result = jcxxSbcsgxService.selectCsbhListBindSxj();
		logger.info("selectCsbhListBindSxj - 查询所有绑定设备的场所编号列表. [OK] SelectCsbhListVO={}", result);
		return WrapMapper.ok(result);
	}
    /** 
     * @see BasicDeviceFeignApi#deviceAdd(com.xcwlkj.resourcecenter.model.dto.device.DeviceAddDTO)
     */
	@Override
	public Wrapper<Void> deviceAdd(@RequestBody @Validated DeviceAddDTO dto) {
		logger.info("设备添加DeviceAddDTO={}", dto);
		Example example = new Example(JcxxSbxx.class);
		example.createCriteria()
				.andEqualTo("xlh", dto.getXlh())
				.orEqualTo("sbxxbh", dto.getSbbh());
		List<JcxxSbxx> jcxxSbxxes = jcxxSbxxService.selectByExample(example);
		if (jcxxSbxxes.isEmpty()) {
			AddV1DTO addV1DTO = BeanUtil.copyProperties(dto, AddV1DTO.class);
			jcxxSbxxService.addV1(addV1DTO);
		}else {
			logger.info("设备编号[{}]或序列号[{}]已存在, 修改设备信息",dto.getSbbh(), dto.getXlh());
			ModifyV1DTO modifyV1DTO = BeanUtil.copyProperties(dto, ModifyV1DTO.class);
			modifyV1DTO.setSbbh(jcxxSbxxes.get(0).getSbxxbh());
			jcxxSbxxService.modifyV1(modifyV1DTO);
		}
		logger.info("deviceAdd - 设备添加. [OK] ");
		return WrapMapper.ok();
	}

	@Override
	public Wrapper<Boolean> xlhCheck(String xlh) {
		boolean result = true;
		logger.info("设备序列号校验, xlh={}", xlh);
		Example example = new Example(JcxxSbxx.class);
		example.createCriteria()
				.andEqualTo("xlh", xlh);
		List<JcxxSbxx> jcxxSbxxes = jcxxSbxxService.selectByExample(example);
		if (jcxxSbxxes.isEmpty()){
			result = false;
		}
		logger.info("checkXlh - 设备序列号校验. [OK] result={}", result);
		return WrapMapper.ok(result);
	}


	/**
	 * @see BasicDeviceFeignApi#onlineStatusUpdate()
	 */
	@Override
	public Wrapper<Void> onlineStatusUpdate() {
		logger.info("设备在线状态更新");

		jcxxSbxxService.onlineStatusUpdate();

		logger.info("设备在线状态更新 OK.");
		return WrapMapper.ok();
	}
}



