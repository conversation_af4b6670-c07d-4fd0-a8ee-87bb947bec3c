/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.resourcecenter.service;

import com.xcwlkj.resourcecenter.model.dto.bpgl.CommandLogListDTO;
import com.xcwlkj.resourcecenter.model.vo.bpgl.CommandLogListVO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 设备命令日志表服务
 * <AUTHOR>
 * @version $Id: SbCommandLogService.java, v 0.1 2024年12月03日 15时13分 xcwlkj.com Exp $
 */
@Service
public interface SbCommandLogService  {

    void batchInit(List<String> deviceList, String msgId, String type, String ywlx, String message);

    void batchUpdate(List<String> deviceList, String msgId, String type, Integer tryCount);
	/**
	 * 命令日志列表
	 * @param dto
	 * @return
	 */
	CommandLogListVO commandLogList(CommandLogListDTO dto);
}