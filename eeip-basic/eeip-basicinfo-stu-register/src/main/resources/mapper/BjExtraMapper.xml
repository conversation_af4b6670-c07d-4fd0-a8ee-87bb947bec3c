<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.sturegister.mapper.BjExtraMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.sturegister.model.domain.BjExtra">
        <id column="bah" jdbcType="VARCHAR" property="bah" />
        <result column="jsh" jdbcType="VARCHAR" property="jsh" />
        <result column="jsmc" jdbcType="VARCHAR" property="jsmc" />
        <result column="alias" jdbcType="VARCHAR" property="alias" />
        <result column="slogan" jdbcType="VARCHAR" property="slogan" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        bah,
        jsh,
        jsmc,
        alias,
        slogan

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="bah != null and bah != ''">
            AND bah = #{bah,jdbcType=VARCHAR}
        </if>
        <if test="jsh != null and jsh != ''">
            AND jsh = #{jsh,jdbcType=VARCHAR}
        </if>
        <if test="jsmc != null and jsmc != ''">
            AND jsmc = #{jsmc,jdbcType=VARCHAR}
        </if>
        <if test="alias != null and alias != ''">
            AND alias = #{alias,jdbcType=VARCHAR}
        </if>
        <if test="slogan != null and slogan != ''">
            AND slogan = #{slogan,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="bah != null ">
            bah = #{bah,jdbcType=VARCHAR},
        </if>
        <if test="jsh != null ">
            jsh = #{jsh,jdbcType=VARCHAR},
        </if>
        <if test="jsmc != null ">
            jsmc = #{jsmc,jdbcType=VARCHAR},
        </if>
        <if test="alias != null ">
            alias = #{alias,jdbcType=VARCHAR},
        </if>
        <if test="slogan != null ">
            slogan = #{slogan,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

    <update id="batchUpdateJshAndJsmc" parameterType="java.util.List">
        update bj_extra
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="jsmc=case " suffix="end,">
                <foreach collection="list" item="item">
                    when jsh=#{item.tempClassroomId} then #{item.classroomName}
                </foreach>
            </trim>
            <trim prefix="jsh=case " suffix="end,">
                <foreach collection="list" item="item">
                    when jsh=#{item.tempClassroomId} then #{item.classroomId}
                </foreach>
            </trim>
        </trim>
        <where>
            jsh in
            <foreach collection="list" item="item" separator="," open="(" close=")">
                #{item.tempClassroomId}
            </foreach>
        </where>
    </update>
    <select id="bindDetail" resultType="com.xcwlkj.sturegister.model.vo.bj.BjExtraDetailVO">
        SELECT
            bj.bjmc,
            (
                SELECT GROUP_CONCAT(jzg.xm)
                FROM jcxx_jzgjbxx jzg
                WHERE FIND_IN_SET(jzg.gh, bj.bzrgh)
            ) bzrmc,
            extra.slogan slogan
        FROM
            bj_extra extra
                LEFT JOIN jcxx_bj bj ON bj.bah = extra.bah
        WHERE
            extra.jsh = #{jsh}
    </select>
</mapper>
