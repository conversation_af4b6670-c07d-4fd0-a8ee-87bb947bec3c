/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.znfx.service.impl.znfx;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.BeanUtil;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.znfx.mapper.znfx.SyncKdKcxxMapper;
import com.xcwlkj.znfx.mapper.znfx.SyncKdKdxxMapper;
import com.xcwlkj.znfx.mapper.znfx.SyncKdKqxxMapper;
import com.xcwlkj.znfx.mapper.znfx.SyncKsKsccMapper;
import com.xcwlkj.znfx.model.adapter.req.KsccDTO;
import com.xcwlkj.znfx.model.domain.enums.KcTypeEnum;
import com.xcwlkj.znfx.model.domain.exchange.KdKcxx;
import com.xcwlkj.znfx.model.domain.znfx.SyncKdKcxx;
import com.xcwlkj.znfx.model.domain.znfx.SyncKdKdxx;
import com.xcwlkj.znfx.model.domain.znfx.SyncKsKscc;
import com.xcwlkj.znfx.model.dto.localdata.KcLbDTO;
import com.xcwlkj.znfx.model.dto.localdata.LjkcLbDTO;
import com.xcwlkj.znfx.service.exchange.KdKcxxService;
import com.xcwlkj.znfx.service.znfx.SyncKdKcxxService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * sync_kd_kcxx服务
 *
 * <AUTHOR>
 * @version $Id: SyncKdKcxxServiceImpl.java, v 0.1 2021年08月06日 13时43分 xcwlkj.com Exp $
 */
@Service("syncKdKcxxService")
public class SyncKdKcxxServiceImpl implements SyncKdKcxxService {

    @Resource
    private SyncKdKcxxMapper modelMapper;
    @Resource
    private KdKcxxService kdKcxxService;
    @Resource
    private SyncKsKsccMapper syncKsKsccMapper;
    @Resource
    private SyncKdKqxxMapper syncKdKqxxMapper;
    @Resource
    private SyncKdKdxxMapper syncKdKdxxMapper;

    @Override
    public SyncKdKcxx selectByKey(String kcbzid) {
        return modelMapper.selectByPrimaryKey(kcbzid);
    }

    @Override
    public List<SyncKdKcxx> selectByExample(Example example) {
        return modelMapper.selectByExample(example);
    }

    @Override
    public void updateByKey(SyncKdKcxx kdkcxx) {
        kdkcxx.setUpdateTime(DateUtil.getCurrentDT());
        modelMapper.updateByPrimaryKey(kdkcxx);
    }

    @Override
    public void insert(SyncKdKcxx kdkcxx) {
        kdkcxx.setSczt(ScztEnum.NOTDEL.getCode());
        kdkcxx.setCreateTime(DateUtil.getCurrentDT());
    }

    @Override
    public int updateByExampleSelective(SyncKdKcxx kdkcxx, Example example) {
        kdkcxx.setUpdateTime(DateUtil.getCurrentDT());
        return modelMapper.updateByExampleSelective(kdkcxx, example);
    }

    @Override
    public List<SyncKdKcxx> selectAll() {
        return modelMapper.selectAll();
    }

    @Override
    public SyncKdKcxx selectOne(SyncKdKcxx kdkcxx) {
        return modelMapper.selectOne(kdkcxx);
    }

    @Override
    public void batchInsertOrUpdate(List<SyncKdKcxx> syncKdKcxxList) {
        modelMapper.batchInsertOrUpdate(syncKdKcxxList);
    }

    @Override
    public void syncKdKcxx(String ksjhbh, String ccm) {
        Example example = new Example(KdKcxx.class, true);
        Example.Criteria criteria = example.createCriteria();
        if (ksjhbh != null) {
            criteria.andEqualTo("ksjhbh", ksjhbh);
        }
        if (ccm != null) {
            criteria.andEqualTo("ccm", ccm);
        }
        Page page = PageHelper.startPage(1, 500);
        List<KdKcxx> kdKcxxList = kdKcxxService.selectByExample(example);

        for (int i = 0; i < page.getPages(); i++) {
            List<SyncKdKcxx> syncKdKcxxList = new ArrayList<>(kdKcxxList.size());
            BeanUtil.copyListProperties(kdKcxxList, syncKdKcxxList, SyncKdKcxx.class);

            modelMapper.batchInsertOrUpdate(syncKdKcxxList);
            PageHelper.startPage(i + 2, 500, false);
            kdKcxxList = kdKcxxService.selectByExample(example);
        }
    }

    @Override
    public void syncKdKcxx(String ksjhbh) {
        syncKdKcxx(ksjhbh, null);
    }

    @Override
    public void syncKdKcxx() {
        syncKdKcxx(null, null);
    }

    @Override
    public Map<String, Object> ljkcLb(LjkcLbDTO dto) {
        Example example = new Example(SyncKdKcxx.class, true);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sczt", "0");
        criteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        criteria.andEqualTo("ccm", dto.getCcm());
        if (StringUtils.isNotBlank(dto.getKcmc())) {
            criteria.andEqualTo("kcmc", dto.getKcmc());
        }
        if (StringUtils.isNotBlank(dto.getKcbh())) {
            criteria.andEqualTo("kcbh", dto.getKcbh());
        }
        if (StringUtils.isNotBlank(dto.getKdbh())) {
            criteria.andEqualTo("kdbh", dto.getKdbh());
        }

        Page page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<SyncKdKcxx> syncKdKcxxList = modelMapper.selectByExample(example);

        Map<String, Object> result = new HashMap<>();
        result.put("syncKdKcxxList", syncKdKcxxList);
        result.put("totalRows", (int) page.getTotal());

        return result;

    }

    @Override
    public int selectCount() {
        int count = modelMapper.selectCount(new SyncKdKcxx());
        return count;
    }

    @Override
    public int selectCount(KcLbDTO dto) {
        SyncKdKcxx syncKdKcxx = new SyncKdKcxx();
        syncKdKcxx.setCcm(dto.getCcm());
        syncKdKcxx.setKsjhbh(dto.getKsjhbh());
        syncKdKcxx.setSczt("0");
        int count = modelMapper.selectCount(syncKdKcxx);
        return count;
    }


    @Override
    public List<KsccDTO> KsccLb(KcLbDTO dto) {
        Example example = new Example(SyncKdKcxx.class, true);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sczt", "0");
        criteria.andEqualTo("ksjhbh", dto.getKsjhbh());
        if (StringUtils.isNotBlank(dto.getCcm())) {
            criteria.andEqualTo("ccm", dto.getCcm());
        }
        if (StringUtils.isNotBlank(dto.getKcmc())) {
            criteria.andEqualTo("kcmc", dto.getKcmc());
        }
        if (StringUtils.isNotBlank(dto.getKcbh())) {
            criteria.andEqualTo("kcbh", dto.getKcbh());
        }
        if (StringUtils.isNotBlank(dto.getKdbh())) {
            criteria.andEqualTo("kdbh", dto.getKdbh());
        }

        Page page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<SyncKdKcxx> syncKdKcxxList = modelMapper.selectByExample(example);
        List<KsccDTO> KsccDTOList = new ArrayList<>();
        for (SyncKdKcxx syncKdKcxx : syncKdKcxxList) {
            Example ksccExample = new Example(SyncKsKscc.class, true);
            Example.Criteria ksccCriteria = ksccExample.createCriteria();
            ksccCriteria.andEqualTo("scztw", "0");
            ksccCriteria.andEqualTo("ksjhbh", syncKdKcxx.getKsjhbh());
            ksccCriteria.andEqualTo("ccm", syncKdKcxx.getCcm());
            List<SyncKsKscc> syncKsKsccs = syncKsKsccMapper.selectByExample(ksccExample);


            Example kdxxExample = new Example(SyncKdKdxx.class, true);
            Example.Criteria kdxxCriteria = kdxxExample.createCriteria();
            kdxxCriteria.andEqualTo("sczt", "0");
            kdxxCriteria.andEqualTo("kdbh", syncKdKcxx.getKdbh());
            kdxxCriteria.andEqualTo("ccm", syncKdKcxx.getCcm());
            kdxxCriteria.andEqualTo("ksjhbh", syncKdKcxx.getKsjhbh());
            List<SyncKdKdxx> syncKdKdxxes = syncKdKdxxMapper.selectByExample(kdxxExample);


            KsccDTO ksccDTO = new KsccDTO();
            ksccDTO.setSzds(syncKdKdxxes.get(0).getKdszsq());
            ksccDTO.setSzqx(syncKdKdxxes.get(0).getKdszqx());
            ksccDTO.setKdmc(syncKdKdxxes.get(0).getKdmc());
            ksccDTO.setCcmc(syncKsKsccs.get(0).getCcmc());
            ksccDTO.setType(KcTypeEnum.KC.getCode());
            ksccDTO.setKcbh(syncKdKcxx.getKcbh());
            ksccDTO.setKcmc(syncKdKcxx.getBzhkcmc());
            ksccDTO.setSipdz(syncKdKcxx.getSipid());
            KsccDTOList.add(ksccDTO);
        }
        return KsccDTOList;
    }
}