package com.xcwlkj.znfx.model.domain.znfx; /**
 * xcwlkj.com Inc.
 * Copyright (c) 2021-2031 All Rights Reserved.
 */


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;


/**
 * znfx_resource_relation
 *
 * <AUTHOR>
 * @version $Id: ZnfxResourceRelation.java, v 0.1 2021年10月29日 15时26分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "znfx_resource_relation")
public class ZnfxResourceRelation implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 资源编号 */
    @Id
    @Column(name = "id")
    private String id;
    /** 任务编号 */
    @Column(name = "task_id")
    private String taskId;
    /** 所属场所 */
    @Column(name = "place_id")
    private String placeId;
    /**
     * 资源类型
     * CAMERA 摄像头 BYKC 备用考场 KCXX 考场
     */
    @Column(name = "resource_type")
    private String resourceType;
    /** 资源地址 */
    @Column(name = "resource_uri")
    private String resourceUri;
    /** 资源协议 */
    @Column(name = "resource_protocol")
    private String resourceProtocol;
    /** 资源帐户 */
    @Column(name = "resource_user")
    private String resourceUser;
    /** 资源密码 */
    @Column(name = "resource_password")
    private String resourcePassword;
    /** 对应资源的id */
    @Column(name = "refer_id")
    private String referId;
    /** 回调地址 */
    @Column(name = "call_back_uri")
    private String callBackUri;
    /** 预警标签  -1：全部记录上报,0：筛选正常记录上报,1：筛选异常记录上报 */
    @Column(name = "alarm_tag")
    private String alarmTag;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date createTime;
    /** 修改时间 */
    @Column(name = "update_time")
    private Date updateTime;
    /** 设备名称 */
    @Column(name = "sbmc")
    private String sbmc;
    /** 场所名称 */
    @Column(name = "csmc")
    private String csmc;
    /** 祖先节点 */
    @Column(name = "jdzx")
    private String jdzx;
    /** 考试管理机构ID */
    @Column(name = "ksgljgid")
    private String ksgljgid;
    /** 开启状态，1开启，0未开启 */
    @Column(name = "status")
    private Integer status;
    /** 扩展字段 */
    @Column(name = "refer_param1")
    private String referParam1;
    /** 所在省码 */
    @Column(name = "szsfm")
    private String szsfm;
    /** 所在省 */
    @Column(name = "szsf")
    private String szsf;
    /** 所在市码 */
    @Column(name = "szsqm")
    private String szsqm;
    /** 所在市 */
    @Column(name = "szsq")
    private String szsq;
    /** 所在区县码 */
    @Column(name = "szqxm")
    private String szqxm;
    /** 所在区县 */
    @Column(name = "szqx")
    private String szqx;
    /** 场所类型 BMS BYKC KCXX KWBGS GLKC OTHER */
    @Column(name = "place_type")
    private String placeType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


