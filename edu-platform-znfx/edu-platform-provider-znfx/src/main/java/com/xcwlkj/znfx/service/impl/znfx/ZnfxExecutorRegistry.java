package com.xcwlkj.znfx.service.impl.znfx;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.xcwlkj.znfx.model.domain.enums.ZnfxTaskTypeEnum;
import com.xcwlkj.znfx.service.znfx.ZnfxService;

/**
 * <AUTHOR> shark
 * @date 2021/5/27 13:43
 */
@Component
public class ZnfxExecutorRegistry {
    private final static Map<ZnfxTaskTypeEnum, ZnfxService> znfxExecutors = new HashMap<>();

    public void register(ZnfxTaskTypeEnum taskType, ZnfxService executor) {
        znfxExecutors.put(taskType, executor);
    }

    public ZnfxService getExecutor(ZnfxTaskTypeEnum taskType) {
        ZnfxService executor = znfxExecutors.get(taskType);
        if(executor == null){
            throw new RuntimeException("not find znfxExecutor by taskType[" + taskType.getCode() + "]");
        }
        return executor;
    }
}
