<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.znfx.mapper.exchange.KdKdxxMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.znfx.model.domain.exchange.KdKdxx">
        <id column="kdbzid" jdbcType="VARCHAR" property="kdbzid" />
        <result column="kqbh" jdbcType="VARCHAR" property="kqbh" />
        <result column="kdbh" jdbcType="VARCHAR" property="kdbh" />
        <result column="ccm" jdbcType="VARCHAR" property="ccm" />
        <result column="ksjhbh" jdbcType="VARCHAR" property="ksjhbh" />
        <result column="kdmc" jdbcType="VARCHAR" property="kdmc" />
        <result column="kdjc" jdbcType="VARCHAR" property="kdjc" />
        <result column="sfbzhkd" jdbcType="VARCHAR" property="sfbzhkd" />
        <result column="bzhkdid" jdbcType="VARCHAR" property="bzhkdid" />
        <result column="bzhkdmc" jdbcType="VARCHAR" property="bzhkdmc" />
        <result column="kdlbm" jdbcType="VARCHAR" property="kdlbm" />
        <result column="kdlb" jdbcType="VARCHAR" property="kdlb" />
        <result column="kdszsfm" jdbcType="VARCHAR" property="kdszsfm" />
        <result column="kdszsf" jdbcType="VARCHAR" property="kdszsf" />
        <result column="kdszsqm" jdbcType="VARCHAR" property="kdszsqm" />
        <result column="kdszsq" jdbcType="VARCHAR" property="kdszsq" />
        <result column="kdszqxm" jdbcType="VARCHAR" property="kdszqxm" />
        <result column="kdszqx" jdbcType="VARCHAR" property="kdszqx" />
        <result column="kddz" jdbcType="VARCHAR" property="kddz" />
        <result column="kdjd" jdbcType="DECIMAL" property="kdjd" />
        <result column="kdwd" jdbcType="DECIMAL" property="kdwd" />
        <result column="kdfzrxm" jdbcType="VARCHAR" property="kdfzrxm" />
        <result column="kdfzrdh" jdbcType="VARCHAR" property="kdfzrdh" />
        <result column="fwbgsfzrxm" jdbcType="VARCHAR" property="fwbgsfzrxm" />
        <result column="kwbgsdh" jdbcType="VARCHAR" property="kwbgsdh" />
        <result column="jsfzrxm" jdbcType="VARCHAR" property="jsfzrxm" />
        <result column="jsfzrdh" jdbcType="VARCHAR" property="jsfzrdh" />
        <result column="kdxzqhm" jdbcType="VARCHAR" property="kdxzqhm" />
        <result column="sczt" jdbcType="VARCHAR" property="sczt" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="sjksgljgid" jdbcType="VARCHAR" property="sjksgljgid" />
        <result column="kds" jdbcType="VARCHAR" property="kds" />
        <result column="kcs" jdbcType="VARCHAR" property="kcs" />
        <result column="kss" jdbcType="VARCHAR" property="kss" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        kdbzid,
        kqbh,
        kdbh,
        ccm,
        ksjhbh,
        kdmc,
        kdjc,
        sfbzhkd,
        bzhkdid,
        bzhkdmc,
        kdlbm,
        kdlb,
        kdszsfm,
        kdszsf,
        kdszsqm,
        kdszsq,
        kdszqxm,
        kdszqx,
        kddz,
        kdjd,
        kdwd,
        kdfzrxm,
        kdfzrdh,
        fwbgsfzrxm,
        kwbgsdh,
        jsfzrxm,
        jsfzrdh,
        kdxzqhm,
        sczt,
        create_time,
        update_time,
        sjksgljgid,
        kds,
        kcs,
        kss

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="kdbzid != null and kdbzid != ''">
            AND kdbzid = #{kdbzid,jdbcType=VARCHAR}
        </if>
        <if test="kqbh != null and kqbh != ''">
            AND kqbh = #{kqbh,jdbcType=VARCHAR}
        </if>
        <if test="kdbh != null and kdbh != ''">
            AND kdbh = #{kdbh,jdbcType=VARCHAR}
        </if>
        <if test="ccm != null and ccm != ''">
            AND ccm = #{ccm,jdbcType=VARCHAR}
        </if>
        <if test="ksjhbh != null and ksjhbh != ''">
            AND ksjhbh = #{ksjhbh,jdbcType=VARCHAR}
        </if>
        <if test="kdmc != null and kdmc != ''">
            AND kdmc = #{kdmc,jdbcType=VARCHAR}
        </if>
        <if test="kdjc != null and kdjc != ''">
            AND kdjc = #{kdjc,jdbcType=VARCHAR}
        </if>
        <if test="sfbzhkd != null and sfbzhkd != ''">
            AND sfbzhkd = #{sfbzhkd,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdid != null and bzhkdid != ''">
            AND bzhkdid = #{bzhkdid,jdbcType=VARCHAR}
        </if>
        <if test="bzhkdmc != null and bzhkdmc != ''">
            AND bzhkdmc = #{bzhkdmc,jdbcType=VARCHAR}
        </if>
        <if test="kdlbm != null and kdlbm != ''">
            AND kdlbm = #{kdlbm,jdbcType=VARCHAR}
        </if>
        <if test="kdlb != null and kdlb != ''">
            AND kdlb = #{kdlb,jdbcType=VARCHAR}
        </if>
        <if test="kdszsfm != null and kdszsfm != ''">
            AND kdszsfm = #{kdszsfm,jdbcType=VARCHAR}
        </if>
        <if test="kdszsf != null and kdszsf != ''">
            AND kdszsf = #{kdszsf,jdbcType=VARCHAR}
        </if>
        <if test="kdszsqm != null and kdszsqm != ''">
            AND kdszsqm = #{kdszsqm,jdbcType=VARCHAR}
        </if>
        <if test="kdszsq != null and kdszsq != ''">
            AND kdszsq = #{kdszsq,jdbcType=VARCHAR}
        </if>
        <if test="kdszqxm != null and kdszqxm != ''">
            AND kdszqxm = #{kdszqxm,jdbcType=VARCHAR}
        </if>
        <if test="kdszqx != null and kdszqx != ''">
            AND kdszqx = #{kdszqx,jdbcType=VARCHAR}
        </if>
        <if test="kddz != null and kddz != ''">
            AND kddz = #{kddz,jdbcType=VARCHAR}
        </if>
        <if test="kdjd != null and kdjd != ''">
            AND kdjd = #{kdjd,jdbcType=DECIMAL}
        </if>
        <if test="kdwd != null and kdwd != ''">
            AND kdwd = #{kdwd,jdbcType=DECIMAL}
        </if>
        <if test="kdfzrxm != null and kdfzrxm != ''">
            AND kdfzrxm = #{kdfzrxm,jdbcType=VARCHAR}
        </if>
        <if test="kdfzrdh != null and kdfzrdh != ''">
            AND kdfzrdh = #{kdfzrdh,jdbcType=VARCHAR}
        </if>
        <if test="fwbgsfzrxm != null and fwbgsfzrxm != ''">
            AND fwbgsfzrxm = #{fwbgsfzrxm,jdbcType=VARCHAR}
        </if>
        <if test="kwbgsdh != null and kwbgsdh != ''">
            AND kwbgsdh = #{kwbgsdh,jdbcType=VARCHAR}
        </if>
        <if test="jsfzrxm != null and jsfzrxm != ''">
            AND jsfzrxm = #{jsfzrxm,jdbcType=VARCHAR}
        </if>
        <if test="jsfzrdh != null and jsfzrdh != ''">
            AND jsfzrdh = #{jsfzrdh,jdbcType=VARCHAR}
        </if>
        <if test="kdxzqhm != null and kdxzqhm != ''">
            AND kdxzqhm = #{kdxzqhm,jdbcType=VARCHAR}
        </if>
        <if test="sczt != null and sczt != ''">
            AND sczt = #{sczt,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null and createTime != ''">
            AND create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null and updateTime != ''">
            AND update_time = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="sjksgljgid != null and sjksgljgid != ''">
            AND sjksgljgid = #{sjksgljgid,jdbcType=VARCHAR}
        </if>
        <if test="kds != null and kds != ''">
            AND kds = #{kds,jdbcType=VARCHAR}
        </if>
        <if test="kcs != null and kcs != ''">
            AND kcs = #{kcs,jdbcType=VARCHAR}
        </if>
        <if test="kss != null and kss != ''">
            AND kss = #{kss,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="kdbzid != null ">
            kdbzid = #{kdbzid,jdbcType=VARCHAR},
        </if>
        <if test="kqbh != null ">
            kqbh = #{kqbh,jdbcType=VARCHAR},
        </if>
        <if test="kdbh != null ">
            kdbh = #{kdbh,jdbcType=VARCHAR},
        </if>
        <if test="ccm != null ">
            ccm = #{ccm,jdbcType=VARCHAR},
        </if>
        <if test="ksjhbh != null ">
            ksjhbh = #{ksjhbh,jdbcType=VARCHAR},
        </if>
        <if test="kdmc != null ">
            kdmc = #{kdmc,jdbcType=VARCHAR},
        </if>
        <if test="kdjc != null ">
            kdjc = #{kdjc,jdbcType=VARCHAR},
        </if>
        <if test="sfbzhkd != null ">
            sfbzhkd = #{sfbzhkd,jdbcType=VARCHAR},
        </if>
        <if test="bzhkdid != null ">
            bzhkdid = #{bzhkdid,jdbcType=VARCHAR},
        </if>
        <if test="bzhkdmc != null ">
            bzhkdmc = #{bzhkdmc,jdbcType=VARCHAR},
        </if>
        <if test="kdlbm != null ">
            kdlbm = #{kdlbm,jdbcType=VARCHAR},
        </if>
        <if test="kdlb != null ">
            kdlb = #{kdlb,jdbcType=VARCHAR},
        </if>
        <if test="kdszsfm != null ">
            kdszsfm = #{kdszsfm,jdbcType=VARCHAR},
        </if>
        <if test="kdszsf != null ">
            kdszsf = #{kdszsf,jdbcType=VARCHAR},
        </if>
        <if test="kdszsqm != null ">
            kdszsqm = #{kdszsqm,jdbcType=VARCHAR},
        </if>
        <if test="kdszsq != null ">
            kdszsq = #{kdszsq,jdbcType=VARCHAR},
        </if>
        <if test="kdszqxm != null ">
            kdszqxm = #{kdszqxm,jdbcType=VARCHAR},
        </if>
        <if test="kdszqx != null ">
            kdszqx = #{kdszqx,jdbcType=VARCHAR},
        </if>
        <if test="kddz != null ">
            kddz = #{kddz,jdbcType=VARCHAR},
        </if>
        <if test="kdjd != null ">
            kdjd = #{kdjd,jdbcType=DECIMAL},
        </if>
        <if test="kdwd != null ">
            kdwd = #{kdwd,jdbcType=DECIMAL},
        </if>
        <if test="kdfzrxm != null ">
            kdfzrxm = #{kdfzrxm,jdbcType=VARCHAR},
        </if>
        <if test="kdfzrdh != null ">
            kdfzrdh = #{kdfzrdh,jdbcType=VARCHAR},
        </if>
        <if test="fwbgsfzrxm != null ">
            fwbgsfzrxm = #{fwbgsfzrxm,jdbcType=VARCHAR},
        </if>
        <if test="kwbgsdh != null ">
            kwbgsdh = #{kwbgsdh,jdbcType=VARCHAR},
        </if>
        <if test="jsfzrxm != null ">
            jsfzrxm = #{jsfzrxm,jdbcType=VARCHAR},
        </if>
        <if test="jsfzrdh != null ">
            jsfzrdh = #{jsfzrdh,jdbcType=VARCHAR},
        </if>
        <if test="kdxzqhm != null ">
            kdxzqhm = #{kdxzqhm,jdbcType=VARCHAR},
        </if>
        <if test="sczt != null ">
            sczt = #{sczt,jdbcType=VARCHAR},
        </if>
        <if test="createTime != null ">
            create_time = #{createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTime != null ">
            update_time = #{updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="sjksgljgid != null ">
            sjksgljgid = #{sjksgljgid,jdbcType=VARCHAR},
        </if>
        <if test="kds != null ">
            kds = #{kds,jdbcType=VARCHAR},
        </if>
        <if test="kcs != null ">
            kcs = #{kcs,jdbcType=VARCHAR},
        </if>
        <if test="kss != null ">
            kss = #{kss,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.znfx.model.domain.exchange.KdKdxx"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from kd_kdxx
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
