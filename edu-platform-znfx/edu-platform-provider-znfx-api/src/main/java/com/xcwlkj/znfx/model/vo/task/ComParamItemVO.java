/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.znfx.model.vo.task;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 分析模板参数列表vo
 * <AUTHOR>
 * @version $Id: ComParamItemVO.java, v 0.1 2021年08月23日 14时29分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ComParamItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 检测参数id */
    private String id;
    /** 检测参数名称 */
    private String name;
    /** 检测参数值 */
    private String value;
    /** 检测参数类型 */
    private String type;

}
