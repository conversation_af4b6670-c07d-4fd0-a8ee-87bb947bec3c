/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.znfx.model.dto.analysrs;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 备用考场检测列表dto
 * <AUTHOR>
 * @version $Id: BykcLbDTO.java, v 0.1 2021年08月23日 14时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class BykcLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /**  */
    @NotBlank(message = "不能为空")
    private String ksgljgid;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;
    /** 关联标识默认1, 0-直属；1-直属以及所有子节点的信息 */
    @NotBlank(message = "关联标识默认1, 0-直属；1-直属以及所有子节点的信息不能为空")
    private String relateFlag;
    /** 考试计划编号 */
    @NotBlank(message = "考试计划编号不能为空")
    private String ksjhbh;
    /** 场次码 */
    private String ccm;
    /** 可能性 */
    private String knx;

}
