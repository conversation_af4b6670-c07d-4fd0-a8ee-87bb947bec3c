package com.xcwlkj.znfx.model.dto.localdata;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
public class BmsmlDTO implements Serializable {
    /** 序列ID */
    private static final long serialVersionUID = 1L;
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    @NotNull(message = "页数不能为空")
    private Integer pageSize;
}
