/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.znfx.model.vo.task;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 启用任务(兼容原有对外接口)vo
 * <AUTHOR>
 * @version $Id: StartTaskVO.java, v 0.1 2021年08月23日 16时36分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class StartTaskVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 启用结果列表 */
    private List<TaskResultItemVO> taskResultList;

}
