package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * {
 *   "headers": {
 *     "useTask": true,
 *     "batchId": "2025-07-23 15:51:48",
 *     "productId": "CENCGW100_S",
 *     "deviceName": "郑州测试yn150.21",
 *     "taskId": "6b598550-f181-487a-b729-dd361e5b6d0d",
 *     "orgId": "1647477021411569664"
 *   },
 *   "functionId": "NOTIFY_XFYDZDGJ",
 *   "messageType": "INVOKE_FUNCTION",
 *   "inputs": [
 *     {
 *       "name": "Data",
 *       "value": {
 *         "versionOrder": "61",
 *         "sign": "812ff15dc4d620e02e5a5a031ac43c2d",
 *         "messageId": "2507231551481397607138632663040",
 *         "version": "1.8.9",
 *         "parameters": {
 *           "packageName": "com.hisome.gxicv"
 *         },
 *         "signMethod": "MD5",
 *         "url": "http://47.111.5.196:8811/remote/file/zjkszhpt/default/24101315535502060292755460124673.apk",
 *         "timestamp": 1753257108270
 *       }
 *     }
 *   ],
 *   "messageId": "1947927612782043136",
 *   "deviceId": "CENCGW800_S03MC11BDJ000099",
 *   "timestamp": 1753257108291
 * }
 */
@NoArgsConstructor
@Data
@JsonIgnoreProperties(ignoreUnknown=true)
public class FunctionInvokeAppGjjsVO {

    @JsonProperty("messageId")
    private String messageId;
    @JsonProperty("deviceId")
    private String deviceId;
    @JsonProperty("timestamp")
    private Long timestamp;
    @JsonProperty("functionId")
    private String functionId;
    @JsonProperty("inputs")
    private List<FunctionInvokeAppGjjsVO.InputsDTO> inputs;
    @JsonProperty("messageType")
    private String messageType;

    @NoArgsConstructor
    @Data
    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class InputsDTO {
        @JsonProperty("name")
        private String name;
        @JsonProperty("value")
        private FunctionInvokeAppGjjsVO.InputsDTO.ValueDTO value;

        @NoArgsConstructor
        @Data
        @JsonIgnoreProperties(ignoreUnknown=true)
        public static class ValueDTO {
            @JsonProperty("versionOrder")
            private String versionOrder;
            @JsonProperty("sign")
            private String sign;
            @JsonProperty("messageId")
            private String messageId;
            @JsonProperty("version")
            private String version;
            @JsonProperty("signMethod")
            private String signMethod;
            @JsonProperty("url")
            private String url;
            @JsonProperty("timestamp")
            private Long timestamp;
            @JsonProperty("parameters")
            private ParametersDTO parameters;
            @NoArgsConstructor
            @Data
            @JsonIgnoreProperties(ignoreUnknown=true)
            public static class ParametersDTO {
                @JsonProperty("packageName")
                private String packageName;
            }
        }
    }

    public AppGjjsVO formatToAppGjjsVO (){
        AppGjjsVO vo = new AppGjjsVO();
        if(CollectionUtils.isNotEmpty(this.getInputs())){
            InputsDTO inputsDTO = this.getInputs().get(0);
            if(inputsDTO.getValue()!=null){
                FunctionInvokeAppGjjsVO.InputsDTO.ValueDTO valueDTO= inputsDTO.getValue();
                vo.setSign(valueDTO.getSign());
                vo.setPackageName(valueDTO.getParameters().packageName);
                vo.setMessageId(valueDTO.getMessageId());
                vo.setSignMethod(valueDTO.getSignMethod());
                vo.setTimestamp(valueDTO.getTimestamp());
                vo.setVersion(valueDTO.getVersion());
                vo.setVersionOrder(valueDTO.getVersionOrder());
                vo.setUrl(valueDTO.getUrl());
            }
        }
        return vo;
    }
}
