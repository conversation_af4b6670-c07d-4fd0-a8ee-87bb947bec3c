package com.xcwlkj.identityverify.service.impl.scheduleTask;

import com.xcwlkj.identityverify.model.domain.KsKscc;
import com.xcwlkj.identityverify.model.domain.KsKsjh;
import com.xcwlkj.identityverify.model.dto.sjscgl.KszpscDTO;
import com.xcwlkj.identityverify.model.vo.cxtj.UploadValidateInfoResultVO;
import com.xcwlkj.identityverify.service.ITaskService;
import com.xcwlkj.identityverify.service.KsKsjhService;
import com.xcwlkj.identityverify.service.KsKsccService;
import com.xcwlkj.identityverify.service.KsKsrcxxService;
import com.xcwlkj.identityverify.third.superior.http.service.PlatformDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service("uploadValidatePicToHsTaskService")
@Slf4j
public class UploadValidatePicToHsTaskServiceImpl implements ITaskService {

    @Resource
    private KsKsjhService ksKsjhService;

    @Resource
    private KsKsccService ksKsccService;

    @Resource
    private KsKsrcxxService ksKsrcxxService;
    @Resource
    private PlatformDataService platformDataService;

    @Override
    public void run(String params) {
        log.info("开始执行考生照片上传定时任务");

        try {
            // 1. 获取所有当前进行中的考试计划
            List<KsKsjh> ongoingExamPlans = ksKsjhService.getOngoingExamPlans();

            if (CollectionUtils.isEmpty(ongoingExamPlans)) {
                log.info("当前没有进行中的考试计划");
                return;
            }

            log.info("找到 {} 个进行中的考试计划", ongoingExamPlans.size());

            // 2. 提取考试计划编号并获取考试场次并去重
            List<String> ksjhbhList = ongoingExamPlans.stream()
                    .map(KsKsjh::getKsjhbh)
                    .collect(Collectors.toList());

            List<KsKscc> examSessions = ksKsccService.getDistinctExamSessions(ksjhbhList);

            if (CollectionUtils.isEmpty(examSessions)) {
                log.info("当前没有考试场次");
                return;
            }

            log.info("找到 {} 个去重后的考试场次", examSessions.size());

            // 3. 对每个场次调用上传方法
            int successCount = 0;
            int failCount = 0;

            for (KsKscc examSession : examSessions) {
                try {
                    log.info("开始上传考试计划 {} 场次 {} 的考生照片", examSession.getKsjhbh(), examSession.getCcm());

//                    UploadValidateInfoResultVO result = ksKsrcxxService.uploadCandidatePhotosToProvince(
//                        examSession.getKsjhbh(), examSession.getCcm());
                    KszpscDTO kszpscDTO = new KszpscDTO();
                    kszpscDTO.setCcm(examSession.getCcm());
                    kszpscDTO.setKsjhbh(examSession.getKsjhbh());
                    platformDataService.kszpUpload(kszpscDTO);
//                    if (result != null) {
//                        log.info("考试计划 {} 场次 {} 照片上传完成：成功 {} 条，失败 {} 条，总计 {} 条",
//                            examSession.getKsjhbh(), examSession.getCcm(),
//                            result.getSuccessCount(), result.getFailCount(), result.getTotalCount());
                        successCount++;
//                    } else {
//                        log.warn("考试计划 {} 场次 {} 照片上传返回结果为空", examSession.getKsjhbh(), examSession.getCcm());
//                        failCount++;
//                    }

                } catch (Exception e) {
                    log.error("考试计划 {} 场次 {} 照片上传失败：{}",
                        examSession.getKsjhbh(), examSession.getCcm(), e.getMessage(), e);
                    failCount++;
                }
            }

            log.info("考生照片上传定时任务执行完成：成功处理 {} 个场次，失败 {} 个场次", successCount, failCount);

        } catch (Exception e) {
            log.error("考生照片上传定时任务执行异常：{}", e.getMessage(), e);
        }
    }
}
