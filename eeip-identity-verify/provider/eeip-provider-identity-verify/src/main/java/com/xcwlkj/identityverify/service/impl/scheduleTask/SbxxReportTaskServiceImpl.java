package com.xcwlkj.identityverify.service.impl.scheduleTask;

import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.dto.sbyw.SubDevInfoDTO;
import com.xcwlkj.identityverify.model.enums.JcsblxEnum;
import com.xcwlkj.identityverify.service.ITaskService;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.service.SbSbxxService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.HisomeParamEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 设备信息上报
 */
@Service("sbxxReportTaskService")
@Slf4j
public class SbxxReportTaskServiceImpl implements ITaskService {

    @Resource
    private SbSbxxService sbSbxxService;
    @Resource
    private JySysDictService jySysDictService;
    @Override
    public void run(String params) {
        SubDevInfoDTO subDevInfoDTO = new SubDevInfoDTO();
        List<String> subTypeList = new ArrayList<>();
//        HisomeParamEnum.SUB_DEV_TYPE
        JySysDict jySysDict = jySysDictService.queryConfigValueByKey("HISOME"+"_"+HisomeParamEnum.SUB_DEV_TYPE.getCode());
        if(jySysDict != null){
            String[] types = jySysDict.getTValue().split(",");
            subTypeList.addAll(Arrays.asList(types));
        }
        if(CollectionUtils.isEmpty(subTypeList)){
            // 没有配置则默认上报电子班牌
            subTypeList.add(JcsblxEnum.DZBP.getMsg());
        }
        List<String> sbxlhs = sbSbxxService.getUnSubSbxlhsByTypes(subTypeList);
        if(CollectionUtils.isEmpty(sbxlhs)){
            log.info("设备信息上报，没有需要上报的设备");
            return;
        }
        subDevInfoDTO.setSbxlhs(sbxlhs);
        log.info("设备信息上报，上报类型：{}，上报设备序列号：{}", subTypeList, sbxlhs);
        sbSbxxService.subDevInfo(subDevInfoDTO);
    }
}
