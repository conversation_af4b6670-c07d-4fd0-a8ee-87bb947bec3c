package com.xcwlkj.identityverify.service;

import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttFuncInvokeEnum;

public interface MqttUnifyAccessEventService {

    public void handleDevEvent(String productId, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr);

    public void handleFuncInvoke(String productId, String deviceId, MqttFuncInvokeEnum funcInvokeEnum, String payloadStr);
}
