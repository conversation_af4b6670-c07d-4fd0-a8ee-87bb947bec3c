package com.xcwlkj.identityverify.model.dos;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode
public class ProvinceExamSeqDo implements Serializable {
    /**
     * 考试计划编号
     */
    String ksjhbh;

    /**
     * 场次码
     */
    String ccm;

    /**
     * 场次名称
     */
    String ccmc;

    /**
     * 开始时间
     */
    Date kssj;

    /**
     * 结束时间
     */
    Date jssj;

    /**
     * 科目码
     */
    String kmm;

    /**
     * 科目名称
     */
    String kmmc;

    /**
     * 允许迟到时间
     */
    Integer yxcdsj;

    /**
     * 考试批次号
     */
    String kspch;

}
