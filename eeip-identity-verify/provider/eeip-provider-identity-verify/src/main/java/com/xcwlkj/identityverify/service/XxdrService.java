package com.xcwlkj.identityverify.service;

import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.identityverify.mapper.*;
import com.xcwlkj.identityverify.model.constant.EventPeriodKey;
import com.xcwlkj.identityverify.model.domain.*;
import com.xcwlkj.identityverify.model.dos.*;
import com.xcwlkj.identityverify.model.dto.ksgl.WjdrDTO;
import com.xcwlkj.identityverify.model.enums.*;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeVO;
import com.xcwlkj.identityverify.util.CompressUtil;
import com.xcwlkj.identityverify.util.HsCmdUtil;
import com.xcwlkj.identityverify.util.SqliteManager;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.common.DatePattern;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.net.URL;
import java.sql.Connection;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public abstract class XxdrService {

    @Resource
    protected KsKssjImportTaskMapper importTaskMapper;
    @Resource
    protected KsKssjImportFileMapper importFileMapper;
    @Resource
    protected KsKssjImportStatusMapper importStatusMapper;
    @Resource
    protected KsKssjImportExceptionMapper importExceptionMapper;
    @Resource
    protected KsProcessEventMapper ksProcessEventMapper;
    @Resource
    protected KsProcessPeriodMapper ksProcessPeriodMapper;
    @Resource
    protected CsXxjbxxMapper xxjbxxMapper;
    @Resource
    protected KsKsjhMapper ksKsjhMapper;
    @Resource
    protected KsKssjImportFileService ksKssjImportFileService;
    @Resource
    protected KsKssjDistributeStatusService ksKssjDistributeStatusService;
    @Resource
    protected CsJsjbxxService csJsjbxxService;
    @Resource
    protected KsJkryJbxxService jkryJbxxService;
    @Resource
    protected KsJkryBpxxService jkryBpxxService;
    @Resource
    protected KsJkryRcxxService jkryRcxxService;
    @Resource
    protected TransactionTemplate transactionTemplate;
    @Resource
    protected KsKssjPkgFileService ksKssjPkgFileService;
    @Resource
    protected KsKssjPkgStatusService ksKssjPkgStatusService;
    @Resource
    protected KsBmxxService ksBmxxService;
    @Resource
    protected KsBpxxService ksBpxxService;
    @Resource
    protected KsKsccService ksKsccService;
    @Resource
    protected KsKsrcxxService ksKsrcxxService;
    @Resource
    protected KsKsjhService ksKsjhService;
    @Resource
    protected KsKcxxService ksKcxxService;
    @Resource
    protected KsKdxxService ksKdxxService;
    @Resource
    protected KsBmxxKstzzService ksBmxxKstzzService;

    @Value("${xc.temp.path}")
    private String tempPath;
    @Value("${xc.identityVerify.wjsc.fileName.ksbmxx:ks.db}")
    private String ksbmxx;
    @Value("${xc.identityVerify.wjsc.fileName.jkryjbxx:jky.db}")
    private String jkryjbxx;
    @Value("${xc.identityVerify.wjsc.fileName.jkrybpxx:jkybp.db}")
    private String jkrybpxx;
    @Value("${xc.identityVerify.wjsc.fileName.sjjkzp:jkryzp.db}")
    private String jkzpxx;
    @Value("${xc.identityVerify.wjsc.fileName.sjkszp:kszp.db}")
    private String kszpxx;
    @Value("${xc.identityVerify.wjsc.fileName.sjpzxx:pzxx.db}")
    private String pzxx;
    @Value("${HsFaceFeature.HsFaceFeatureCreatorPath}")
    private String hsFaceFeatureCreatorPath;
    @Value("${HsFaceFeature.HsFaceFeatureFilePath}")
    private String hsFaceFeatureFilePath;
    protected String zpName = "photo"; //照⽚名称为考⽣⾝份证号.jpg
    private String bzhkdid;
    private String bzhkdmc;

    /**
     * 任务创建
     * @param ksjhbh 考试计划编号
     * @param importCatalog 数据来源
     * @param tConfValue 任务内容
     * @return 任务
     */
    public KsKssjImportTask importTaskInit(String ksjhbh, String importCatalog, Integer tConfValue){
        KsKssjImportTask importTask = new KsKssjImportTask();
        //查询当前任务是否已经存在
        Example taskEx = new Example(KsKssjImportTask.class);
        taskEx.createCriteria()
                .andEqualTo("ksjhbh",ksjhbh)
                .andEqualTo("importCatalog", importCatalog)
                .andEqualTo("tConf", tConfValue)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        List<KsKssjImportTask> importTaskList = importTaskMapper.selectByExample(taskEx);
        if (importTaskList == null || importTaskList.size() == 0) {
            //新任务
            importTask.setId(IdGenerateUtil.generateId());
            importTask.setKsjhbh(ksjhbh);
            importTask.setImportCatalog(importCatalog);
            importTask.setTConf(tConfValue);
            //获取当前时间
            Date date = new Date();
            importTask.setName(ksjhbh+"_"+DateUtil.format(date,"yyyyMMddhhmmss")+"_"+DateUtil.format(date, DatePattern.NORM_DATETIME_PATTERN));
            importTask.setTProgress("0");
            importTask.setComplete(KssjImportTaskCompleteEnum.START.getValue());
            importTask.setStartTime(date);
            importTask.setSczt(ScztEnum.NOTDEL.getCode());
            importTaskMapper.insert(importTask);
        }else {
            //旧任务改变完成状态
            importTask = importTaskList.get(0);
            importTask.setComplete(KssjImportTaskCompleteEnum.START.getValue());
            importTask.setTProgress("0");

            importTaskMapper.updateByPrimaryKey(importTask);
        }
        return importTask;
    }

    /**
     * 文件下载记录创建
     * @param initDO
     * @return 文件id
     */
    public KsKssjImportFile importFileInit(ImportFileInitDO initDO){

        String bzhkdid = initDO.getBzhkdid();
        String filePath = initDO.getFilePath();
        ImportTypeEnum importType = initDO.getImportType();
        String importCatalog = initDO.getImportCatalog();
        String bzhkdmc = initDO.getBzhkdmc();
        String ksjhbh = initDO.getKsjhbh();

        KsKssjImportFile importFile = new KsKssjImportFile();
        //文件上传记录是否存在
        Example fileEx = new Example(KsKssjImportFile.class);
        fileEx.createCriteria()
                .andEqualTo("ksjhbh",ksjhbh)
                .andEqualTo("importCatalog",importCatalog)
                .andEqualTo("importType", importType)
                .andEqualTo("sczt",ScztEnum.NOTDEL.getCode());
        List<KsKssjImportFile> importFileList = importFileMapper.selectByExample(fileEx);
        if (importFileList == null || importFileList.size() == 0){
            //新建记录
            importFile.setId(IdGenerateUtil.generateId());
            importFile.setImportType(importType.getCode());
            importFile.setKsjhbh(ksjhbh);
            importFile.setBzhkdid(bzhkdid);
            importFile.setBzhkdmc(bzhkdmc);
            importFile.setImportCatalog(importCatalog);
            importFile.setFilePath(filePath);
            importFile.setFileName(initDO.getFileName());
            importFile.setIsDownload(FileDownloadEnum.XZWC.getCode());
            Date date = new Date();
            importFile.setCreateTime(date);
            importFile.setUpdateTime(date);
            importFile.setSczt(ScztEnum.NOTDEL.getCode());
            importFile.setVersion("1");
            importFileMapper.insert(importFile);
        }else{
            importFile = importFileList.get(0);
            importFile.setIsDownload(FileDownloadEnum.XZWC.getCode());
            importFile.setFilePath(filePath);
            importFile.setFileName(initDO.getFileName());
            Date date = new Date();
            importFile.setUpdateTime(date);
            int version = Integer.parseInt(importFile.getVersion());
            version += 1;
            importFile.setVersion(String.valueOf(version));
            importFileMapper.updateByPrimaryKey(importFile);
        }
        return importFile;
    }

    /**
     * 获取文件导入状态或新建
     * @param ksjhbh 考试计划编号
     * @param importCatalog 数据来源
     * @param bzhkdid 标准化考点编号
     * @param bzhkdmc 标准化考点名称
     * @return 文件导入状态
     */
    public KsKssjImportStatus importStatusInit(String ksjhbh, String importCatalog, String bzhkdid, String bzhkdmc){
        KsKssjImportStatus importStatus = new KsKssjImportStatus();
        Example statusEx = new Example(KsKssjImportStatus.class);
        statusEx.createCriteria()
                .andEqualTo("ksjhbh",ksjhbh)
                .andEqualTo("importCatalog",importCatalog);
        List<KsKssjImportStatus> importStatusList = importStatusMapper.selectByExample(statusEx);
        if (importStatusList == null || importStatusList.size() == 0){
            // 新建导入
            importStatus.setId(IdGenerateUtil.generateId());
            importStatus.setImportCatalog(importCatalog);
            importStatus.setBzhkdid(bzhkdid);
            importStatus.setKsjhbh(ksjhbh);
            importStatus.setBzhkdmc(bzhkdmc);
            importStatus.setJkryjbsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            importStatus.setJkrybpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            importStatus.setPzsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            importStatus.setKszpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            importStatus.setKssjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            importStatus.setJkryzpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            Date date = new Date();
            importStatus.setLatestOperateTime(date);
            importStatus.setCreateTime(date);
            importStatus.setUpdateTime(date);
            importStatusMapper.insert(importStatus);
            // 删除过时文件信息
            Example fileExam = new Example(KsKssjImportFile.class);
            fileExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("importCatalog", importCatalog)
                    .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
            KsKssjImportFile importFile = new KsKssjImportFile();
            importFile.setSczt(ScztEnum.DEL.getCode());
            importFileMapper.updateByExampleSelective(importFile, fileExam);
        }else {
            importStatus = importStatusList.get(0);
            Date date = new Date();
            importStatus.setLatestOperateTime(date);
            importStatus.setUpdateTime(date);

            importStatusMapper.updateByPrimaryKey(importStatus);
        }

        return importStatus;
    }

    /**
     * 获取导入状态
     * @param ksjhbh
     * @param catalogEnum
     * @return
     */
    public KsKssjImportStatus getImportStatus(String ksjhbh, ImportCatalogEnum catalogEnum){
        KsKssjImportStatus result = new KsKssjImportStatus();
        Example example = new Example(KsKssjImportStatus.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("importCatalog", catalogEnum.getCode());
        List<KsKssjImportStatus> importStatuses = importStatusMapper.selectByExample(example);
        if (importStatuses != null && importStatuses.size() != 0){
            result = importStatuses.get(0);
        }
        return result;
    }

    public void importTaskUpdate(KsKssjImportTask task){
        importTaskMapper.updateByPrimaryKey(task);
    }

    public void importStatusUpdate(KsKssjImportStatus status){
        Date date = new Date();
        status.setUpdateTime(date);
        status.setLatestOperateTime(date);
        importStatusMapper.updateByPrimaryKey(status);
    }

    public int importFileComplete(String ksjhbh, List<String> typeList){
        return importFileMapper.completeBatch(ksjhbh, typeList);
    }

    public void fileInsertList(List<KsKssjImportFile> list){
        importFileMapper.insertListSelective(list);
    }

    public String getFileId(String ksjhbh, String type, String catalog){
        String fileId = null;

        Example example = new Example(KsKssjImportFile.class);
        example.createCriteria().andEqualTo("sczt",ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh",ksjhbh)
                .andEqualTo("import_catalog",catalog)
                .andEqualTo("import_type",type);
        List<KsKssjImportFile> importFileList = importFileMapper.selectByExample(example);
        if (importFileList != null && importFileList.size() != 0){
            KsKssjImportFile importFile = importFileList.get(0);
            fileId = importFile.getId();
        }
        return fileId;
    }

    public void kssjImportException(String ksjhbh, Exception exception, String taskid, int tConf){
        KsKssjImportException importException = new KsKssjImportException();
        importException.setId(IdGenerateUtil.generateId());
        importException.setKsjhbh(ksjhbh);
        importException.setCreateTime(DateUtil.getCurrentDT());
        importException.setUpdateTime(DateUtil.getCurrentDT());
        importException.setImportTaskId(taskid);
        importException.setTDesc(exception.getMessage());
        importException.setTMode(String.valueOf(tConf));
        importExceptionMapper.insert(importException);
    }

    public KsProcessEvent sjtbEventInit(String ksjhbh, DataSynchTypeEnum typeEnum){
        KsProcessEvent event;
        Example example = new Example(KsProcessEvent.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("eventPeriod", "SJTB")
                .andEqualTo("tbsjlx", typeEnum.getSjlxdm())
                .andEqualTo("eventCatalog", EventCatalogSjtbEnum.SJTB_RECORD.getCode())
                .andEqualTo("eventHandle", EventHandleEnum.DEFAULT.getCode())
                .andEqualTo("sczt",ScztEnum.NOTDEL.getCode());
        List<KsProcessEvent> events = ksProcessEventMapper.selectByExample(example);
        if (events != null && events.size() != 0){
            KsProcessEvent event1 = new KsProcessEvent();
            event1.setSczt(ScztEnum.DEL.getCode());
            event = events.get(0);
            event.setId(IdGenerateUtil.generateId());
            event.setUpdateTime(new Date());
            event.setEventType(null);
            event.setEventDescDetail(null);
            ksProcessEventMapper.updateByExampleSelective(event1, example);
            ksProcessEventMapper.insertSelective(event);
        }else {
            event = new KsProcessEvent();
            event.setId(IdGenerateUtil.generateId());
            event.setKsjhbh(ksjhbh);
            event.setEventPeriod("SJTB");
            event.setEventCatalog(EventCatalogSjtbEnum.SJTB_RECORD.getCode());
            event.setTbsjlx(typeEnum.getSjlxdm());
            event.setEventHandle(EventHandleEnum.DEFAULT.getCode());
            event.setParam1(DateUtil.format(new Date(),DateUtil.DEFAULT_DATE_TIME));
            event.setEventDesc(typeEnum.getTblxmc());
            event.setCreateTime(new Date());
            event.setUpdateTime(new Date());
            event.setSczt(ScztEnum.NOTDEL.getCode());
            ksProcessEventMapper.insert(event);
        }
        return event;
    }

    public KsProcessEvent getDefaultEvent(String ksjhbh, DataSynchTypeEnum synchTypeEnum){
        Example example = new Example(KsProcessEvent.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("eventPeriod", EventPeriodKey.SJTB)
                .andEqualTo("tbsjlx", synchTypeEnum.getSjlxdm())
                .andEqualTo("eventCatalog", EventCatalogSjtbEnum.SJTB_RECORD.getCode())
                .andEqualTo("eventHandle", EventHandleEnum.DEFAULT.getCode())
                .andIsNull("eventType")
                .andEqualTo("sczt",ScztEnum.NOTDEL.getCode());
        List<KsProcessEvent> events = ksProcessEventMapper.selectByExample(example);
        if (events != null && events.size() != 0){
            return events.get(0);
        }
        return null;
    }

    public void updateEvent(KsProcessEvent event){
        if (StringUtils.equals(event.getEventType(), EventTypeSjtbEnum.SUCCESS.getCode())) {
            Example example = new Example(KsProcessEvent.class);
            example.createCriteria().andEqualTo("ksjhbh", event.getKsjhbh())
                    .andNotEqualTo("eventType", EventTypeSjtbEnum.SUCCESS.getCode())
                    .andEqualTo("eventHandle", EventHandleEnum.UNTREATED.getCode())
                    .andEqualTo("tbsjlx", event.getTbsjlx())
                    .andEqualTo("eventCatalog", EventCatalogSjtbEnum.SJTB_EVENT.getCode())
                    .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
            List<KsProcessEvent> ksProcessEvents = ksProcessEventMapper.selectByExample(example);
            if (ksProcessEvents != null && ksProcessEvents.size() != 0) {
                for (KsProcessEvent ksProcessEvent : ksProcessEvents) {
                    ksProcessEvent.setEventHandle(EventHandleEnum.TREATED.getCode());
                    ksProcessEvent.setEventHandleTime(new Date());
                }
                ksProcessEventMapper.deleteByExample(example);
                ksProcessEventMapper.insertListSelective(ksProcessEvents);
            }
        }
        event.setUpdateTime(new Date());
        ksProcessEventMapper.updateByPrimaryKey(event);
    }

    public KsProcessPeriod sjtbPeriodInit(String ksjhbh){
        KsProcessPeriod period = new KsProcessPeriod();
        Example example = new Example(KsProcessPeriod.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("periodKey", EventPeriodKey.SJTB)
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        List<KsProcessPeriod> ksProcessPeriods = ksProcessPeriodMapper.selectByExample(example);
        if (ksProcessPeriods != null && ksProcessPeriods.size() != 0){
            period = ksProcessPeriods.get(0);
            period.setPeriodStartTime(new Date());
            period.setPeriodStatus(PeriodStatusEnum.INIT.getCode());
            period.setPeriodProgress("0/1");
            updatePeriod(period);
        }else {
            period.setId(IdGenerateUtil.generateId());
            period.setKsjhbh(ksjhbh);
            period.setPeriodKey(EventPeriodKey.SJTB);
            period.setPeriodStartTime(new Date());
            period.setPeriodStatus(PeriodStatusEnum.INIT.getCode());
            period.setPeriodProgress("0/1");
            period.setPeriodName("数据同步");
            period.setCreateTime(new Date());
            period.setSczt(ScztEnum.NOTDEL.getCode());
            ksProcessPeriodMapper.insert(period);
        }
        return period;
    }

    public void updatePeriod(KsProcessPeriod period){
        period.setUpdateTime(new Date());
        ksProcessPeriodMapper.updateByPrimaryKey(period);
    }

    public void tbsjDbPeriod(KsKssjPkgStatus pkgStatus, String ksjhbh){
        KsKsjh ksjh = ksKsjhMapper.selectByPrimaryKey(ksjhbh);
        List<Integer> sjbQkList = new ArrayList<>();
        sjbQkList.add(pkgStatus.getKssjbQk());
        sjbQkList.add(pkgStatus.getKszpsjbQk());
        sjbQkList.add(pkgStatus.getPzsjbQk());
        Integer kzqy = ksjh.getKzqy();

        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0){
            sjbQkList.add(pkgStatus.getJkrybpsjbQk());
        }
        if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0){
            sjbQkList.add(pkgStatus.getJkryjbsjbQk());
            sjbQkList.add(pkgStatus.getJkryzpsjbQk());
        }
        List<Integer> collect = sjbQkList.stream().filter(s -> SjbQkEnum.FINISH.getValue() == s).collect(Collectors.toList());
        String periodStatus = PeriodStatusEnum.FAIL.getCode();
        if (collect.size() == sjbQkList.size()) {
            periodStatus = PeriodStatusEnum.DONE.getCode();
        }
        double progress = collect.size() * 100 / sjbQkList.size();
        Example example = new Example(KsProcessPeriod.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andLike("periodKey", EventPeriodKey.SJDB + "%");
        List<KsProcessPeriod> ksProcessPeriods = ksProcessPeriodMapper.selectByExample(example);
        Date date = new Date();
        if (ksProcessPeriods.size() != 2){
            KsProcessPeriod period = new KsProcessPeriod(IdGenerateUtil.generateId(), ksjhbh, null, EventPeriodKey.SJDB, date, date, periodStatus, String.format(".2f",progress), null, "数据打包", date, date, ScztEnum.NOTDEL.getCode());
            KsProcessPeriod periodKd = new KsProcessPeriod(IdGenerateUtil.generateId(), ksjhbh, null, EventPeriodKey.SJDB_KD, date, date, periodStatus, String.format(".2f",progress), null, "数据打包", date, date, ScztEnum.NOTDEL.getCode());
            ksProcessPeriods.clear();
            ksProcessPeriods.add(period);
            ksProcessPeriods.add(periodKd);
            KsProcessPeriod delPeriod = new KsProcessPeriod();
            delPeriod.setSczt(ScztEnum.DEL.getCode());
            ksProcessPeriodMapper.updateByExampleSelective(delPeriod, example);
            ksProcessPeriodMapper.insertListSelective(ksProcessPeriods);
        }else {
            KsProcessPeriod period = new KsProcessPeriod();
            period.setPeriodEndTime(date);
            period.setUpdateTime(date);
            period.setPeriodProgress(String.format(".2f",progress));
            period.setPeriodStatus(periodStatus);
            ksProcessPeriodMapper.updateByExampleSelective(period, example);
        }


    }

    /**
     * 获取学校组织机构码
     * @return
     */
    public String getXxZzjgm(){
        List<CsXxjbxx> xxjbxxes = xxjbxxMapper.selectAll();
        return xxjbxxes.get(0).getZzjgm();
    }

    public List<KsKssjImportFile> getFileList(KsKssjImportStatus status){
        List<String> fileIdList = new ArrayList<>();
        if (SjbQkEnum.NOT_ENABLED.getValue() != status.getPzsjbQk())
            fileIdList.add(status.getFilePzsjbId());
        if (SjbQkEnum.NOT_ENABLED.getValue() != status.getJkryjbsjbQk())
            fileIdList.add(status.getFileJkryjbsjbId());
        if (SjbQkEnum.NOT_ENABLED.getValue() != status.getJkrybpsjbQk())
            fileIdList.add(status.getFileJkrybpsjbId());
        if (SjbQkEnum.NOT_ENABLED.getValue() != status.getJkryzpsjbQk())
            fileIdList.add(status.getFileJkryzpsjbId());
        if (SjbQkEnum.NOT_ENABLED.getValue() != status.getKssjbQk())
            fileIdList.add(status.getFileKssjbId());
        if (SjbQkEnum.NOT_ENABLED.getValue() != status.getKszpsjbQk())
            fileIdList.add(status.getFileKszpsjbId());
        if (fileIdList.size() != 0){
            Example example = new Example(KsKssjImportFile.class);
            example.createCriteria().andIn("id", fileIdList);
            List<KsKssjImportFile> importFiles = importFileMapper.selectByExample(example);
            return importFiles;
        }
        return null;
    }


    /**
     * 将导入任务和事件状态设为失败
     * @param ksKssjImportTask
     * @param ksProcessPeriod
     * @param ksProcessEvent
     * @param planCode
     * @param exception
     * @param platName
     */
    public void setImportTaskAndProcessFail(KsKssjImportTask ksKssjImportTask, KsProcessPeriod ksProcessPeriod, KsProcessEvent ksProcessEvent, String planCode, Exception exception, String platName) {
        ksKssjImportTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
        importTaskUpdate(ksKssjImportTask);

        kssjImportException(planCode, exception, ksKssjImportTask.getId(), KssjImportTaskTConfEnum.KS.getValue());

        ksProcessPeriod.setPeriodStatus(PeriodStatusEnum.FAIL.getCode());
        updatePeriod(ksProcessPeriod);

        ksProcessEvent.setEventType(EventTypeSjtbEnum.TBCW.getCode());
        ksProcessEvent.setEventHandle(EventHandleEnum.UNTREATED.getCode());
        ksProcessEvent.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
        ksProcessEvent.setEventDesc(platName + EventTypeSjtbEnum.TBCW.getCode());
        ksProcessEvent.setEventDescDetail(platName + EventTypeSjtbEnum.TBCW.getCode() + ":" + exception.getMessage());
       updateEvent(ksProcessEvent);
    }

    /**
     * 将导入任务和事件状态设为成功
     * @param ksKssjImportTask
     * @param ksProcessPeriod
     * @param ksProcessEvent
     * @param platName
     */
    public void setImportTaskAndProcessSuccess(KsKssjImportTask ksKssjImportTask, KsProcessPeriod ksProcessPeriod, KsProcessEvent ksProcessEvent, String platName) {
        ksKssjImportTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
        ksKssjImportTask.setTProgress("100");
        ksKssjImportTask.setCompleteTime(new Date());
        importTaskUpdate(ksKssjImportTask);

        ksProcessPeriod.setPeriodStatus(PeriodStatusEnum.DONE.getCode());
        ksProcessPeriod.setPeriodProgress("1/1");
        updatePeriod(ksProcessPeriod);

        ksProcessEvent.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
        ksProcessEvent.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
        ksProcessEvent.setEventDesc(platName + DataSynchTypeEnum.SJKSXX.getTblxmc() + "导入成功");
        ksProcessEvent.setEventDescDetail(platName + DataSynchTypeEnum.SJKSXX.getTblxmc() + "导入成功");
        updateEvent(ksProcessEvent);
    }


    public void importFileAndStatusInit(Map<String, String> fileMap, String ksjhbh, KsKdxx ksKdxx) {
        KsKssjImportStatus importStatus = importStatusInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(), ksKdxx.getBzhkdid(), ksKdxx.getBzhkdmc());

        for (Map.Entry<String, String> entry : fileMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();

            File file = new File(value);
            if (file.exists()) {
                // 导入文件初始化
                ImportFileInitDO fileInitDO = new ImportFileInitDO();
                fileInitDO.setKsjhbh(ksjhbh);
                fileInitDO.setFilePath(value);
                fileInitDO.setFileName(file.getName());
                fileInitDO.setImportType(ImportTypeEnum.get(key));
                fileInitDO.setImportCatalog(ImportCatalogEnum.SJPTDR.getCode());
                fileInitDO.setBzhkdid(ksKdxx.getBzhkdid());
                fileInitDO.setBzhkdmc(ksKdxx.getBzhkdmc());
                KsKssjImportFile importFile = importFileInit(fileInitDO);

                if (StringUtils.equals(ImportTypeEnum.KS.getCode(), key)) {
                    importStatus.setKssjbQk(SjbQkEnum.FINISH.getValue());
                    importStatus.setFileKssjbId(importFile.getId());
                } else if (StringUtils.equals(ImportTypeEnum.KSZP.getCode(), key)) {
                    importStatus.setKszpsjbQk(SjbQkEnum.FINISH.getValue());
                    importStatus.setFileKszpsjbId(importFile.getId());
                } else if (StringUtils.equals(ImportTypeEnum.JKRY.getCode(), key)) {
                    importStatus.setJkryjbsjbQk(SjbQkEnum.FINISH.getValue());
                    importStatus.setFileJkryjbsjbId(importFile.getId());
                }else if (StringUtils.equals(ImportTypeEnum.JKBP.getCode(), key)) {
                    importStatus.setJkrybpsjbQk(SjbQkEnum.FINISH.getValue());
                    importStatus.setFileJkrybpsjbId(importFile.getId());
                }else if (StringUtils.equals(ImportTypeEnum.JKZP.getCode(), key)) {
                    importStatus.setJkryzpsjbQk(SjbQkEnum.FINISH.getValue());
                    importStatus.setFileJkryzpsjbId(importFile.getId());
                }
                importStatusUpdate(importStatus);
            }
        }
    }

    protected void addFileImportList(String ksjhbh, FileListItemDO file, SjbVersionDO sjbVersionDO, List<KsKssjImportFile> importFileList, List<KsKssjImportFile> dbFileList,KsKssjImportStatus importStatus){
        KsProcessEvent event = new KsProcessEvent();
        KsKssjImportFile importFile = new KsKssjImportFile();
        Date date = new Date();
        String id = IdGenerateUtil.generateId();
        importFile.setId(id);
        importFile.setKsjhbh(ksjhbh);
        importFile.setSczt(ScztEnum.NOTDEL.getCode());
        importFile.setVersion(file.getVersion());
        importFile.setFileMd(file.getFileMd5());
        importFile.setImportCatalog(ImportCatalogEnum.SJPTDR.getCode());
        importFile.setCreateTime(date);
        importFile.setUpdateTime(date);

        // 编辑文件下载参数
        String fileUrl = file.getFileUrl();
        Boolean downloadFlag = true;

        String dir = DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT);
        String path = tempPath + dir + File.separator;
        String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1, fileUrl.lastIndexOf("?"));
        String newFilePath = path + fileName;

        importFile.setFilePath(newFilePath);
        importFile.setFileName(fileName);
        switch (ImportTypeEnum.getByType(file.getType())){
            case KS : {
                downloadFlag = getDownlaodFlag(dbFileList,file,ImportTypeEnum.KS);
                if (downloadFlag){
                    event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.SJKSXX);
                    importStatus.setFileKssjbId(id);
                    importStatus.setKssjbQk(SjbQkEnum.IN_PROGRESS.getValue());
                }
                importFile.setImportType(ImportTypeEnum.KS.getCode());
                sjbVersionDO.setStuinfVersion(Integer.parseInt(file.getVersion()));
                sjbVersionDO.setStuinfMd(file.getFileMd5());
                break;
            }
            case KSZP : {
                downloadFlag = getDownlaodFlag(dbFileList,file,ImportTypeEnum.KSZP);
                if (downloadFlag){
                    event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.SJKSZP);
                    importStatus.setFileKszpsjbId(id);
                    importStatus.setKszpsjbQk(SjbQkEnum.IN_PROGRESS.getValue());
                }
                importFile.setImportType(ImportTypeEnum.KSZP.getCode());
                sjbVersionDO.setStuzpVersion(Integer.parseInt(file.getVersion()));
                sjbVersionDO.setStuzpMd(file.getFileMd5());
                break;
            }
            case JKRY : {
                downloadFlag = getDownlaodFlag(dbFileList,file,ImportTypeEnum.JKRY);
                if (downloadFlag){
                    event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.SJJKXX);
                    importStatus.setFileJkryjbsjbId(id);
                    importStatus.setJkryjbsjbQk(SjbQkEnum.IN_PROGRESS.getValue());
                }
                importFile.setImportType(ImportTypeEnum.JKRY.getCode());
                sjbVersionDO.setJkryjbxxVersion(Integer.parseInt(file.getVersion()));
                sjbVersionDO.setJkryjbxxMd(file.getFileMd5());
                break;
            }
            case JKZP : {
                downloadFlag = getDownlaodFlag(dbFileList,file,ImportTypeEnum.JKZP);
                if (downloadFlag){
                    event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.SJJKZP);
                    importStatus.setFileJkryzpsjbId(id);
                    importStatus.setJkryzpsjbQk(SjbQkEnum.IN_PROGRESS.getValue());
                }
                importFile.setImportType(ImportTypeEnum.JKZP.getCode());
                sjbVersionDO.setJkryzpVersion(Integer.parseInt(file.getVersion()));
                sjbVersionDO.setJkryzpMd(file.getFileMd5());
                break;
            }
            case JKBP : {
                downloadFlag = getDownlaodFlag(dbFileList,file,ImportTypeEnum.JKBP);
                if (downloadFlag){
                    event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.SJJKBP);
                    importStatus.setFileJkrybpsjbId(id);
                    importStatus.setJkrybpsjbQk(SjbQkEnum.IN_PROGRESS.getValue());
                }
                importFile.setImportType(ImportTypeEnum.JKBP.getCode());
                sjbVersionDO.setJkrybpxxVersion(Integer.parseInt(file.getVersion()));
                sjbVersionDO.setJkrybpxxMd(file.getFileMd5());
                break;
            }
            case PZ : {
                downloadFlag = getDownlaodFlag(dbFileList,file,ImportTypeEnum.PZ);
                if (downloadFlag){
                    event = sjtbEventInit(ksjhbh, DataSynchTypeEnum.SJKSJHXX);
                    importStatus.setFilePzsjbId(id);
                    importStatus.setPzsjbQk(SjbQkEnum.IN_PROGRESS.getValue());
                }
                importFile.setImportType(ImportTypeEnum.PZ.getCode());
                sjbVersionDO.setCommonVersion(Integer.parseInt(file.getVersion()));
                sjbVersionDO.setCommonMd(file.getFileMd5());
                break;
            }
            default:
                break;
        }
        if (downloadFlag) {
            try {
                URL url = new URL(fileUrl);
                File newfile = new File(newFilePath);
                FileUtils.copyURLToFile(url, newfile);
                importFileList.add(importFile);
            } catch (Exception e) {
                e.printStackTrace();
                log.error(e.getMessage(), e);
                event.setEventType(EventTypeSjtbEnum.LQSB.getCode());
                event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                event.setEventDesc(EventTypeSjtbEnum.LQSB.getDesc());
                event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                event.setEventDescDetail(EventTypeSjtbEnum.LQSB.getDesc() + e.getMessage());
                event.setUpdateTime(new Date());
                updateEvent(event);
            }
        }
    }
    protected Boolean getDownlaodFlag(List<KsKssjImportFile> dbImportFileList, FileListItemDO file, ImportTypeEnum typeEnum){
        if (dbImportFileList != null && dbImportFileList.size() != 0) {
            List<KsKssjImportFile> ksFiles = dbImportFileList.stream().filter(_file ->
                    StringUtils.equals(_file.getImportType(), typeEnum.getCode())).collect(Collectors.toList());
            if (ksFiles != null && ksFiles.size() != 0) {
                KsKssjImportFile ksFile = ksFiles.get(0);
                if (StringUtils.equals(ksFile.getFileMd(),file.getFileMd5()) && ksFile.getVersion().equals(file.getVersion())){
//                    return false;
                }else {
                    ksFile.setSczt(ScztEnum.DEL.getCode());
                    ksKssjImportFileService.updateSingle(ksFile);
                }
            }
        }
        return true;
    }

    protected KsKssjPkgFile sjbCl(List<KsKssjImportFile> importFileList, ImportTypeEnum typeEnum, String key,
                               KsKssjPkgStatus pkgStatus,KsKssjImportStatus importStatus, SjbVersionDO sjbVersionDO) throws ZipException {
        List<KsKssjImportFile> importFiles = null;
        KsKssjImportFile importFile;
        if (importFileList != null && importFileList.size() != 0) {
            importFiles = importFileList.stream()
                    .filter(file -> StringUtils.equals(file.getImportType(), typeEnum.getCode())).collect(Collectors.toList());
        }
        if (importFiles == null || importFiles.size() == 0){
            String fileId = null;
            switch (typeEnum){
                case KS:
                    fileId = importStatus.getFileKssjbId();
                    break;
                case KSZP:
                    fileId = importStatus.getFileKszpsjbId();
                    break;
                case JKRY:
                    fileId = importStatus.getFileJkryjbsjbId();
                    break;
                case JKBP:
                    fileId = importStatus.getFileJkrybpsjbId();
                    break;
                case JKZP:
                    fileId = importStatus.getFileJkryzpsjbId();
                    break;
                case PZ:
                    fileId = importStatus.getFilePzsjbId();
                    break;
            }
            if (fileId != null) {
                importFile = ksKssjImportFileService.selectSingleByKey(fileId);
            }else {
                return null;
            }
        }else {
            importFile = importFiles.get(0);
        }
        WjdrDTO wjdrDTO = new WjdrDTO();
        KsKssjPkgFile pkgFile = new KsKssjPkgFile();
        String id = IdGenerateUtil.generateId();
        pkgFile.setId(id);

        // 解压
        String pzPath = importFile.getFilePath();
        UploadVO uploadVO = XcDfsClient.uploadStream(pzPath);
        File[] unzip = CompressUtil.unzip(pzPath, tempPath + "unZipDir", key);
        File parentFile = unzip[0].getParentFile();
        wjdrDTO.setKsjhbh(importFile.getKsjhbh());
        wjdrDTO.setFilePath(parentFile.getAbsolutePath());
        switch (typeEnum){
            case PZ:{
                if (SjbQkEnum.FINISH.getValue() != importStatus.getPzsjbQk()) {
                    pkgFile.setPkgType(PackEnum.Pack_GxHisomeCommon.getCode());
                    importPzxx(wjdrDTO, sjbVersionDO);
                    importStatus.setPzsjbQk(SjbQkEnum.FINISH.getValue());
                    pkgStatus.setFilePzsjbId(id);
                    pkgStatus.setPzsjbQk(SjbQkEnum.FINISH.getValue());
                }else {
                    return null;
                }
                break;
            }
            case KS:{
                if (SjbQkEnum.FINISH.getValue() != importStatus.getKssjbQk()){
                    pkgFile.setPkgType(PackEnum.Pack_GxHisomeStu.getCode());
                    importKsxx(wjdrDTO);
                    importStatus.setKssjbQk(SjbQkEnum.FINISH.getValue());
                    pkgStatus.setFileKssjbId(id);
                    pkgStatus.setKssjbQk(SjbQkEnum.FINISH.getValue());
                }else {
                    return null;
                }
                break;
            }
            case KSZP:{
                if (SjbQkEnum.FINISH.getValue() != importStatus.getKszpsjbQk()) {
                    pkgFile.setPkgType(PackEnum.Pack_GxHisomeKdKsZp.getCode());
                    importKszp(wjdrDTO);
                    importStatus.setKszpsjbQk(SjbQkEnum.FINISH.getValue());
                    pkgStatus.setFileKszpsjbId(id);
                    pkgStatus.setKszpsjbQk(SjbQkEnum.FINISH.getValue());
                }else {
                    return null;
                }
                break;
            }
            case JKRY: {
                if (SjbQkEnum.FINISH.getValue() != importStatus.getJkryjbsjbQk()){
                    pkgFile.setPkgType(PackEnum.Pack_GxHisomeJkry.getCode());
                    importJkry(wjdrDTO);
                    importStatus.setJkryjbsjbQk(SjbQkEnum.FINISH.getValue());
                    pkgStatus.setFileJkryjbsjbId(id);
                    pkgStatus.setJkryjbsjbQk(SjbQkEnum.FINISH.getValue());
                }else {
                    return null;
                }
                break;
            }
            case JKBP:{
                if (SjbQkEnum.FINISH.getValue() != importStatus.getJkrybpsjbQk()) {
                    pkgFile.setPkgType(PackEnum.Pack_GxHisomeJkryBp.getCode());
                    importJkrybp(wjdrDTO);
                    importStatus.setJkrybpsjbQk(SjbQkEnum.FINISH.getValue());
                    pkgStatus.setFileJkrybpsjbId(id);
                    pkgStatus.setJkrybpsjbQk(SjbQkEnum.FINISH.getValue());
                }else {
                    return null;
                }
                break;
            }
            case JKZP:{
                if (SjbQkEnum.FINISH.getValue() != importStatus.getJkryzpsjbQk()) {
                    pkgFile.setPkgType(PackEnum.Pack_GxHisomeKdJkryZp.getCode());
                    importJkryzp(wjdrDTO);
                    importStatus.setJkryzpsjbQk(SjbQkEnum.FINISH.getValue());
                    pkgStatus.setFileJkryzpsjbId(id);
                    pkgStatus.setJkryzpsjbQk(SjbQkEnum.FINISH.getValue());
                }else {
                    return null;
                }
                break;
            }
        }
        for (File _file : unzip) {
            _file.delete();
        }
        parentFile.delete();
        Example example = new Example(KsKssjPkgFile.class);
        example.createCriteria().andEqualTo("ksjhbh", importFile.getKsjhbh())
                .andEqualTo("pkgType", pkgFile.getPkgType())
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        List<KsKssjPkgFile> pkgFileList = ksKssjPkgFileService.selectListByExample(example);
        if (pkgFileList != null && pkgFileList.size() != 0){
            KsKssjPkgFile ksKssjPkgFile = pkgFileList.get(0);
            ksKssjPkgFile.setSczt(ScztEnum.DEL.getCode());
            ksKssjPkgFileService.updateSingle(ksKssjPkgFile);
        }
        pkgFile.setPkgCatalog("kd");
        pkgFile.setFileMd(importFile.getFileMd());
        pkgFile.setFilePath(importFile.getFilePath());
        pkgFile.setSczt(ScztEnum.NOTDEL.getCode());
        pkgFile.setDfsFileObjUri(uploadVO.getList().get(0).getId());
        pkgFile.setDfsFilePathUri(uploadVO.getList().get(0).getFilePath());
        pkgFile.setKsjhbh(importFile.getKsjhbh());
        pkgFile.setVersion(importFile.getVersion());
        pkgFile.setFileName(importFile.getFileName());
        pkgFile.setCreateTime(new Date());
        pkgFile.setUpdateTime(new Date());

        return pkgFile;
    }

    protected void importKsxx(WjdrDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String path = dto.getFilePath() + File.separator + ksbmxx;
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                log.info("==============开始导入考生信息======导入时间:" + DateUtil.getCurrentDateTime());

                // 任务记录初始化
                KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(),
                        KssjImportTaskTConfEnum.KS.getValue());

                // 获取初始打包事件
                KsProcessEvent event = getDefaultEvent(ksjhbh, DataSynchTypeEnum.SJKSXX);
                event.setParam3(importTask.getId());
                // 上传特征值
                boolean tzzFlag = false;
                Map<String, String> tzzNameAndIdMap = new HashMap<>();
                File folder = new File(path);
                File parentFile = folder.getParentFile();
                File[] files = parentFile.listFiles();
                for (File file : files) {
                    if (file.isFile() && file.getName().lastIndexOf(".fac") >= 0) {
                        log.info("=======特征值开始上传：{}", file.getAbsolutePath());
                        UploadVO uploadVO = XcDfsClient.uploadStream(file.getAbsolutePath());
                        tzzNameAndIdMap.put(file.getName(), uploadVO.getList().get(0).getId());
                        if (!tzzFlag) {
                            tzzFlag = true;
                        }
                    }
                }
                String np  = parentFile.getParentFile().getAbsolutePath();
                Connection connection = SqliteManager.dbConnInit(np+File.separator+ksbmxx);
                try {
                    // 任务开始
                    importTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
                    importTaskUpdate(importTask);
                    Map<String, KsxxDO> ksxxDOMap = new HashMap<>();
                    // 查询ksjbxx
                    List<KsStuBasic4PkgDO> ksjbxxList = SqliteManager.queryList(connection, "", KsStuBasic4PkgDO.class);
                    for (KsStuBasic4PkgDO ksjbxx : ksjbxxList) {
                        if (tzzFlag) {
                            ksjbxx.setTzz(tzzNameAndIdMap.get(ksjbxx.getTzz()));
                        }
                        KsxxDO ksxxDO = new KsxxDO();
                        BeanUtils.copyProperties(ksjbxx, ksxxDO);
                        ksxxDOMap.put(ksjbxx.getZjhm(), ksxxDO);

                        if (ksxxDOMap.size() >= 1000){
                            // 查询ksbpxx并进行入库
                            ksxxRc(ksxxDOMap, connection, ksjhbh, tzzFlag);
                            ksxxDOMap.clear();
                        }
                    }

                    if (ksxxDOMap.size() > 0){
                        ksxxRc(ksxxDOMap, connection, ksjhbh, tzzFlag);
                    }
                    if (tzzFlag) {
                        KsKsjh ksjh = ksKsjhService.selectSingleByKey(ksjhbh);
                        int kzqy = ksjh.getKzqy() + KzqyEnum.TZZQY.getValue();
                        ksjh.setKzqy(kzqy);
                        ksKsjhService.updateSingle(ksjh);
                    }
                    event.setEventDesc(DataSynchTypeEnum.SJKSXX.getTblxmc() + "同步成功");
                    event.setEventDescDetail(DataSynchTypeEnum.SJKSXX.getTblxmc() + "同步成功");
                    event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                    event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                    event.setEventHandleTime(new Date());

                    importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                    importTask.setCompleteTime(DateUtil.getCurrentDT());
                    importTask.setTProgress("100");

                    log.info("==============考生信息导入完成======完成时间:" + DateUtil.getCurrentDateTime());

                } catch (Exception e) {
                    importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                    kssjImportException(ksjhbh, e, importTask.getId(), importTask.getTConf());
                    log.error("错误消息:{}", e.getMessage(), e);
                    e.printStackTrace();
                    event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                    event.setEventDesc("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc());
                    event.setEventDescDetail("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                    event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                    event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                }finally {
                    SqliteManager.closeConnection(connection);
                    event.setBzhkdid(bzhkdid);
                    event.setBzhkdmc(bzhkdmc);
                    updateEvent(event);
                    importTaskUpdate(importTask);
                }
            }
        });
    }

    protected void ksxxRc(Map<String, KsxxDO> ksxxDOMap, Connection connection, String ksjhbh, boolean tzzFlag) {
        List<KsBmxx> ksBmxxList = new ArrayList<>();
        List<KsBpxx> ksBpxxList = new ArrayList<>();
        List<KsKsrcxx> ksrcxxList = new ArrayList<>();
        List<String> kshs = new ArrayList<>();
        List<KsBmxxKstzz> kstzzList = new ArrayList<>();

        //拼接查询sql
        StringBuilder sqlBuilder = new StringBuilder();
        if (!ksxxDOMap.isEmpty()) {
            sqlBuilder.append(" AND zjhm IN (");
            Iterator<String> iterator = ksxxDOMap.keySet().iterator();
            while (iterator.hasNext()) {
                sqlBuilder.append("'").append(iterator.next()).append("'");
                if (iterator.hasNext()) {
                    sqlBuilder.append(",");
                }
            }
            sqlBuilder.append(")");
        }
        String sql = sqlBuilder.toString();
        List<KsStuBp4PkgDO> ksbpxxList = SqliteManager.queryList(connection, sql, KsStuBp4PkgDO.class);
        for (KsStuBp4PkgDO ksbpxx : ksbpxxList) {
            KsxxDO _ksxxDO = ksxxDOMap.get(ksbpxx.getZjhm());
            BeanUtils.copyProperties(ksbpxx, _ksxxDO);
        }

        // 考生信息入库
        for (KsxxDO value : ksxxDOMap.values()) {
            // 考生报名信息表
            KsBmxx ksBmxx = new KsBmxx();
            // 考生编号
            ksBmxx.setKsid(IdGenerateUtil.generateId());
            ksBmxx.setKsh(value.getZjhm());
            kshs.add(value.getZjhm());
            // 身份证件
            ksBmxx.setSfzjhm(value.getZjhm());
            ksBmxx.setSfzjlx("居民身份证");
            ksBmxx.setSfzjlxm("1");
            // 考生姓名
            ksBmxx.setXm(value.getKsxm());
            // 性别
            ksBmxx.setXb(value.getXb());
            ksBmxx.setXbm(StringUtils.equals(value.getXb(),"男")? "1" : "2");
            // 照片
            ksBmxx.setZp(value.getZp());
            // 考试计划编号
            ksBmxx.setKsjhid(ksjhbh);
            ksBmxx.setScztw(ScztEnum.NOTDEL.getCode());
            Date date = new Date();
            ksBmxx.setCreateTime(date);
            ksBmxx.setUpdateTime(date);
            ksBmxxList.add(ksBmxx);
            if (tzzFlag) {
                // 考生特征值
                KsBmxxKstzz kstzz = new KsBmxxKstzz();
                kstzz.setKszpid(value.getZp());
                kstzz.setKsh(value.getZjhm());
                kstzz.setSfzjhm(value.getZjhm());
                kstzz.setKsid(ksBmxx.getKsid());
                kstzz.setKsjhbh(ksjhbh);
                kstzz.setTzzfs("2");
                kstzz.setTzzzt("1");
                kstzz.setTzzid(value.getTzz());
                kstzz.setCreateTime(date);
                kstzz.setUpdateTime(date);
                kstzzList.add(kstzz);
            }
        }
        for (KsStuBp4PkgDO entity : ksbpxxList) {
            // 考生编排信息表
            KsBpxx ksBpxx = new KsBpxx();
            Date date = new Date();
            // 编排信息唯一编号
            ksBpxx.setKsbpbh(IdGenerateUtil.generateId());
            // 场次码
            ksBpxx.setCcm(entity.getCcbh());
            // 准考证号
            ksBpxx.setZkzh(entity.getZkzh());
            ksBpxx.setKsh(entity.getZjhm());
            // 身份证件
            ksBpxx.setSfzjhm(entity.getZjhm());
            // 考场号
            ksBpxx.setKcbh(entity.getKch());
            // 考点编号
            ksBpxx.setKdbh(entity.getKdbh());
            // 座位号
            ksBpxx.setZwh(entity.getZwh());
            ksBpxx.setZwhPx(Integer.valueOf(entity.getZwh()));
            // 科目码
            ksBpxx.setKmm(entity.getKmm());
            // 科目名称
            ksBpxx.setKmmc(entity.getKmmc());
            // 检录开始时间
            ksBpxx.setJlkssj(StringUtils.isNotBlank(entity.getJlkssj())? DateUtil.parse(entity.getJlkssj()) : null);
            // 检录结束时间
            ksBpxx.setJljssj(StringUtils.isNotBlank(entity.getJljssj())? DateUtil.parse(entity.getJljssj()) : null);
            // 考试开始时间
            ksBpxx.setKskssj(StringUtils.isNotBlank(entity.getKskssj())? DateUtil.parse(entity.getKskssj()) : null);
            // 考试结束时间
            ksBpxx.setKsjssj(StringUtils.isNotBlank(entity.getKsjssj())? DateUtil.parse(entity.getKsjssj()) : null);
            // 标准化考场标志号
            ksBpxx.setBzhkcbid(entity.getBzhkcid());
            // 逻辑考场编号
            ksBpxx.setLjkcbh(entity.getLjkcbh());
            // 考试计划编号
            ksBpxx.setKsjhbh(ksjhbh);
            ksBpxx.setScztw(ScztEnum.NOTDEL.getCode());
            ksBpxx.setCreateTime(date);
            ksBpxx.setUpdateTime(date);
            ksBpxxList.add(ksBpxx);

            // 考生入场信息表
            KsKsrcxx ksrcxx = new KsKsrcxx();
            ksrcxx.setKsrcbh(IdGenerateUtil.generateId());
            ksrcxx.setCcm(entity.getCcbh());
            ksrcxx.setZkzh(entity.getZkzh());
            ksrcxx.setKsh(entity.getZjhm());
            ksrcxx.setSfzh(entity.getZjhm());
            ksrcxx.setKcbh(entity.getKch());
            ksrcxx.setKdbh(entity.getKdbh());
            ksrcxx.setZwh(entity.getZwh());
            ksrcxx.setZwhPx(Integer.valueOf(entity.getZwh()));
            ksrcxx.setKmdm(entity.getKmm());
            ksrcxx.setKmmc(entity.getKmmc());
            ksrcxx.setBzhkcid(entity.getBzhkcid());
            ksrcxx.setLjkcbh(entity.getLjkcbh());
            ksrcxx.setKsxm(ksxxDOMap.get(entity.getZjhm()).getKsxm());
            ksrcxx.setSfrc("9");
            ksrcxx.setKsjhbh(ksjhbh);
            ksrcxx.setScztw(ScztEnum.NOTDEL.getCode());
            ksrcxx.setCreateTime(date);
            ksrcxx.setUpdateTime(date);
            ksrcxxList.add(ksrcxx);
        }
        Example bmExam = new Example(KsBmxx.class);
        Example.Criteria bmCriteria = bmExam.createCriteria();
        bmCriteria.andEqualTo("ksjhid", ksjhbh);
        if( CollectionUtils.isNotEmpty(kshs)){
            bmCriteria.andIn("ksh", kshs);
        }
        ksBmxxService.deleteByExample(bmExam);
        ksBmxxService.insertListSelective(ksBmxxList);

        if (tzzFlag) {
            Example tzzExam = new Example(KsBmxxKstzz.class);
            tzzExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                    .andIn("ksh", kshs);
            ksBmxxKstzzService.deleteByExample(tzzExam);
            ksBmxxKstzzService.insertListSelective(kstzzList);
        }
        //获取考点信息及考场名称
        List<String> kdbhList = ksrcxxList.stream().map(KsKsrcxx::getKdbh).collect(Collectors.toList());
        Example kdxxExam = new Example(KsKdxx.class);
        kdxxExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andIn("kdbh", kdbhList);
        kdxxExam.selectProperties("kdmc","kdbh","bzhkdid");
        kdxxExam.setDistinct(true);
        List<KsKdxx> kdxxList = ksKdxxService.selectListByExample(kdxxExam);

        List<String> kcbhList = ksrcxxList.stream().map(KsKsrcxx::getKcbh).collect(Collectors.toList());
        Example kcxxExam = new Example(KsKcxx.class);
        kcxxExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andIn("kcbh", kcbhList);
        kcxxExam.selectProperties("kcmc","kcbh");
        kcxxExam.setDistinct(true);
        List<KsKcxx> kcxxList = ksKcxxService.selectListByExample(kcxxExam);

        Map<String, KsKdxx> kdbhAndKdxxMap = kdxxList.stream().collect(Collectors.toMap(KsKdxx::getKdbh, Function.identity()));
        Map<String, String> kcbhAndKcmcMap = kcxxList.stream().collect(Collectors.toMap(KsKcxx::getKcbh, KsKcxx::getKcmc));
        for (KsKsrcxx ksKsrcxx : ksrcxxList) {
            KsKdxx ksKdxx = kdbhAndKdxxMap.get(ksKsrcxx.getKdbh());
            ksKsrcxx.setKdmc(ksKdxx.getKdmc());
            ksKsrcxx.setBzhkdid(ksKdxx.getBzhkdid());
            ksKsrcxx.setKcmc(kcbhAndKcmcMap.get(ksKsrcxx.getKcbh()));
            bzhkdid = ksKdxx.getBzhkdid();
            bzhkdmc = ksKdxx.getKdmc();
        }

        for (KsBpxx bpxx : ksBpxxList) {
            bpxx.setBzhkdbid(kdbhAndKdxxMap.get(bpxx.getKdbh()).getBzhkdid());
        }

        Example bpExam = new Example(KsBpxx.class);
        Example.Criteria bpCriteria = bpExam.createCriteria();
        bpCriteria.andEqualTo("ksjhbh", ksjhbh);
        bpCriteria.andIn("ksh", kshs);
        ksBpxxService.deleteByExample(bpExam);
        ksBpxxService.insertListSelective(ksBpxxList);

        Example rcExam = new Example(KsKsrcxx.class);
        Example.Criteria rcCriteria = rcExam.createCriteria();
        rcCriteria.andEqualTo("ksjhbh", ksjhbh);
        rcCriteria.andIn("sfzh", kshs);
        ksKsrcxxService.deleteByExample(rcExam);
        ksKsrcxxService.insertListSelective(ksrcxxList);

    }

    protected void importKszp(WjdrDTO dto){
        String ksjhbh = dto.getKsjhbh();
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                log.info("==============开始导入考生照片信息======导入时间:" + DateUtil.getCurrentDateTime());

                // 任务记录初始化
                KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(),
                        KssjImportTaskTConfEnum.KSZP.getValue());
                KsProcessEvent event = getDefaultEvent(ksjhbh, DataSynchTypeEnum.SJKSZP);
                event.setParam3(importTask.getId());
                Connection connection = null;
                try {
                    // 任务开始
                    importTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
                    importTaskUpdate(importTask);
                    String zpPath = dto.getFilePath();
                    // 上传照片
                    Map<String, String> zpNameAndIdMap = new HashMap<>();
                    File folder = new File(zpPath);
                    String path = folder.getParent() + File.separator + kszpxx;
                    connection = SqliteManager.dbConnInit(path);
                    File[] files = folder.listFiles();
                    for (File file : files) {
                        if (file.isFile() && file.getName().lastIndexOf(".jpg") >= 0) {
                            log.info("=======照片开始上传：" + zpPath + File.separator + file.getName());
                            UploadVO uploadVO = XcDfsClient.uploadStream(zpPath + File.separator + file.getName());
                            zpNameAndIdMap.put(file.getName(), uploadVO.getList().get(0).getId());
                        }
                    }
                    Map<String, String> zjhmAndZpMap = new HashMap<>();
                    // 查询ksjbxx（考生照片）
                    List<Kszpxx4PkgDO> kszpxxList = SqliteManager.queryList(connection, "", Kszpxx4PkgDO.class);
                    for (Kszpxx4PkgDO kszpxx : kszpxxList) {
                        String zpid = zpNameAndIdMap.get(kszpxx.getZp());
                        zjhmAndZpMap.put(kszpxx.getZjhm(), zpid);

                        // 入库
                        if (zjhmAndZpMap.size() >= 1000){
                            Example example = new Example(KsBmxx.class);
                            example.createCriteria().andEqualTo("ksjhid", ksjhbh)
                                    .andIn("sfzjhm", zjhmAndZpMap.keySet());
                            List<KsBmxx> ksBmxxList = ksBmxxService.selectListByExample(example);

                            Example tzzExam = new Example(KsBmxxKstzz.class);
                            tzzExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                                    .andIn("sfzjhm", zjhmAndZpMap.keySet());
                            List<KsBmxxKstzz> kstzzList = ksBmxxKstzzService.selectListByExample(tzzExam);
                            Map<String, KsBmxxKstzz> sfzhTzzMap = kstzzList.stream().collect(Collectors.toMap(KsBmxxKstzz::getSfzjhm, Function.identity()));
                            if (ksBmxxList != null && ksBmxxList.size() != 0){
                                for (KsBmxx ksBmxx : ksBmxxList) {
                                    String nzp = zjhmAndZpMap.get(ksBmxx.getSfzjhm());
                                    KsBmxxKstzz kstzz = sfzhTzzMap.get(ksBmxx.getSfzjhm());
                                    kstzz.setKszpid(nzp);
                                    ksBmxx.setZp(nzp);
                                }
                            }
                            ksBmxxService.deleteByExample(example);
                            ksBmxxService.insertListSelective(ksBmxxList);

                            ksBmxxKstzzService.deleteByExample(tzzExam);
                            ksBmxxKstzzService.insertListSelective(new ArrayList<>(sfzhTzzMap.values()));
                            zjhmAndZpMap.clear();
                        }
                    }
                    if (zjhmAndZpMap.size() > 0){
                        Example example = new Example(KsBmxx.class);
                        example.createCriteria().andEqualTo("ksjhid", ksjhbh)
                                .andIn("sfzjhm", zjhmAndZpMap.keySet());
                        List<KsBmxx> ksBmxxList = ksBmxxService.selectListByExample(example);

                        Example tzzExam = new Example(KsBmxxKstzz.class);
                        tzzExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                                .andIn("sfzjhm", zjhmAndZpMap.keySet());
                        List<KsBmxxKstzz> kstzzList = ksBmxxKstzzService.selectListByExample(tzzExam);
                        Map<String, KsBmxxKstzz> sfzhTzzMap = kstzzList.stream().collect(Collectors.toMap(KsBmxxKstzz::getSfzjhm, Function.identity()));
                        if (ksBmxxList != null && ksBmxxList.size() != 0){
                            for (KsBmxx ksBmxx : ksBmxxList) {
                                String nzp = zjhmAndZpMap.get(ksBmxx.getSfzjhm());
                                KsBmxxKstzz kstzz = sfzhTzzMap.get(ksBmxx.getSfzjhm());
                                kstzz.setKszpid(nzp);
                                ksBmxx.setZp(nzp);
                            }
                        }
                        ksBmxxService.deleteByExample(example);
                        ksBmxxService.insertListSelective(ksBmxxList);

                        ksBmxxKstzzService.deleteByExample(tzzExam);
                        ksBmxxKstzzService.insertListSelective(new ArrayList<>(sfzhTzzMap.values()));
                    }
                    event.setEventDesc(DataSynchTypeEnum.SJKSZP.getTblxmc() + "同步成功");
                    event.setEventDescDetail(DataSynchTypeEnum.SJKSZP.getTblxmc() + "同步成功");
                    event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                    event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                    event.setEventHandleTime(new Date());

                    importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                    importTask.setCompleteTime(DateUtil.getCurrentDT());
                    importTask.setTProgress("100");

                    log.info("==============考生照片信息导入完成======完成时间:" + DateUtil.getCurrentDateTime());
                }catch (Exception e) {
                    importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                    kssjImportException(ksjhbh, e, importTask.getId(), importTask.getTConf());
                    log.error("错误消息:{}", e.getMessage(), e);
                    e.printStackTrace();
                    event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                    event.setEventDesc("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc());
                    event.setEventDescDetail("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                    event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                    event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                } finally {
                    if (connection != null)
                        SqliteManager.closeConnection(connection);
                    event.setBzhkdid(bzhkdid);
                    event.setBzhkdmc(bzhkdmc);
                    updateEvent(event);
                    importTaskUpdate(importTask);
                }
            }
        });

    }

    protected void importJkry(WjdrDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String path = dto.getFilePath() + File.separator + jkryjbxx;
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                log.info("==============开始导入监考信息======导入时间:" + DateUtil.getCurrentDateTime());
                // 任务记录初始化
                KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(),
                        KssjImportTaskTConfEnum.JKRYJB.getValue());
                KsKdxx kdxx = new KsKdxx();
                Example kdExam = new Example(KsKdxx.class);
                kdExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                        .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                List<KsKdxx> kdxxList = ksKdxxService.selectListByExample(kdExam);
                if (kdxxList != null && kdxxList.size() != 0){
                    kdxx = kdxxList.get(0);
                }
                // 获取初始打包事件
                KsProcessEvent event = getDefaultEvent(ksjhbh, DataSynchTypeEnum.SJJKXX);
                event.setBzhkdid(kdxx.getBzhkdid());
                event.setBzhkdmc(kdxx.getBzhkdmc());
                event.setParam3(importTask.getId());
                Connection connection = SqliteManager.dbConnInit(path);
                try {
                    // 任务开始
                    importTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
                    importTaskUpdate(importTask);

                    List<KsJkryJbxx> jkryJbxxList = new ArrayList<>();
                    List<String> grbsms = new ArrayList<>();

                    List<KsJkryBasic4PkgDO> jbxxList = SqliteManager.queryList(connection, "", KsJkryBasic4PkgDO.class);
                    for (KsJkryBasic4PkgDO jbxx : jbxxList) {
                        KsJkryJbxx jkryJbxx = new KsJkryJbxx();
                        Date date = new Date();
                        jkryJbxx.setJkrybzid(IdGenerateUtil.generateId());
                        jkryJbxx.setKsjhdm(ksjhbh);
                        jkryJbxx.setGrbsm(jbxx.getGrbsm());
                        grbsms.add(jbxx.getGrbsm());
                        jkryJbxx.setGwlxm(jbxx.getGwlxm());
                        jkryJbxx.setGwmc(jbxx.getGwmc());
                        jkryJbxx.setXm(jbxx.getXm());
                        jkryJbxx.setXbm(XbEnum.getKey(jbxx.getXb()));
                        jkryJbxx.setXb(jbxx.getXb());
                        jkryJbxx.setZp(jbxx.getZp());
                        jkryJbxx.setZjlxm("1");
                        jkryJbxx.setZjlx("身份证");
                        jkryJbxx.setZjh(jbxx.getZjhm());
                        jkryJbxx.setCreateTime(date);
                        jkryJbxx.setUpdateTime(date);
                        jkryJbxx.setKddm(kdxx.getKdbh());
                        jkryJbxxList.add(jkryJbxx);

                        if (jkryJbxxList.size() >= 1000){
                            Example example = new Example(KsJkryJbxx.class);
                            example.createCriteria()
                                    .andEqualTo("ksjhdm", ksjhbh)
                                    .andIn("grbsm", grbsms);
                            jkryJbxxService.deleteByExample(example);
                            jkryJbxxService.insertListSelective(jkryJbxxList);

                            jkryJbxxList.clear();
                            grbsms.clear();
                        }
                    }
                    if (jkryJbxxList.size() > 0){
                        Example example = new Example(KsJkryJbxx.class);
                        example.createCriteria()
                                .andIn("grbsm", grbsms);
                        jkryJbxxService.deleteByExample(example);
                        jkryJbxxService.insertListSelective(jkryJbxxList);
                    }

                    event.setEventDesc(DataSynchTypeEnum.SJJKXX.getTblxmc() + "同步成功");
                    event.setEventDescDetail(DataSynchTypeEnum.SJJKXX.getTblxmc() + "同步成功");
                    event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                    event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                    event.setEventHandleTime(new Date());

                    importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                    importTask.setCompleteTime(DateUtil.getCurrentDT());
                    importTask.setTProgress("100");
                    log.info("==============监考信息导入完成======完成时间:" + DateUtil.getCurrentDateTime());
                }catch (Exception e){
                    importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                    kssjImportException(ksjhbh, e, importTask.getId(), importTask.getTConf());
                    log.error("错误消息:{}", e.getMessage(), e);
                    e.printStackTrace();
                    event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                    event.setEventDesc("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc());
                    event.setEventDescDetail("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                    event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                    event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                } finally {
                    SqliteManager.closeConnection(connection);
                    updateEvent(event);
                    importTaskUpdate(importTask);
                }
            }
        });
    }

    protected void importJkrybp(WjdrDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        String path = dto.getFilePath() + File.separator + jkrybpxx;
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                log.info("==============开始导入监考人员编排信息======导入时间:" + DateUtil.getCurrentDateTime());
                // 任务记录初始化
                KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(),
                        KssjImportTaskTConfEnum.JKRYBP.getValue());
                KsKdxx kdxx = new KsKdxx();
                Example kdExam = new Example(KsKdxx.class);
                kdExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                        .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                List<KsKdxx> kdxxList = ksKdxxService.selectListByExample(kdExam);
                if (kdxxList != null && kdxxList.size() != 0){
                    kdxx = kdxxList.get(0);
                }
                // 获取初始打包事件
                KsProcessEvent event = getDefaultEvent(ksjhbh, DataSynchTypeEnum.SJJKBP);
                event.setBzhkdid(kdxx.getBzhkdid());
                event.setBzhkdmc(kdxx.getBzhkdmc());
                event.setParam3(importTask.getId());
                Connection connection = SqliteManager.dbConnInit(path);
                try {
                    // 任务开始
                    importTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
                    importTaskUpdate(importTask);

                    List<KsJkryBpxx> jkryBpxxList = new ArrayList<>();
                    List<KsJkryRcxx> jkryRcxxList = new ArrayList<>();
                    List<String> grbsms = new ArrayList<>();
                    Example exampleJb = new Example(KsJkryJbxx.class);
                    exampleJb.createCriteria().andEqualTo("ksjhdm", ksjhbh);
                    List<KsJkryJbxx> jbxxList = jkryJbxxService.selectListByExample(exampleJb);
                    Map<String, KsJkryJbxx> grbsmAndJkryJbxx = jbxxList.stream().collect(Collectors.toMap(KsJkryJbxx::getGrbsm, each -> each, (value1, value2) -> value1));

                    List<KsJkryBp4PkgDO> bpxxList = SqliteManager.queryList(connection, "", KsJkryBp4PkgDO.class);
                    for (KsJkryBp4PkgDO bpxx : bpxxList) {
                        KsJkryBpxx jkryBpxx = new KsJkryBpxx();
                        Date date = new Date();
                        jkryBpxx.setJkrybpbzid(IdGenerateUtil.generateId());
                        jkryBpxx.setKsjhdm(ksjhbh);
                        jkryBpxx.setGrbsm(bpxx.getGrbsm());
                        jkryBpxx.setGwzzm(bpxx.getGwzzm());
                        jkryBpxx.setKdbh(kdxx.getKdbh());
                        jkryBpxx.setCcm(bpxx.getCcbh());
                        jkryBpxx.setKcbh(bpxx.getKch());
                        jkryBpxx.setBzhkcid(bpxx.getBzhkcid());
                        jkryBpxx.setBzhkdid(kdxx.getBzhkdid());
                        jkryBpxx.setCreateTime(date);
                        jkryBpxx.setUpdateTime(date);
                        jkryBpxxList.add(jkryBpxx);
                        grbsms.add(bpxx.getGrbsm());

                        KsJkryJbxx jbxx = grbsmAndJkryJbxx.get(bpxx.getGrbsm());
                        KsJkryRcxx jkryRcxx = new KsJkryRcxx();
                        jkryRcxx.setJkryrcid(IdGenerateUtil.generateId());
                        jkryRcxx.setKsjhdm(ksjhbh);
                        jkryRcxx.setGrbsm(jbxx.getGrbsm());
                        jkryRcxx.setGwzzm(bpxx.getGwzzm());
                        jkryRcxx.setKdbh(kdxx.getKdbh());
                        jkryRcxx.setBzhkdid(kdxx.getBzhkdid());
                        jkryRcxx.setCreateTime(date);
                        jkryRcxx.setUpdateTime(date);
                        jkryRcxx.setZjh(jbxx.getZjh());
                        jkryRcxx.setXm(jbxx.getXm());
                        jkryRcxx.setXb(jbxx.getXb());
                        jkryRcxx.setSfrc("9");
                        jkryRcxx.setRgkwsbdjg("0");
                        jkryRcxx.setSfkwsbd("9");
                        jkryRcxx.setCcm(bpxx.getCcbh());
                        jkryRcxx.setKcbh(bpxx.getKch());
                        jkryRcxx.setBzhkcid(bpxx.getBzhkcid());
                        jkryRcxxList.add(jkryRcxx);

                        if (jkryBpxxList.size() >= 1000){
                            Example example = new Example(KsJkryBpxx.class);
                            example.createCriteria()
                                    .andEqualTo("ksjhdm", ksjhbh)
                                    .andIn("grbsm", grbsms);
                            jkryBpxxService.deleteByExample(example);
                            jkryBpxxService.insertListSelective(jkryBpxxList);

                            Example exampleRc = new Example(KsJkryRcxx.class);
                            exampleRc.createCriteria()
                                    .andEqualTo("ksjhdm", ksjhbh)
                                    .andIn("grbsm", grbsms);
                            jkryRcxxService.deleteByExample(exampleRc);
                            jkryRcxxService.insertListSelective(jkryRcxxList);

                            jkryBpxxList.clear();
                            jkryRcxxList.clear();
                            grbsms.clear();
                        }
                    }
                    if (jkryBpxxList.size() > 0){
                        Example example = new Example(KsJkryBpxx.class);
                        example.createCriteria()
                                .andEqualTo("ksjhdm", ksjhbh)
                                .andIn("grbsm", grbsms);
                        jkryBpxxService.deleteByExample(example);
                        jkryBpxxService.insertListSelective(jkryBpxxList);

                        Example exampleRc = new Example(KsJkryRcxx.class);
                        exampleRc.createCriteria()
                                .andEqualTo("ksjhdm", ksjhbh)
                                .andIn("grbsm", grbsms);
                        jkryRcxxService.deleteByExample(exampleRc);
                        jkryRcxxService.insertListSelective(jkryRcxxList);
                    }

                    event.setEventDesc(DataSynchTypeEnum.SJJKBP.getTblxmc() + "同步成功");
                    event.setEventDescDetail(DataSynchTypeEnum.SJJKBP.getTblxmc() + "同步成功");
                    event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                    event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                    event.setEventHandleTime(new Date());

                    importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                    importTask.setCompleteTime(DateUtil.getCurrentDT());
                    importTask.setTProgress("100");
                    log.info("==============监考人员编排信息导入完成======完成时间:" + DateUtil.getCurrentDateTime());
                }catch (Exception e){
                    importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                    kssjImportException(ksjhbh, e, importTask.getId(), importTask.getTConf());
                    log.error("错误消息:{}", e.getMessage(), e);
                    e.printStackTrace();
                    event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                    event.setEventDesc("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc());
                    event.setEventDescDetail("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                    event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                    event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                } finally {
                    SqliteManager.closeConnection(connection);
                    updateEvent(event);
                    importTaskUpdate(importTask);
                }
            }
        });
    }

    protected void importJkryzp(WjdrDTO dto) {
        String ksjhbh = dto.getKsjhbh();
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                log.info("==============开始导入监考照片信息======导入时间:" + DateUtil.getCurrentDateTime());

                // 任务记录初始化
                KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(),
                        KssjImportTaskTConfEnum.JKRYZP.getValue());

                KsKdxx kdxx = new KsKdxx();
                Example kdExam = new Example(KsKdxx.class);
                kdExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                        .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                List<KsKdxx> kdxxList = ksKdxxService.selectListByExample(kdExam);
                if (kdxxList != null && kdxxList.size() != 0){
                    kdxx = kdxxList.get(0);
                }
                KsProcessEvent event = getDefaultEvent(ksjhbh, DataSynchTypeEnum.SJJKZP);
                event.setParam3(importTask.getId());
                event.setBzhkdid(kdxx.getBzhkdid());
                event.setBzhkdmc(kdxx.getBzhkdmc());
                Connection connection = null;
                try {
                    // 任务开始
                    importTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
                    importTaskUpdate(importTask);
                    String zpPath = dto.getFilePath();
                    // 上传照片
                    Map<String, String> zpNameAndIdMap = new HashMap<>();
                    File folder = new File(zpPath);
                    String path = folder + File.separator + jkzpxx;
                    connection = SqliteManager.dbConnInit(path);
                    File[] files = folder.listFiles();
                    for (File file : files) {
                        if(file.getName().lastIndexOf(".") < 0){
                            File[] zpFiles = file.listFiles();
                            for (File zpFile : zpFiles) {
                                if (zpFile.isFile() && zpFile.getName().lastIndexOf(".jpg") >= 0) {
                                    log.info("=======照片开始上传：" + zpFile.getAbsolutePath());
                                    UploadVO uploadVO = XcDfsClient.uploadStream(zpFile.getAbsolutePath());
                                    zpNameAndIdMap.put(zpFile.getName(), uploadVO.getList().get(0).getId());
                                }
                            }
                        }

                    }
                    Map<String, String> grbsmAndZpMap = new HashMap<>();
                    // 查询ksjbxx（考生照片）
                    List<Jkryzpxx4PkgDO> jkzpxxList = SqliteManager.queryList(connection, "", Jkryzpxx4PkgDO.class);
                    for (Jkryzpxx4PkgDO jkzpxx : jkzpxxList) {
                        String zpid = zpNameAndIdMap.get(jkzpxx.getZp());
                        grbsmAndZpMap.put(jkzpxx.getGrbsm(), zpid);

                        // 入库
                        if (grbsmAndZpMap.size() >= 1000){
                            Example example = new Example(KsJkryJbxx.class);
                            example.createCriteria().andEqualTo("ksjhdm", ksjhbh)
                                    .andIn("grbsm", grbsmAndZpMap.keySet());
                            List<KsJkryJbxx> jkryJbxxList = jkryJbxxService.selectListByExample(example);
                            if (jkryJbxxList != null && jkryJbxxList.size() != 0){
                                for (KsJkryJbxx jkryJbxx : jkryJbxxList) {
                                    String nzp = grbsmAndZpMap.get(jkryJbxx.getGrbsm());
                                    jkryJbxx.setZp(nzp);
                                }
                            }
                            jkryJbxxService.deleteByExample(example);
                            jkryJbxxService.insertListSelective(jkryJbxxList);

                            grbsmAndZpMap.clear();
                        }
                    }
                    if (grbsmAndZpMap.size() > 0){
                        Example example = new Example(KsJkryJbxx.class);
                        example.createCriteria().andEqualTo("ksjhdm", ksjhbh)
                                .andIn("grbsm", grbsmAndZpMap.keySet());
                        List<KsJkryJbxx> jkryJbxxList = jkryJbxxService.selectListByExample(example);
                        if (jkryJbxxList != null && jkryJbxxList.size() != 0){
                            for (KsJkryJbxx jkryJbxx : jkryJbxxList) {
                                String nzp = grbsmAndZpMap.get(jkryJbxx.getGrbsm());
                                jkryJbxx.setZp(nzp);
                            }
                        }
                        jkryJbxxService.deleteByExample(example);
                        jkryJbxxService.insertListSelective(jkryJbxxList);
                    }
                    event.setEventDesc(DataSynchTypeEnum.SJJKZP.getTblxmc() + "同步成功");
                    event.setEventDescDetail(DataSynchTypeEnum.SJJKZP.getTblxmc() + "同步成功");
                    event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                    event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                    event.setEventHandleTime(new Date());

                    importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                    importTask.setCompleteTime(DateUtil.getCurrentDT());
                    importTask.setTProgress("100");

                    log.info("==============监考照片信息导入完成======完成时间:" + DateUtil.getCurrentDateTime());
                }catch (Exception e) {
                    importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                    kssjImportException(ksjhbh, e, importTask.getId(), importTask.getTConf());
                    log.error("错误消息:{}", e.getMessage(), e);
                    e.printStackTrace();
                    event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                    event.setEventDesc("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc());
                    event.setEventDescDetail("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                    event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                    event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                } finally {
                    if (connection != null)
                        SqliteManager.closeConnection(connection);
                    updateEvent(event);
                    importTaskUpdate(importTask);
                }

            }
        });
    }

    protected void importPzxx(WjdrDTO dto, SjbVersionDO sjbVersionDO){
        String ksjhbh = dto.getKsjhbh();
        String path = dto.getFilePath() + File.separator + pzxx;
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(TransactionStatus status) {
                log.info("==============开始导入配置信息======导入时间:" + DateUtil.getCurrentDateTime());
                // 任务记录初始化
                KsKssjImportTask importTask = importTaskInit(ksjhbh, ImportCatalogEnum.SJPTDR.getCode(),
                        KssjImportTaskTConfEnum.PZXX.getValue());
                // 获取初始打包事件
                KsProcessEvent event = getDefaultEvent(ksjhbh, DataSynchTypeEnum.SJKSJHXX);
                event.setParam3(importTask.getId());
                Connection connection = SqliteManager.dbConnInit(path);
                try {
                    // 任务开始
                    importTask.setComplete(KssjImportTaskCompleteEnum.RUNNING.getValue());
                    importTaskUpdate(importTask);

                    List<KsKcxx> kcxxList = new ArrayList<>();
                    List<String> bzhkcids = new ArrayList<>();
                    Map<String, KsKssjDistributeStatus> distributeStatusMap = new HashMap<>();

                    // 考试计划
                    List<KsKsjh> ksjhList = new ArrayList<>();
                    List<String> ksjhbhs = new ArrayList<>();
                    List<KsKsjh4PkgDO> _ksjhList = SqliteManager.queryList(connection, "", KsKsjh4PkgDO.class);
                    for (KsKsjh4PkgDO _ksjh : _ksjhList) {
                        KsKsjh ksKsjh = new KsKsjh();

                        ksKsjh.setKsjhbh(_ksjh.getKsjhbh());
                        ksjhbhs.add(_ksjh.getKsjhbh());
                        // 考试计划名称
                        ksKsjh.setMc(_ksjh.getKsjhmc());
                        // 开始时间
                        ksKsjh.setKssj(DateUtil.parse(_ksjh.getKssj()));
                        // 结束时间
                        ksKsjh.setJssj(DateUtil.parse(_ksjh.getJssj()));
                        // 上报加密
                        ksKsjh.setSbjm(_ksjh.getSbjm());
                        // 设备开启时间
                        ksKsjh.setSbkqsj(Integer.valueOf(_ksjh.getSbkqsj()));
                        // 设备关闭时间
                        ksKsjh.setSbgbsj(Integer.valueOf(_ksjh.getSbgbsj()));
                        // jhms
                        // 场所设备编号
                        // 场所设备密钥
                        ksKsjh.setKszt("20");
                        ksKsjh.setDbms(_ksjh.getDbms());
                        // 启用设备白名单
                        if (StringUtils.isNotBlank(_ksjh.getQysbbmd())) {
                            ksKsjh.setQysbbmd(Integer.valueOf(_ksjh.getQysbbmd()));
                        } else {
                            ksKsjh.setQysbbmd(0);
                        }
                        // wifi启用
                        if (_ksjh.getWifiqy() != null) {
                            ksKsjh.setWifiqy(_ksjh.getWifiqy());
                        } else {
                            ksKsjh.setWifiqy(0);
                        }
                        Date date = new Date();
                        ksKsjh.setCjsj(date);
                        ksKsjh.setXgsj(date);
                        int kzqy = 0;
                        if (sjbVersionDO.getJkryjbxxMd() != null){
                            kzqy += KzqyEnum.JKRYQY.getValue();
                        }
                        if (sjbVersionDO.getJkrybpxxMd() != null){
                            kzqy += KzqyEnum.JKRYBPQY.getValue();
                        }
                        if (_ksjh.getJkqdqy() == 1){
                            kzqy += KzqyEnum.JKRYQDQY.getValue();
                        }
                        if (_ksjh.getKszpscqy() == 1){
                            kzqy += KzqyEnum.KSZPSCQY.getValue();
                        }
                        if (sjbVersionDO.getStuzpMd() != null) {
                            kzqy += KzqyEnum.KSZPQY.getValue();
                        }

                        ksKsjh.setKzqy(kzqy);

                        ksKsjh.setScztw(ScztEnum.NOTDEL.getCode());
                        ksjhList.add(ksKsjh);

                        if (ksjhList.size() >= 1000){
                            Example example = new Example(KsKsjh.class);
                            example.createCriteria()
                                    .andIn("ksjhbh", ksjhbhs);
                            ksKsjhService.deleteByExample(example);
                            ksKsjhService.insertListSelective(ksjhList);

                            ksjhList.clear();
                            ksjhbhs.clear();
                        }
                    }
                    if (ksjhList.size() > 0){
                        Example example = new Example(KsKsjh.class);
                        example.createCriteria()
                                .andIn("ksjhbh", ksjhbhs);
                        ksKsjhService.deleteByExample(example);
                        ksKsjhService.insertListSelective(ksjhList);
                    }

                    // 考场信息
                    List<Kcxx4PkgDO> _kcxxList = SqliteManager.queryList(connection, "", Kcxx4PkgDO.class);
                    for (Kcxx4PkgDO kcxx : _kcxxList) {
                        KsKcxx ksKcxx = new KsKcxx();
                        KsKssjDistributeStatus distributeStatus = new KsKssjDistributeStatus();

                        distributeStatus.setId(IdGenerateUtil.generateId());
                        ksKcxx.setId(IdGenerateUtil.generateId());
                        // 场次码
                        ksKcxx.setCcm(kcxx.getCcbh());
                        // 考场号
                        ksKcxx.setKcbh(kcxx.getKch());
                        distributeStatus.setKcbh(kcxx.getKch());
                        // 考点编号
                        ksKcxx.setKdbh(kcxx.getKdbh());
                        // 标准化考场id
                        ksKcxx.setBzhkcid(kcxx.getBzhkcid());
                        distributeStatus.setBzhkcbh(kcxx.getBzhkcid());
                        bzhkcids.add(kcxx.getBzhkcid());
                        // 物理位置
                        ksKcxx.setKcdz(kcxx.getWlwz());
                        // 逻辑考场名称
                        ksKcxx.setKcmc(kcxx.getLjkcmc());
                        // 逻辑考场编号
                        ksKcxx.setLjkcbh(kcxx.getLjkcbh());
                        distributeStatus.setLjkcbh(kcxx.getLjkcbh());
                        //
                        ksKcxx.setSfwbykc(kcxx.getSfwbykc());
                        distributeStatus.setSfwbykc(kcxx.getSfwbykc());
                        // 座次起始位置
                        ksKcxx.setZcqswz(kcxx.getZcqswz());
                        // 座次起始位置码
                        ksKcxx.setZcqswzm(kcxx.getZcqswzm());
                        // 座位布局方式
                        ksKcxx.setZwbjfs(kcxx.getZwbjfs());
                        // 座位布局方式码
                        ksKcxx.setZwbjfsm(kcxx.getZwbjfsm());
                        // 座位排列方式
                        ksKcxx.setZwplfs(kcxx.getZwplfs());
                        // 座位排列方式码
                        ksKcxx.setZwplfsm(kcxx.getZwplfsm());
                        ksKcxx.setSczt(ScztEnum.NOTDEL.getCode());

                        distributeStatus.setStuinfVersion(sjbVersionDO.getStuinfVersion());
                        distributeStatus.setCommonVersion(sjbVersionDO.getCommonVersion());
                        distributeStatus.setJkryjbxxVersion(sjbVersionDO.getJkryjbxxVersion());
                        distributeStatus.setJkrybpxxVersion(sjbVersionDO.getJkrybpxxVersion());
                        distributeStatus.setStuzpVersion(sjbVersionDO.getStuzpVersion());
                        distributeStatus.setJkryzpVersion(sjbVersionDO.getJkryzpVersion());

                        distributeStatus.setStuinfPkgStatus(DistributeStatusEnum.WAIT.getCode());
                        distributeStatus.setCommonPkgStatus(DistributeStatusEnum.WAIT.getCode());
                        distributeStatus.setJkryjbxxPkgStatus(DistributeStatusEnum.WAIT.getCode());
                        distributeStatus.setJkrybpxxPkgStatus(DistributeStatusEnum.WAIT.getCode());
                        distributeStatus.setStuzpPkgStatus(DistributeStatusEnum.WAIT.getCode());
                        distributeStatus.setJkryzpPkgStatus(DistributeStatusEnum.WAIT.getCode());
                        Date date = new Date();
                        ksKcxx.setCreateTime(date);
                        ksKcxx.setUpdateTime(date);
                        distributeStatus.setTDownloadTime(date);
                        distributeStatus.setTNoticeTime(date);

                        distributeStatus.setKsjhbh(ksjhbh);
                        ksKcxx.setKsjhbh(ksjhbh);
                        kcxxList.add(ksKcxx);
                        distributeStatusMap.put(distributeStatus.getBzhkcbh(), distributeStatus);

                        if (kcxxList.size() >= 1000){
                            //获取教室名称
                            Example jsExam = new Example(CsJsjbxx.class);
                            jsExam.createCriteria().andIn("bzhkcid", bzhkcids);
                            jsExam.selectProperties("bzhkcid", "jsmc");
                            List<CsJsjbxx> csJsjbxxes = csJsjbxxService.selectListByExample(jsExam);
                            Map<String, String> bzhkcidAndJsmcMap = csJsjbxxes.stream().collect(Collectors.toMap(CsJsjbxx::getBzhkcid, CsJsjbxx::getJsmc));

                            for (KsKcxx entity : kcxxList) {
                                entity.setBzhkcmc(bzhkcidAndJsmcMap.get(entity.getBzhkcid()));
                            }
                            Example example = new Example(KsKcxx.class);
                            example.createCriteria()
                                    .andEqualTo("ksjhbh", ksjhbh)
                                    .andIn("bzhkcid", bzhkcids);
                            ksKcxxService.deleteByExample(example);
                            ksKcxxService.insertListSelective(kcxxList);

                            for (Map.Entry<String, KsKssjDistributeStatus> entry : distributeStatusMap.entrySet()) {
                                entry.getValue().setBzhkcmc(bzhkcidAndJsmcMap.get(entry.getKey()));
                            }
                            Example disExample = new Example(KsKssjDistributeStatus.class);
                            disExample.createCriteria()
                                    .andEqualTo("ksjhbh", ksjhbh)
                                    .andIn("bzhkcbh", bzhkcids);
                            ksKssjDistributeStatusService.deleteByExample(disExample);
                            ksKssjDistributeStatusService.insertListSelective(new ArrayList<>(distributeStatusMap.values()));

                            kcxxList.clear();
                            distributeStatusMap.clear();
                            bzhkcids.clear();

                        }
                    }
                    if (kcxxList.size() > 0){
                        Example jsExam = new Example(CsJsjbxx.class);
                        jsExam.createCriteria().andIn("bzhkcid", bzhkcids);
                        jsExam.selectProperties("bzhkcid", "jsmc");
                        List<CsJsjbxx> csJsjbxxes = csJsjbxxService.selectListByExample(jsExam);
                        Map<String, String> bzhkcidAndJsmcMap = csJsjbxxes.stream().collect(Collectors.toMap(CsJsjbxx::getBzhkcid, CsJsjbxx::getJsmc));

                        for (KsKcxx entity : kcxxList) {
                            entity.setBzhkcmc(bzhkcidAndJsmcMap.get(entity.getBzhkcid()));
                        }
                        Example example = new Example(KsKcxx.class);
                        example.createCriteria()
                                .andEqualTo("ksjhbh", ksjhbh)
                                .andIn("bzhkcid", bzhkcids);
                        ksKcxxService.deleteByExample(example);
                        ksKcxxService.insertListSelective(kcxxList);

                        for (Map.Entry<String, KsKssjDistributeStatus> entry : distributeStatusMap.entrySet()) {
                            entry.getValue().setBzhkcmc(bzhkcidAndJsmcMap.get(entry.getKey()));
                        }
                        Example disExample = new Example(KsKssjDistributeStatus.class);
                        disExample.createCriteria()
                                .andEqualTo("ksjhbh", ksjhbh)
                                .andIn("bzhkcbh", bzhkcids);
                        ksKssjDistributeStatusService.deleteByExample(disExample);
                        ksKssjDistributeStatusService.insertListSelective(new ArrayList<>(distributeStatusMap.values()));
                    }

                    // 考点信息
                    List<KsKdxx> kdxxList = new ArrayList<>();
                    List<String> bzhkdbhs = new ArrayList<>();
                    List<KsKdxx4PkgDO> _kdxxList = SqliteManager.queryList(connection, "", KsKdxx4PkgDO.class);
                    for (KsKdxx4PkgDO _kdxx : _kdxxList) {
                        KsKdxx kdxx = new KsKdxx();

                        kdxx.setId(IdGenerateUtil.generateId());
                        // 考点编号
                        kdxx.setKdbh(_kdxx.getKdbh());
                        // 考区编号
                        kdxx.setKqbh(_kdxx.getKqbh());
                        // 考点名称
                        kdxx.setKdmc(_kdxx.getKdmc());
                        kdxx.setBzhkdmc(_kdxx.getKdmc());
                        bzhkdmc = _kdxx.getKdmc();
                        // 标准化考点编号
                        kdxx.setBzhkdid(_kdxx.getBzhkdbh());
                        bzhkdid = _kdxx.getBzhkdbh();
                        bzhkdbhs.add(_kdxx.getBzhkdbh());
                        Date date = new Date();
                        kdxx.setCreateTime(date);
                        kdxx.setUpdateTime(date);

                        kdxx.setScztw(ScztEnum.NOTDEL.getCode());
                        kdxx.setKsjhbh(ksjhbh);
                        kdxxList.add(kdxx);

                        if (kdxxList.size() >= 1000){
                            Example example = new Example(KsKdxx.class);
                            example.createCriteria()
                                    .andEqualTo("ksjhbh", ksjhbh)
                                    .andIn("bzhkdid", bzhkdbhs);
                            ksKdxxService.deleteByExample(example);
                            ksKdxxService.insertListSelective(kdxxList);

                            kdxxList.clear();
                            bzhkdbhs.clear();
                        }
                    }
                    if (kdxxList.size() > 0){
                        Example example = new Example(KsKdxx.class);
                        example.createCriteria()
                                .andEqualTo("ksjhbh", ksjhbh)
                                .andIn("bzhkdid", bzhkdbhs);
                        ksKdxxService.deleteByExample(example);
                        ksKdxxService.insertListSelective(kdxxList);
                    }

                    // 考试场次
                    List<KsKscc> ksccList = new ArrayList<>();
                    List<String> ccbhs = new ArrayList<>();
                    List<KsKscc4PkgDO> _ksccList = SqliteManager.queryList(connection, "", KsKscc4PkgDO.class);
                    for (KsKscc4PkgDO _kscc : _ksccList) {
                        KsKscc ksKscc = new KsKscc();

                        ksKscc.setBh(IdGenerateUtil.generateId());
                        ksKscc.setKsjhbh(ksjhbh);
                        // 场次码
                        ksKscc.setCcm(_kscc.getCcbh());
                        ccbhs.add(_kscc.getCcbh());
                        // 场次名称
                        ksKscc.setCcmc(_kscc.getCcmc());
                        // 开始时间
                        ksKscc.setKssj(DateUtil.parse(_kscc.getKssj()));
                        // 结束时间
                        ksKscc.setJssj(DateUtil.parse(_kscc.getJssj()));
                        // 科目码
                        ksKscc.setKmm(_kscc.getKmm());
                        // 科目名称
                        ksKscc.setKmmc(_kscc.getKmmc());
                        // 允许迟到时间
                        ksKscc.setYxcdsj(Integer.valueOf(_kscc.getYxcdsj()));
                        // 考生入场时间
                        ksKscc.setKsrcsj(DateUtil.parse(_kscc.getKsrcsj()));
                        // 开始上报缺考时间
                        ksKscc.setKssbqksj(DateUtil.parse(_kscc.getKssbqksj()));
                        Date date = new Date();
                        ksKscc.setCjsj(date);
                        ksKscc.setXgsj(date);

                        ksKscc.setScztw(ScztEnum.NOTDEL.getCode());
                        ksccList.add(ksKscc);

                        if (ksccList.size() >= 1000){
                            Example example = new Example(KsKscc.class);
                            example.createCriteria()
                                    .andEqualTo("ksjhbh", ksjhbh)
                                    .andIn("ccm", ccbhs);
                            ksKsccService.deleteByExample(example);
                            ksKsccService.insertListSelective(ksccList);

                            ksccList.clear();
                            ccbhs.clear();
                        }
                    }
                    if (ksccList.size() > 0){
                        Example example = new Example(KsKscc.class);
                        example.createCriteria()
                                .andEqualTo("ksjhbh", ksjhbh)
                                .andIn("ccm", ccbhs);
                        ksKsccService.deleteByExample(example);
                        ksKsccService.insertListSelective(ksccList);
                    }
                    event.setEventDesc(DataSynchTypeEnum.SJKSJHXX.getTblxmc() + "同步成功");
                    event.setEventDescDetail(DataSynchTypeEnum.SJKSJHXX.getTblxmc() + "同步成功");
                    event.setEventType(EventTypeSjtbEnum.SUCCESS.getCode());
                    event.setParam2(DateUtil.format(new Date(), DateUtil.DEFAULT_DATE_TIME));
                    event.setEventHandleTime(new Date());

                    importTask.setComplete(KssjImportTaskCompleteEnum.COMPLETE.getValue());
                    importTask.setCompleteTime(DateUtil.getCurrentDT());
                    importTask.setTProgress("100");

                    log.info("==============配置信息导入完成======完成时间:" + DateUtil.getCurrentDateTime());
                }catch (Exception e){
                    importTask.setComplete(KssjImportTaskCompleteEnum.FAIL.getValue());
                    kssjImportException(ksjhbh, e, importTask.getId(), importTask.getTConf());
                    log.error("错误消息:{}", e.getMessage(), e);
                    e.printStackTrace();
                    event.setEventCatalog(EventCatalogSjtbEnum.SJTB_EVENT.getCode());
                    event.setEventDesc("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc());
                    event.setEventDescDetail("数据同步错误:"+EventTypeSjtbEnum.TBCW.getDesc()+":"+e.getMessage());
                    event.setEventType(EventTypeSjtbEnum.TBCW.getCode());
                    event.setEventHandle(EventHandleEnum.UNTREATED.getCode());
                } finally {
                    event.setBzhkdid(bzhkdid);
                    event.setBzhkdmc(bzhkdmc);
                    SqliteManager.closeConnection(connection);
                    updateEvent(event);
                    importTaskUpdate(importTask);
                }
            }
        });
    }

    protected KsKssjImportFile getImportFileById(String fileId){
        return ksKssjImportFileService.selectSingleByKey(fileId);
    }

    public String sckstzz(String srcZpFilePath) {
        log.info("生成考生特征");
        try {
            // 照片保存位置
            String srcDir = hsFaceFeatureFilePath + "/src" + File.separator;
            String photoDir = srcDir + zpName;
            // 清空照片
            File folder = new File(srcDir);
            if (folder.exists()) {
                FileUtils.deleteDirectory(folder);
            } else {
                folder.mkdirs();
            }
            File photoFile = new File(photoDir);
            if (!photoFile.exists()) {
                photoFile.mkdirs();
            }
            FileUtils.copyDirectory(new File(srcZpFilePath), photoFile);
            HsCmdUtil.runCmd(hsFaceFeatureCreatorPath + "/HsFaceFeatureCreator -c config.json -l log.log");
            String tzzResultPath = hsFaceFeatureFilePath + "/dst/result/";
            return tzzResultPath;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    protected void insertKstzzCreatorException(KsKssjImportTask importTask, String exceptionPath, String ksjhbh, KsKdxx kdKdxx) {
        File file = new File(exceptionPath);
        File[] files = file.listFiles();
        if (files != null) {
            ArrayList<KsKssjImportException> list = new ArrayList<>();
            for (int i = 0; i < files.length; i++) {
                KsKssjImportException exception = new KsKssjImportException();
                exception.setId(IdGenerateUtil.generateId());
                exception.setKdbh(kdKdxx.getKdbh());
                exception.setBzhkdid(kdKdxx.getBzhkdid());
                exception.setKsjhbh(ksjhbh);
                exception.setTDesc("生成特征值失败：" + files[i].getName());
                exception.setCreateTime(new Date());
                exception.setUpdateTime(new Date());
                exception.setImportTaskId(importTask.getId());

                list.add(exception);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                importExceptionMapper.insertListSelective(list);
            }
        }
    }

    protected void initDistributeStatus(String ksjhbh){
        KsKsjh ksjh = ksKsjhMapper.selectByPrimaryKey(ksjhbh);
        Integer kzqy = ksjh.getKzqy();
        KsKssjDistributeStatus initDistributeStatus = new KsKssjDistributeStatus();
        initDistributeStatus.setCommonPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        initDistributeStatus.setStuinfPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        initDistributeStatus.setStuzpPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0) {
            initDistributeStatus.setJkrybpxxPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        }
        if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0) {
            initDistributeStatus.setJkryjbxxPkgStatus(DataDistributeStatusEnum.Wait.getCode());
            initDistributeStatus.setJkryzpPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        }
        Example example = new Example(KsKssjDistributeStatus.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh);
        List<KsKssjDistributeStatus> distributeStatuses = ksKssjDistributeStatusService.selectListByExample(example);
        if (!distributeStatuses.isEmpty()) {
            ksKssjDistributeStatusService.updateByExampleSelective(initDistributeStatus, example);
        }
    }

    protected void initPkgStatus(String ksjhbh, String drlx){
        KsKsjh ksjh = ksKsjhMapper.selectByPrimaryKey(ksjhbh);
        Integer kzqy = ksjh.getKzqy();
        KsKssjPkgStatus initPkgStatus = new KsKssjPkgStatus();
        boolean flag = false;
        if (StringUtils.equals(drlx, "ks")) {
            initPkgStatus.setKssjbQk(SjbQkEnum.NOT_STARTED.getValue());
            initPkgStatus.setKszpsjbQk(SjbQkEnum.NOT_STARTED.getValue());
            flag = true;
        }
        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0 && StringUtils.equals(drlx, "jk")) {
            initPkgStatus.setJkrybpsjbQk(SjbQkEnum.NOT_STARTED.getValue());
            flag = true;
        }
        if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0 && StringUtils.equals(drlx, "jk")) {
            initPkgStatus.setJkryjbsjbQk(SjbQkEnum.NOT_STARTED.getValue());
            initPkgStatus.setJkryzpsjbQk(SjbQkEnum.NOT_STARTED.getValue());
            flag = true;
        }
        Example example = new Example(KsKssjPkgStatus.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh);
        List<KsKssjPkgStatus> pkgStatuses = ksKssjPkgStatusService.selectListByExample(example);
        if (!pkgStatuses.isEmpty() && flag) {
            ksKssjPkgStatusService.updateByExampleSelective(initPkgStatus, example);
        }
    }

}
