package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums;

import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.*;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeVO;
import com.xcwlkj.util.StringUtil;
import org.bouncycastle.cert.ocsp.Req;

public enum MqttDevEventEnum {

    SCH_WJ_REGISTER("SCH_WJ_REGISTER", "违纪登记", "NOTIFY_SCH_WJ_REGISTER", WgksDjReq.class, MqttUnifyAccessEventHandlerEnum.WGKS_EVENT_HANDLER)
    ,SCH_WJ_QUERY("SCH_WJ_QUERY", "校级违纪列表查询", "NOTIFY_SCH_WJ_QUERY", WgksLbReq.class, MqttUnifyAccessEventHandlerEnum.WGKS_EVENT_HANDLER)
    ,SCH_WJ_HANDLE("SCH_WJ_HANDLE", "违纪处理", "NOTIFY_SCH_WJ_HANDLE", WgksClReq.class, MqttUnifyAccessEventHandlerEnum.WGKS_EVENT_HANDLER)
    ,SCH_WJ_REPORT("SCH_WJ_REPORT", "违纪上报", "NOTIFY_SCH_WJ_REPORT", WgksSbReq.class, MqttUnifyAccessEventHandlerEnum.WGKS_EVENT_HANDLER)

    ,SCH_KWMSG_PUBLISH("SCH_KWMSG_PUBLISH", "考务消息发布", "NOTIFY_SCH_KWMSG_PUBLISH", KwxxFbReq.class, MqttUnifyAccessEventHandlerEnum.KWMSG_EVENT_HANDLER)
    ,SCH_KWMSG_HANDLE("SCH_KWMSG_HANDLE", "考务消息处理", "NOTIFY_SCH_KWMSG_HANDLE", KwxxClReq.class, MqttUnifyAccessEventHandlerEnum.KWMSG_EVENT_HANDLER)
    ,SCH_KWMSG_KCQUERY("SCH_KWMSG_KCQUERY", "考场考务消息查询", "NOTIFY_SCH_KWMSG_KCQUERY", KwxxKcLbReq.class, MqttUnifyAccessEventHandlerEnum.KWMSG_EVENT_HANDLER)
    ,SCH_KWMSG_KWDLBQUERY("SCH_KWMSG_KWDLBQUERY", "考务端考务消息列表查询", "NOTIFY_SCH_KWMSG_KWDLBQUERY", KwxxKwdLbReq.class, MqttUnifyAccessEventHandlerEnum.KWMSG_EVENT_HANDLER)
    ,SCH_KWMSG_KWDXQQUERY("SCH_KWMSG_KWDXQQUERY", "考务端考务消息详情查询", "NOTIFY_SCH_KWMSG_KWDXQQUERY",KwxxKwdXqReq.class, MqttUnifyAccessEventHandlerEnum.KWMSG_EVENT_HANDLER)

    ,SCH_URGENCYMSG_PUBLISH("SCH_URGENCYMSG_PUBLISH", "紧急呼叫发布", "NOTIFY_SCH_URGENCYMSG_PUBLISH", JjhjFbReq.class, MqttUnifyAccessEventHandlerEnum.URGENCYMSG_EVENT_HANDLER)
    ,SCH_URGENCYMSG_HANDLE("SCH_URGENCYMSG_HANDLE", "紧急呼叫处理", "NOTIFY_SCH_URGENCYMSG_HANDLE", JjhjClReq.class, MqttUnifyAccessEventHandlerEnum.URGENCYMSG_EVENT_HANDLER)
    ,SCH_URGENCYMSG_KCQUERY("SCH_URGENCYMSG_KCQUERY", "考场紧急呼叫查询", "NOTIFY_SCH_URGENCYMSG_KCQUERY", JjhjKcLbReq.class, MqttUnifyAccessEventHandlerEnum.URGENCYMSG_EVENT_HANDLER)
    ,SCH_URGENCYMSG_KWDQUERY("SCH_URGENCYMSG_KWDQUERY", "考务端紧急呼叫查询", "NOTIFY_SCH_URGENCYMSG_KWDQUERY", JjhjKwdLbReq.class, MqttUnifyAccessEventHandlerEnum.URGENCYMSG_EVENT_HANDLER)

    ,SCH_KCQK_QUERY("SCH_KCQK_QUERY", "考场情况查询", "NOTIFY_SCH_KCQK_QUERY", KcqkQueryReq.class, MqttUnifyAccessEventHandlerEnum.KCQK_EVENT_HANDLER)

    ,SCH_KQZL_QUERY("SCH_KQZL_QUERY", "考情总览查询", "NOTIFY_SCH_KQZL_QUERY", KqzlQueryReq.class, MqttUnifyAccessEventHandlerEnum.KQZL_EVENT_HANDLER)

    ,SCH_KCLB_QUERY("SCH_KCLB_QUERY", "考场列表查询",  "NOTIFY_SCH_KCLB_QUERY", KclbQueryReq.class, MqttUnifyAccessEventHandlerEnum.KCLB_EVENT_HANDLER)

    ,SCH_KWBD_CHECKIN("SCH_KWBD_CHECKIN", "考务报到", "NOTIFY_SCH_KWBD_CHECKIN", KwbdCheckinReq.class, MqttUnifyAccessEventHandlerEnum.KWBD_EVENT_HANDLER)
    ,SCH_KWBD_QUERY("SCH_KWBD_QUERY", "考务报到查询", "NOTIFY_SCH_KWBD_QUERY", KwbdQueryReq.class, MqttUnifyAccessEventHandlerEnum.KWBD_EVENT_HANDLER)

    ,SCH_KWTJ_QUERY("SCH_KWTJ_QUERY", "考务统计查询", "NOTIFY_SCH_KWTJ_QUERY", KwtjQueryReq.class, MqttUnifyAccessEventHandlerEnum.KWTJ_EVENT_HANDLER)

    ,HQSBMY("HQSBMY", "获取设备密钥", "NOTIFY_HQSBMY", SbmyFetchReq.class, MqttUnifyAccessEventHandlerEnum.SBMY_EVENT_HANDLER)
    ,SBJH("SBJH", "终端激活", "", SbxxReq.class, MqttUnifyAccessEventHandlerEnum.SBMY_EVENT_HANDLER)
    ,SBMYYZ("SBMYYZ", "设备密钥验证", "NOTIFY_SBMYYZ", SbmyValidateReq.class, MqttUnifyAccessEventHandlerEnum.SBMY_EVENT_HANDLER)

    ,KS_JKJSHY("KS_JKJSHY", "监考人员签到", "", StringOperationReq.class, MqttUnifyAccessEventHandlerEnum.KSANDJKRC_EVENT_HANDLER)
    ,KS_RCHY("KS_RCHY", "考生核验入场", "", StringOperationReq.class, MqttUnifyAccessEventHandlerEnum.KSANDJKRC_EVENT_HANDLER)
    ,KS_RCQK("KS_RCQK", "考生入场人工审核", "", StringOperationReq.class, MqttUnifyAccessEventHandlerEnum.KSANDJKRC_EVENT_HANDLER)
    ,KS_RCZS("KS_RCZS", "终端上报-考生入场总数", "NOTIFY_ACK_MSG", StringOperationReq.class, MqttUnifyAccessEventHandlerEnum.KSANDJKRC_EVENT_HANDLER)
    ,KDGWXZZT("KDGWXZZT", "上报考点、考场网关下载状态", "", KdwgxzztArrayReq.class, MqttUnifyAccessEventHandlerEnum.KSSJ_EVENT_HANDLER)
    ,HQKSJH("HQKSJH", "获取考试计划信息", "NOTIFY_XFCXKSJH", HqksjhReq.class, MqttUnifyAccessEventHandlerEnum.KSSJ_EVENT_HANDLER)
    ,HQKDWJ("HQKDWJ", "获取考点文件地址", "NOTIFY_XFCXKDWJ", HqkdwjReq.class, MqttUnifyAccessEventHandlerEnum.KSSJ_EVENT_HANDLER)
    ,HQJGLB("HQJGLB", "获取机构列表", "NOTIFY_HQJGLB", HqjglbReq.class, MqttUnifyAccessEventHandlerEnum.KSSJ_EVENT_HANDLER)

    ,REPORT_DEVEVENT("REPORT_DEVEVENT", "设备事件上报", "", BaseOperationReq.class, MqttUnifyAccessEventHandlerEnum.DEVICE_EVENT_HANDLER)

    ,HQJGSBLB("HQJGSBLB", "获取机构设备列表", "NOTIFY_HQJGSBLB", HqjgsblbReq.class, MqttUnifyAccessEventHandlerEnum.SBZD_EVENT_HANDLER)
    ,HQLRCKCXX("HQLRCKCXX", "获取考场信息", "NOTIFY_HQLRCKCXX", HqlrckcxxReq.class, MqttUnifyAccessEventHandlerEnum.SBZD_EVENT_HANDLER)
    ,YDZDXZZT("YDZDXZZT", "移动终端上报下载状态", "NOTIFY_YDZDXZZT", YdzdxzztReq.class, MqttUnifyAccessEventHandlerEnum.SBZD_EVENT_HANDLER)
    ,JXSJXZZZTSB("on_school_updated", "教学数据下载状态上报", "", JxsjxzztReq.class, MqttUnifyAccessEventHandlerEnum.JXSJ_EVENT_HANDLER)
    ,SCH_JKRYRC_QUERY("SCH_JKRYRC_QUERY", "监考人员入场情况查询", "NOTIFY_SCH_JKRYRC_QUERY", JkryrcQueryReq.class, MqttUnifyAccessEventHandlerEnum.JKRYRC_EVENT_HANDLER)
    ,SJGJ("upgrade", "升级固件", "", JxsjxzztReq.class, MqttUnifyAccessEventHandlerEnum.SJGJ_EVENT_HANDLER)
    ,KS_RCZP("KS_RCZP", "终端上报-考生核验照片上传", "", StringOperationReq.class, MqttUnifyAccessEventHandlerEnum.KSANDJKRC_EVENT_HANDLER)
    ,KS_JKJSQD("KS_JKJSQD", "无编排监考人员签到", "", StringOperationReq.class, MqttUnifyAccessEventHandlerEnum.KSANDJKRC_EVENT_HANDLER)
    ,WJFWCSPZCX("CSPZCX", "文件服务参数配置查询", "NOTIFY_CSPZCX", WjfwcspzcxReq.class, MqttUnifyAccessEventHandlerEnum.WJFWCSPZCX_ENENT_HANDLER)
    ,WGCSPZXF("XFWGCSPZ", "网关配置参数下发", "NOTIFY_XFWGCSPZ", WgcspzxfReq.class, MqttUnifyAccessEventHandlerEnum.WGCSPZXF_EVENT_HANDLER)
    ,XFYDZDGJ("XFYDZDGJ", "移动终端固件下发", "NOTIFY_XFYDZDGJ", XfydzdgjReq.class, MqttUnifyAccessEventHandlerEnum.XFYDZDGJ_EVENT_HANDLER)
    ,NOTIFY_XFKSJH("", "移动终端固件下发", "INVOKE_FUNCTION_NOTIFY_XFKSJH", FunctionInvokeVO.class, MqttUnifyAccessEventHandlerEnum.KSSJDR_EVENT_HANDLER)
    ,NOTIFY_XFYDZDGJ("XFYDZDGJ", "移动终端固件下发", "INVOKE_FUNCTION_NOTIFY_XFYDZDGJ", FunctionInvokeVO.class, MqttUnifyAccessEventHandlerEnum.YDZDGJJS_EVENT_HANDLER)
    ;
    /**
     * 主题key
     */
    final String code;
    /**
     * 下行functionId
     */
    final String functionId;
    /**
     * 名称
     */
    final String name;
    /**
     * 请求类
     */
    final Class<?> reqClass;

    final MqttUnifyAccessEventHandlerEnum eventHandlerEnum;

    MqttDevEventEnum(String code, String name,String functionId,Class<?> clazz,MqttUnifyAccessEventHandlerEnum eventHandlerEnum) {
        this.code = code;
        this.functionId = functionId;
        this.name = name;
        this.reqClass = clazz;
        this.eventHandlerEnum = eventHandlerEnum;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getFunctionId() {
        return functionId;
    }

    public Class<?> getReqClass() {
        return reqClass;
    }

    public MqttUnifyAccessEventHandlerEnum getEventHandlerEnum() {
        return eventHandlerEnum;
    }

    public static MqttDevEventEnum getEvent(String code){
        for (MqttDevEventEnum ele : MqttDevEventEnum.values()) {
            if (StringUtil.equals(ele.getCode(), code)) {
                return ele;
            }
        }
        return null;
    }

    public static MqttDevEventEnum getEventByFuncId(String functionId){
        for (MqttDevEventEnum ele : MqttDevEventEnum.values()) {
            if (StringUtil.equals(ele.getFunctionId(), functionId)) {
                return ele;
            }
        }
        return null;
    }
}
