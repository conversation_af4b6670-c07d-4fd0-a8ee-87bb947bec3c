/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.facade.manager;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.auth.annotation.Permission;
import com.xcwlkj.base.BaseController;
import com.xcwlkj.dfs.model.vo.FileItemVO;
import com.xcwlkj.dfs.model.vo.TimePathVO;
import com.xcwlkj.dfs.model.vo.UploadItemVO;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.cache.MbCacheOperateService;
import com.xcwlkj.identityverify.model.domain.KsJkryBpxx;
import com.xcwlkj.identityverify.model.domain.KsJkryJbxx;
import com.xcwlkj.identityverify.model.domain.KsKscc;
import com.xcwlkj.identityverify.model.domain.KsKsjh;
import com.xcwlkj.identityverify.model.dto.ksgl.*;
import com.xcwlkj.identityverify.model.req.ksgl.*;
import com.xcwlkj.identityverify.model.resp.ksgl.*;
import com.xcwlkj.identityverify.model.vo.ksgl.*;
import com.xcwlkj.identityverify.service.*;
import com.xcwlkj.identityverify.service.impl.XxwjdrServiceImpl;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.third.superior.http.service.*;
import com.xcwlkj.identityverify.util.ExcelFormatUtil;
import com.xcwlkj.identityverify.util.HsUtils;
import com.xcwlkj.identityverify.util.OrderUtils;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * Ksgl控制层
 * <AUTHOR>
 * @version $Id: KsglController.java, v 0.1 2023年09月20日 10时50分 xcwlkj.com Exp $
 */
@Slf4j
@RestController("KsglManagerController")
@RequestMapping(value = "", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
public class KsglController extends BaseController {
    @Value("${xc.temp.path}")
    private String localPath;
    @Value("${xc.temp.generatePath}")
    private String rootPath;
    @Value("${xc.xcDfs.prefixUrl}")
    private String dfsServer;
    private static final String _zipFileRootPath = "zipFile"; // 存放 zip 文件的文件夹

    private static final String _xlsxFileRootPath = "xlsxFile"; // 存放 xlsx 文件的文件夹

    private static final String _jkryFileRootPath = "jkryFile"; // 存放监考人员文件的文件夹

    @Resource
	private KsKsjhService ksKsjhService;
    @Resource
    private KsKsccService ksKsccService;
    @Resource
    private XxwjdrServiceImpl xxwjdrService;
    @Resource
    private KsBpxxService ksBpxxService;
    @Resource
    private KsJkryBpxxService ksJkryBpxxService;
    @Resource
    private KsKsrcxxService ksKsrcxxService;
    @Resource
    private KsJkryRcxxService ksJkryRcxxService;
    @Resource
    private KsJkryJbxxService ksJkryJbxxService;
    @Resource
    private KsKcxxService ksKcxxService;
    @Resource
    private CsJsjbxxService csJsjbxxService;
    @Resource
    private MbCacheOperateService mbCacheOperateService;
    @Resource
    private KsProcessEventService ksProcessEventService;
    @Resource
    private HisomeServer hisomeServer;
    @Resource
    private HbTHService hbTHService;
    @Resource
    private JsJFService jsJFService;
    @Resource
    private SxService sxService;
    @Resource
    private KsBmxxService ksBmxxService;
    @Resource
    private HisomeV1Service hisomeV1Service;

    /**
    * 考试计划新增
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/ksjhxz")
    public Wrapper<KsjhxzRespModel> ksjhxz(@RequestBody KsjhxzReqModel reqModel) {
		log.info("收到请求开始：[考试计划新增][/manager/identityverify/ksgl/ksjhxz]reqModel:"+reqModel.toString());
		KsjhxzDTO dto = new KsjhxzDTO();

        BeanUtils.copyProperties(reqModel,dto);
        ksKsjhService.ksjhxz(dto);

        KsjhxzRespModel respModel = new KsjhxzRespModel();
        log.info("处理请求结束：[考试计划新增][/manager/identityverify/ksgl/ksjhxz]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试计划更新
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/ksjhgx")
    public Wrapper<KsjhgxRespModel> ksjhgx(@RequestBody KsjhgxReqModel reqModel) {
		log.info("收到请求开始：[考试计划更新][/manager/identityverify/ksgl/ksjhgx]reqModel:"+reqModel.toString());
		KsjhgxDTO dto = new KsjhgxDTO();

        BeanUtils.copyProperties(reqModel,dto);
		ksKsjhService.ksjhgx(dto);

        KsjhgxRespModel respModel = new KsjhgxRespModel();
		log.info("处理请求结束：[考试计划更新][/manager/identityverify/ksgl/ksjhgx]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试计划删除
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ksjhsc")
    @PostMapping(value = "/manager/identityverify/ksgl/ksjhsc")
    public Wrapper<KsjhscRespModel> ksjhsc(@RequestBody KsjhscReqModel reqModel) {
		log.info("收到请求开始：[考试计划删除][/manager/identityverify/ksgl/ksjhsc]reqModel:"+reqModel.toString());

		ksKsjhService.deleteKsjhByKsjhbh(reqModel.getKsjhbhList());

        KsjhscRespModel respModel = new KsjhscRespModel();
		log.info("处理请求结束：[考试计划删除][/manager/identityverify/ksgl/ksjhsc]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试计划列表
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/ksjhlb")
    public Wrapper<KsjhlbRespModel> ksjhlb(@RequestBody KsjhlbReqModel reqModel) {
		log.info("收到请求开始：[考试计划列表][/manager/identityverify/ksgl/ksjhlb]reqModel:"+reqModel.toString());
		KsjhlbDTO dto = new KsjhlbDTO();

        BeanUtils.copyProperties(reqModel,dto);
        KsjhlbVO data = ksKsjhService.ksjhlb(dto);
        KsjhlbRespModel respModel = new KsjhlbRespModel();

        respModel.setKsjhList(data.getKsjhList());
        respModel.setTotalRows(data.getTotalRows());
		log.info("处理请求结束：[考试计划列表][/manager/identityverify/ksgl/ksjhlb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 更改考试状态
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ggkszt")
    @PostMapping(value = "/manager/identityverify/ksgl/ggkszt")
    public Wrapper<GgksztRespModel> ggkszt(@RequestBody GgksztReqModel reqModel) {
		log.info("收到请求开始：[更改考试状态][/manager/identityverify/ksgl/ggkszt]reqModel:"+reqModel.toString());
		GgksztDTO dto = new GgksztDTO();
        BeanUtils.copyProperties(reqModel,dto);

        ksKsjhService.ggkszt(dto);
        GgksztRespModel respModel = new GgksztRespModel();

		log.info("处理请求结束：[更改考试状态][/manager/identityverify/ksgl/ggkszt]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考生信息导入
    * @param
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/ksxxdr")
    public Wrapper<Void> ksxxdr(@RequestPart(value = "file", required = true) MultipartFile file,
                                @RequestParam(value = "ksjhbh", required = true) String ksjhbh,
                                @RequestParam(value = "drlx", required = true) String drlx,
                                @RequestParam(value = "mrkm", required = false) String mrkm) {
		log.info("收到请求开始：[考生信息导入][/manager/identityverify/ksgl/ksxxdr]");

        String path = rootPath+File.separator + _xlsxFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
                DateUtil.FULL_DATETIMEFORMAT) + File.separator;
        String filePath = path + file.getOriginalFilename();
        File zipFile = new File(filePath);
        if (!zipFile.getParentFile().exists()){
            zipFile.getParentFile().mkdirs();
        }
        try {
            file.transferTo(zipFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        xxwjdrService.xxwjdr(zipFile, ksjhbh, drlx, "ksxx", mrkm);
        log.info("处理请求结束：[考生信息导入][/manager/identityverify/ksgl/ksxxdr]");
        return WrapMapper.ok();
    }

    /**
     * 监考信息导入
     * @param
     * @return
     */
    @PostMapping(value = "/manager/identityverify/ksgl/jkxxdr")
    public Wrapper<Void> jkxxdr(@RequestPart(value = "file", required = true) MultipartFile file,
                                           @RequestParam(value = "ksjhbh", required = true) String ksjhbh,
                                           @RequestParam(value = "drlx", required = true) String drlx) {
        log.info("收到请求开始：[监考信息导入][/manager/identityverify/ksgl/jkxxdr]");
        String path = rootPath+File.separator + _xlsxFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
                DateUtil.FULL_DATETIMEFORMAT) + File.separator;
        String filePath = path + file.getOriginalFilename();
        File zipFile = new File(filePath);
        if (!zipFile.getParentFile().exists()){
            zipFile.getParentFile().mkdirs();
        }
        try {
            file.transferTo(zipFile);
        } catch (IOException e) {
            e.printStackTrace();
        }

        xxwjdrService.xxwjdr(zipFile, ksjhbh, drlx, "jkry", null);
        log.info("处理请求结束：[监考信息导入][/manager/identityverify/ksgl/jkxxdr]");
        return WrapMapper.ok();
    }

    /**
     * 考试场次列表
     * @param reqModel
     * @return
     */
	@Permission("ksgl:kscclb")
    @PostMapping(value = "/manager/identityverify/ksgl/kscclb")
    public Wrapper<KscclbRespModel> kscclb(@RequestBody KscclbReqModel reqModel) {
        log.info("收到请求开始：[考试场次列表][/manager/identityverify/ksgl/kscclb]reqModel:"+reqModel.toString());
        KscclbDTO dto = new KscclbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setCcmc(reqModel.getCcmc());
        KscclbVO info = ksKsccService.getKscclb(dto, reqModel.getPageNum(), reqModel.getPageSize());
        KscclbRespModel respModel = new KscclbRespModel();
        respModel.setKsccList(info.getKsccList());
        respModel.setTotal(info.getTotal());
        log.info("处理请求结束：[考试场次列表][/manager/identityverify/ksgl/kscclb]reqModel:"+reqModel.toString()
                +",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }


   /**
    * 考生编排列表
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/ksbpxxlb")
    public Wrapper<KsbpxxlbRespModel> ksbpxxlb(@RequestBody KsbpxxlbReqModel reqModel) {
		log.info("收到请求开始：[考生编排列表][/manager/identityverify/ksgl/ksbpxxlb]reqModel:"+reqModel.toString());
		KsbpxxlbDTO dto = new KsbpxxlbDTO();
        KsbpxxlbRespModel respModel = new KsbpxxlbRespModel();
        int total = 0;
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setSfzjhm(reqModel.getSfzjhm());
        dto.setKsh(reqModel.getKsh());
        dto.setKsxm(reqModel.getKsxm());
        dto.setKch(reqModel.getKch());
        dto.setOrderCol(reqModel.getOrderCol());
        dto.setOrderType(reqModel.getOrderType());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        Example example = new Example(KsKscc.class);
        example.createCriteria().andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        List<KsKscc> ksccs = ksKsccService.selectListByExample(example);
        if(ksccs != null && ksccs.size() != 0) {
            Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            List<KsbpxxItemVO> bpxxItemVOList = ksBpxxService.ksbpxxlb(dto.getKsjhbh(), dto.getCcm(), dto.getKsxm(), dto.getKsh(),
                    dto.getSfzjhm(), dto.getKch(), OrderUtils.checkOrderCol(dto.getOrderCol(), KsbpxxItemVO.class, "zkzh"), dto.getOrderType());

            if (bpxxItemVOList != null && bpxxItemVOList.size() != 0) {
                for (KsbpxxItemVO itemVO : bpxxItemVOList) {
                    String sfzh = itemVO.getSfzjhm();
                    itemVO.setSfzjhm(HsUtils.EncryptionSFZH(sfzh));
                }
            }
            total = (int) page.getTotal();
            respModel.setKsbpxxList(bpxxItemVOList);
            respModel.setCcmc(ksccs.get(0).getCcmc());
        }
        respModel.setTotalNum(total);
		log.info("处理请求结束：[考生编排列表][/manager/identityverify/ksgl/ksbpxxlb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 监考编排列表
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/jkbplb")
    public Wrapper<JkbplbRespModel> jkbplb(@RequestBody JkbplbReqModel reqModel) {
		log.info("收到请求开始：[监考编排列表][/manager/identityverify/ksgl/jkbplb]reqModel:"+reqModel.toString());
		JkbplbDTO dto = new JkbplbDTO();
        JkbplbRespModel respModel = new JkbplbRespModel();
        int total = 0;
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setSfzjhm(reqModel.getSfzjhm());
        dto.setXm(reqModel.getXm());
        dto.setGwlxm(reqModel.getGwlxm());
        dto.setOrderCol(reqModel.getOrderCol());
        dto.setOrderType(reqModel.getOrderType());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        Example example = new Example(KsKscc.class);
        example.createCriteria().andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("ccm", dto.getCcm())
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        List<KsKscc> ksccs = ksKsccService.selectListByExample(example);
        if(ksccs != null && ksccs.size() != 0) {
            Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            List<JkbpxxItemVO> lsBpxxItemVOList = ksJkryBpxxService.selectBpxxLb(dto.getKsjhbh(), dto.getCcm(), dto.getXm(),
                    dto.getGwlxm(), dto.getSfzjhm(), OrderUtils.checkOrderCol(dto.getOrderCol(), JkbpxxItemVO.class, "sfzjhm"), dto.getOrderType());
            if (lsBpxxItemVOList != null && lsBpxxItemVOList.size() != 0) {
                for (JkbpxxItemVO itemVO : lsBpxxItemVOList) {
                    String sfzh = itemVO.getSfzjhm();
                    itemVO.setSfzjhm(HsUtils.EncryptionSFZH(sfzh));
                }
            }
            total = (int) page.getTotal();
            respModel.setJkbpxxList(lsBpxxItemVOList);
            respModel.setCcmc(ksccs.get(0).getCcmc());
        }
        respModel.setTotalNum(total);
		log.info("处理请求结束：[监考编排列表][/manager/identityverify/ksgl/jkbplb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试场次删除
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ksccsc")
    @PostMapping(value = "/manager/identityverify/ksgl/ksccsc")
    public Wrapper<KsccscRespModel> ksccsc(@RequestBody KsccscReqModel reqModel) {
		log.info("收到请求开始：[考试场次删除][/manager/identityverify/ksgl/ksccsc]reqModel:"+reqModel.toString());
		KsccscDTO dto = new KsccscDTO();
        dto.setCcms(reqModel.getCcms());
        dto.setKsjhbh(reqModel.getKsjhbh());

		ksKsccService.ksccsc(dto);
        KsccscRespModel respModel = new KsccscRespModel();

		log.info("处理请求结束：[考试场次删除][/manager/identityverify/ksgl/ksccsc]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考场编排列表
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/kcbplb")
    public Wrapper<KcbplbRespModel> kcbplb(@RequestBody KcbplbReqModel reqModel) {
		log.info("收到请求开始：[考场编排列表][/manager/identityverify/ksgl/kcbplb]reqModel:"+reqModel.toString());
		KcbplbDTO dto = new KcbplbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcmList(reqModel.getCcmList());
        dto.setPageSize(reqModel.getPageSize());
        dto.setPageNum(reqModel.getPageNum());

		KcbplbVO result = ksKcxxService.kcbplb(dto);
        KcbplbRespModel respModel = new KcbplbRespModel();
        respModel.setKcbpList(result.getKcbpList());
        respModel.setTotalRows(result.getTotalRows());
		log.info("处理请求结束：[考场编排列表][/manager/identityverify/ksgl/kcbplb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 考场教室对应关系Excel导入
     * @param
     * @return
     */
    @PostMapping(value = "/manager/identityverify/ksgl/kcjsdygxExceldr")
    public Wrapper<Void> kcjsdygxExceldr(@RequestPart(value = "file", required = true) MultipartFile file,
                                         @RequestParam(required = true) String ksjhbh) {
        log.info("收到请求开始：[考场教室对应关系Excel导入][/manager/identityverify/ksgl/kcjsdygxExceldr] ksjhbh{}", ksjhbh);

        String path = localPath + DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT) + File.separator;
        // 验证文件正确性并保存文件到本地
        String filePath = ExcelFormatUtil.saveFileToService(path, file);
        ksKcxxService.kcjsdygxExcelsjdr(filePath, ksjhbh);

        log.info("处理请求结束：[考场教室对应关系Excel导入][/manager/identityverify/ksgl/kcjsdygxExceldr] ksjhbh{}", ksjhbh);
        return WrapMapper.ok();
    }

   /**
    * 考场教室对应关系Excel导出
    * @param reqModel
    * @return
    */
   @PostMapping(value = "/manager/identityverify/ksgl/kcjsdygxExceldc")
   public Wrapper<KcjsdygxExceldcRespModel> kcjsdygxExceldc(@RequestBody KcjsdygxExceldcReqModel reqModel) {
       log.info("收到请求开始：[考场教室对应关系Excel导出][/manager/identityverify/ksgl/kcjsdygxExceldc]reqModel:" + reqModel.toString());
       KcjsdygxExceldcDTO dto = new KcjsdygxExceldcDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       KcjsdygxExceldcVO result = ksKcxxService.kcjsdygxExceldc(dto);
       KcjsdygxExceldcRespModel respModel = new KcjsdygxExceldcRespModel();
       respModel.setDcwjlj(result.getDcwjlj());
       log.info("处理请求结束：[考场教室对应关系Excel导出][/manager/identityverify/ksgl/kcjsdygxExceldc]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
       return WrapMapper.ok(reqModel, respModel);
   }

   /**
    * 考场教室对应关系取消关联
    * @param reqModel
    * @return
    */
   @PostMapping(value = "/manager/identityverify/ksgl/kcjsdygxQxgl")
   public Wrapper<Void> kcjsdygxQxgl(@RequestBody KcjsdygxQxglReqModel reqModel) {
       log.info("收到请求开始：[考场教室对应关系取消关联][/manager/identityverify/ksgl/kcjsdygxQxgl]reqModel:" + reqModel.toString());
       KcjsdygxQxglDTO dto = new KcjsdygxQxglDTO();
       dto.setKcxxIds(reqModel.getKcxxIds());
       ksKcxxService.kcjsdygxQxgl(dto);
       log.info("处理请求结束：[考场教室对应关系取消关联][/manager/identityverify/ksgl/kcjsdygxQxgl]reqModel:" + reqModel.toString());
       return WrapMapper.ok();
   }

   /**
    * 考场关系分配情况
    * @param reqModel
    * @return
    */
   @PostMapping(value = "/manager/identityverify/ksgl/kcgxFpqk")
   public Wrapper<KcgxFpqkRespModel> kcgxFpqk(@RequestBody KcgxFpqkReqModel reqModel) {
       log.info("收到请求开始：[考场关系分配情况][/manager/identityverify/ksgl/kcgxFpqk]reqModel:" + reqModel.toString());
       KcgxFpqkDTO dto = new KcgxFpqkDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       dto.setCcm(reqModel.getCcm());
       dto.setCslx(reqModel.getCslx());
       dto.setFpqk(reqModel.getFpqk());
       dto.setPageSize(reqModel.getPageSize());
       dto.setPageNum(reqModel.getPageNum());
       KcgxFpqkVO result = ksKcxxService.kcgxFpqk(dto);
       KcgxFpqkRespModel respModel = new KcgxFpqkRespModel();
       respModel.setKcgxFpqkList(result.getKcgxFpqkList());
       respModel.setTotalRows(result.getTotalRows());
       log.info("处理请求结束：[考场关系分配情况][/manager/identityverify/ksgl/kcgxFpqk]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
       return WrapMapper.ok(reqModel, respModel);
   }

   /**
    * 考场关系未分配考场列表
    * @param reqModel
    * @return
    */
   @PostMapping(value = "/manager/identityverify/ksgl/kcgxWfpkclb")
   public Wrapper<KcgxWfpkclbRespModel> kcgxWfpkclb(@RequestBody KcgxWfpkclbReqModel reqModel) {
       log.info("收到请求开始：[考场关系未分配考场列表][/manager/identityverify/ksgl/kcgxWfpkclb]reqModel:" + reqModel.toString());
       KcgxWfpkclbDTO dto = new KcgxWfpkclbDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       dto.setCcm(reqModel.getCcm());
       KcgxWfpkclbVO result = ksKcxxService.kcgxWfpkclb(dto);
       KcgxWfpkclbRespModel respModel = new KcgxWfpkclbRespModel();
       respModel.setWfpkcList(result.getWfpkcList());
       log.info("处理请求结束：[考场关系未分配考场列表][/manager/identityverify/ksgl/kcgxWfpkclb]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
       return WrapMapper.ok(reqModel, respModel);
   }

   /**
    * 考场关系标准化考场列表
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/kcgxBzhkclb")
    public Wrapper<KcgxBzhkclbRespModel> kcgxBzhkclb(@RequestBody KcgxBzhkclbReqModel reqModel) {
        log.info("收到请求开始：[考场关系标准化考场列表][/manager/identityverify/ksgl/kcgxBzhkclb]reqModel:" + reqModel.toString());
        KcgxBzhkclbDTO dto = new KcgxBzhkclbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        KcgxBzhkclbVO result = ksKcxxService.kcgxBzhkclb(dto);
        KcgxBzhkclbRespModel respModel = new KcgxBzhkclbRespModel();
        respModel.setBzhkcList(result.getBzhkcList());
        log.info("处理请求结束：[考场关系标准化考场列表][/manager/identityverify/ksgl/kcgxBzhkclb]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 考场关系保存关联
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/kcgxBcgl")
    public Wrapper<Void> kcgxBcgl(@RequestBody KcgxBcglReqModel reqModel) {
		log.info("收到请求开始：[考场关系保存关联][/manager/identityverify/ksgl/kcgxBcgl]reqModel:"+reqModel.toString());
		KcgxBcglDTO dto = new KcgxBcglDTO();
        dto.setKcglgx(reqModel.getKcglgx());
        dto.setWglkcbhs(reqModel.getWglkcbhs());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setSffzdqtcc(reqModel.getSffzdqtcc());
		ksKcxxService.kcgxBcgl(dto);
		log.info("处理请求结束：[考场关系保存关联][/manager/identityverify/ksgl/kcgxBcgl]reqModel:"+reqModel.toString());
        return WrapMapper.ok();
    }
   /**
    * 考试计划编号查询科目列表
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ksjhbhcxkmlb")
    @PostMapping(value = "/manager/identityverify/ksgl/ksjhbhcxkmlb")
    public Wrapper<KsjhbhcxkmlbRespModel> ksjhbhcxkmlb(@RequestBody KsjhbhcxkmlbReqModel reqModel) {
		log.info("收到请求开始：[考试计划编号查询科目列表][/manager/identityverify/ksgl/ksjhbhcxkmlb]reqModel:"+reqModel.toString());
        KsKsjh ksjh = ksKsjhService.selectSingleByKey(reqModel.getKsjhbh());
        String ksxmdm = ksjh.getKslx();
        List<Map<Object, Object>> mbList = mbCacheOperateService.queryMbsjList("jy_kskmdmb:" + ksxmdm);
        List<KsjhbhCxkmItemVO> kmList = new ArrayList<KsjhbhCxkmItemVO>();
        for (Map<Object, Object> map : mbList) {
            KsjhbhCxkmItemVO cxkmItemVO = new KsjhbhCxkmItemVO();
            cxkmItemVO.setKmm(map.get("KM_DM").toString());
            cxkmItemVO.setKmmc(map.get("MC").toString());
            kmList.add(cxkmItemVO);
        }
        KsjhbhcxkmlbRespModel respModel = new KsjhbhcxkmlbRespModel();
        respModel.setKsjhbhCxkmList(kmList);
        log.info("处理请求结束：[考试计划编号查询科目列表][/manager/identityverify/ksgl/ksjhbhcxkmlb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 考场教室对应关系解除本场关联
    * @param reqModel
    * @return
    */
   @PostMapping(value = "/manager/identityverify/ksgl/kcjsdygxJcbcgl")
   public Wrapper<Void> kcjsdygxJcbcgl(@Validated @RequestBody KcjsdygxJcbcglReqModel reqModel) {
       log.info("收到请求开始：[考场教室对应关系解除本场关联][/manager/identityverify/ksgl/kcjsdygxJcbcgl]reqModel:" + reqModel.toString());
       KcjsdygxJcbcglDTO dto = new KcjsdygxJcbcglDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       dto.setCcm(reqModel.getCcm());
       ksKcxxService.kcjsdygxJcbcgl(dto);
       log.info("处理请求结束：[考场教室对应关系解除本场关联][/manager/identityverify/ksgl/kcjsdygxJcbcgl]reqModel:" + reqModel.toString());
       return WrapMapper.ok();
   }

   /**
    * 复制备用考场关联关系到其它场次
    * @param reqModel
    * @return
    */
   @PostMapping(value = "/manager/identityverify/ksgl/fzbykcglgxdqtcc")
   public Wrapper<Void> fzbykcglgxdqtcc(@RequestBody FzbykcglgxdqtccReqModel reqModel) {
       log.info("收到请求开始：[复制备用考场关联关系到其它场次][/manager/identityverify/ksgl/fzbykcglgxdqtcc]reqModel:" + reqModel.toString());
       FzbykcglgxdqtccDTO dto = new FzbykcglgxdqtccDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       dto.setCcm(reqModel.getCcm());
       dto.setKcglgx(reqModel.getKcglgx());
       ksKcxxService.fzbykcglgxdqtcc(dto);

       log.info("处理请求结束：[复制备用考场关联关系到其它场次][/manager/identityverify/ksgl/fzbykcglgxdqtcc]reqModel:" + reqModel.toString());
       return WrapMapper.ok();
   }
   /**
    * 获取导入类型
    * @param reqModel
    * @return
    */
	@Permission("ksgl:drlxlb")
    @PostMapping(value = "/manager/identityverify/ksgl/drlxlb")
    public Wrapper<DrlxlbRespModel> drlxlb(@RequestBody DrlxlbReqModel reqModel) {
		log.info("收到请求开始：[获取导入类型][/manager/identityverify/ksgl/drlxlb]reqModel:"+reqModel.toString());

        DrlxlbVO result = ksKsjhService.drlxlb();
        DrlxlbRespModel respModel = new DrlxlbRespModel();
        respModel.setDrlxList(result.getDrlxList());
		log.info("处理请求结束：[获取导入类型][/manager/identityverify/ksgl/drlxlb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取上级平台考试计划列表
    * @param reqModel
    * @return
    */
	@Permission("ksgl:sjptksjhlb")
    @PostMapping(value = "/manager/identityverify/ksgl/sjptksjhlb")
    public Wrapper<SjptksjhlbRespModel> sjptksjhlb(@RequestBody SjptksjhlbReqModel reqModel) {
		log.info("收到请求开始：[获取上级平台考试计划列表][/manager/identityverify/ksgl/sjptksjhlb]reqModel:"+reqModel.toString());
        String ptlx = reqModel.getPtlx();
        SjptksjhlbVO result = new SjptksjhlbVO();
        if (StringUtils.equals(ptlx, SuperiorPlatEnum.HISOME.getCode())) {
            result = hisomeServer.getExamPlanList();
        }
        if (StringUtils.equals(ptlx, SuperiorPlatEnum.HISOME_V1.getCode())) {
            result = hisomeV1Service.getExamPlanListV1();
        }
        if (StringUtils.equals(ptlx, SuperiorPlatEnum.HB_TH.getCode())){
            result = hbTHService.getExamplanList();
        }
        if (StringUtils.equals(ptlx, SuperiorPlatEnum.JS_JF.getCode())){
            result = jsJFService.getExamplanList();
        }
        if (StringUtils.equals(ptlx, SuperiorPlatEnum.SXCJXX.getCode())){
            result = ksKsjhService.getKsjhbhAndKsmc();
        }
        SjptksjhlbRespModel respModel = new SjptksjhlbRespModel();
        respModel.setSjptksjhList(result.getSjptksjhList());
		log.info("处理请求结束：[获取上级平台考试计划列表][/manager/identityverify/ksgl/sjptksjhlb]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 上级平台考试信息导入
    * @param
    * @return
    */
	@Permission("ksgl:sjksxxdr")
    @PostMapping(value = "/manager/identityverify/ksgl/sjksxxdr")
    public Wrapper<SjksxxdrRespModel> sjksxxdr(@RequestParam(value = "drlx", required = true) String drlx,
                                               @RequestParam(value = "ksjhbh", required = true) String ksjhbh,
                                               @RequestPart(value = "file", required = false) MultipartFile file) {
		log.info("收到请求开始：[上级平台考试信息导入][/manager/identityverify/ksgl/sjksxxdr]");
        if (StringUtils.equals(drlx, SuperiorPlatEnum.HISOME.getCode())) {
            hisomeServer.importData(ksjhbh);
        }
        if (StringUtils.equals(drlx, SuperiorPlatEnum.HISOME_V1.getCode())) {
            hisomeV1Service.importDataV1(ksjhbh);
        }
        if (StringUtils.equals(drlx, SuperiorPlatEnum.HB_TH.getCode())){
            hbTHService.importData(ksjhbh);
        }
        if (StringUtils.equals(drlx, SuperiorPlatEnum.JS_JF.getCode())){
            jsJFService.importData(ksjhbh);
        }

        if (StringUtils.equals(drlx, SuperiorPlatEnum.SXCJXX.getCode())){
            if (file != null) {
                String path = rootPath + File.separator + _zipFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
                        DateUtil.FULL_DATETIMEFORMAT) + File.separator;
                String filePath = path + file.getOriginalFilename();
                File zipFile = new File(filePath);
                if (!zipFile.getParentFile().exists()) {
                    zipFile.getParentFile().mkdirs();
                }
                try {
                    file.transferTo(zipFile);
                } catch (IOException e) {
                    e.printStackTrace();
                }
                sxService.importDbf(ksjhbh, filePath);
            }
        }
		log.info("处理请求结束：[上级平台考试信息导入][/manager/identityverify/ksgl/sjksxxdr]");
        return WrapMapper.ok();
    }
   /**
    * 导入模板获取
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/getImportModel")
    public Wrapper<GetImportModelRespModel> getImportModel(@RequestBody GetImportModelReqModel reqModel) {
		log.info("收到请求开始：[导入模板获取][/manager/identityverify/ksgl/getImportModel]reqModel:"+reqModel.toString());
		GetImportModelDTO dto = new GetImportModelDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setDrlx(reqModel.getDrlx());

		GetImportModelVO result = ksKsccService.getImportModel(dto);
        GetImportModelRespModel respModel = new GetImportModelRespModel();
        respModel.setUrl(result.getUrl());
		log.info("处理请求结束：[导入模板获取][/manager/identityverify/ksgl/getImportModel]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

    /**
     * 监考老师编辑
     * @param kchs 考场号
     * @param jkxm 监考姓名
     * @param zjhm 证件号码
     * @param gwzzm 岗位
     * @param ccm 场次
     * @param ksjhbh 考试计划编号
     * @param zp 照片
     * @return
     */
    @PostMapping(value = "/manager/identityverify/ksgl/jkrybj")
    public Wrapper<Void> jkrybj(@RequestParam(value = "kchs", required = false) List<String> kchs,
                                @RequestParam(value = "jkxm") String jkxm,
                                @RequestParam(value = "zjhm") String zjhm,
                                @RequestParam(value = "gwzzm") String gwzzm,
                                @RequestParam(value = "ccm") String ccm,
                                @RequestParam(value = "ksjhbh") String ksjhbh,
                                @RequestPart(value = "zp", required = false) MultipartFile zp) {
		log.info("收到请求开始：[监考老师编辑][/manager/identityverify/ksgl/jkrybj]");
        JkrybjDTO dto = new JkrybjDTO();
        if (zp != null){
            String path = rootPath + File.separator + _zipFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
                    DateUtil.FULL_DATETIMEFORMAT) + File.separator;
            String filePath = path + zp.getOriginalFilename();
            File zpFile = new File(filePath);
            if (!zpFile.getParentFile().exists()) {
                zpFile.getParentFile().mkdirs();
            }
            try {
                zp.transferTo(zpFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            dto.setZpPath(filePath);
        }
        dto.setKchs(kchs);
        dto.setJkxm(jkxm);
        dto.setZjhm(zjhm);
        dto.setGwzzm(gwzzm);
        dto.setCcm(ccm);
        dto.setKsjhbh(ksjhbh);

        ksJkryBpxxService.jkrybj(dto);
		log.info("处理请求结束：[监考老师编辑][/manager/identityverify/ksgl/jkrybj]");
        return WrapMapper.ok();
    }
   /**
    * 监考老师删除
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/jkrysc")
    public Wrapper<JkryscRespModel> jkrysc(@RequestBody JkryscReqModel reqModel) {
		log.info("收到请求开始：[监考老师删除][/manager/identityverify/ksgl/jkrysc]reqModel:"+reqModel.toString());
		JkryscDTO dto = new JkryscDTO();
        dto.setJkrybpbzids(reqModel.getJkrybpbzids());

		ksJkryBpxxService.jkrysc(dto);
        JkryscRespModel respModel = new JkryscRespModel();

		log.info("处理请求结束：[监考老师删除][/manager/identityverify/ksgl/jkrysc]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取工作人员岗位
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/getGwList")
    public Wrapper<GetGwListRespModel> getGwList(@RequestBody GetGwListReqModel reqModel) {
		log.info("收到请求开始：[获取工作人员岗位][/manager/identityverify/ksgl/getGwList]reqModel:"+reqModel.toString());
		GetGwListDTO dto = new GetGwListDTO();

		GetGwListVO result = ksJkryBpxxService.getGwList(dto);
        GetGwListRespModel respModel = new GetGwListRespModel();
        respModel.setGwList(result.getGwList());
		log.info("处理请求结束：[获取工作人员岗位][/manager/identityverify/ksgl/getGwList]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 获取导入结果
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/hqdrjg")
    public Wrapper<HqdrjgRespModel> hqdrjg(@RequestBody HqdrjgReqModel reqModel) {
		log.info("收到请求开始：[获取导入结果][/manager/identityverify/ksgl/hqdrjg]reqModel:"+reqModel.toString());
		HqdrjgDTO dto = new HqdrjgDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setDrlx(reqModel.getDrlx());

		HqdrjgVO result = ksProcessEventService.hqdrjg(dto);
        HqdrjgRespModel respModel = new HqdrjgRespModel();
        respModel.setEventType(result.getEventType());
        respModel.setDesc(result.getDesc());
        respModel.setDrsl(result.getDrsl());
        respModel.setUpdateTime(result.getUpdateTime());
		log.info("处理请求结束：[获取导入结果][/manager/identityverify/ksgl/hqdrjg]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试场次添加
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/kscctj")
    public Wrapper<KscctjRespModel> kscctj(@RequestBody KscctjReqModel reqModel) {
		log.info("收到请求开始：[考试场次添加][/manager/identityverify/ksgl/kscctj]reqModel:"+reqModel.toString());
		KscctjDTO dto = new KscctjDTO();
        dto.setCcm(reqModel.getCcm());
        dto.setCcmc(reqModel.getCcmc());
        dto.setRcjzsj(reqModel.getRcjzsj());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setZc(reqModel.getZc());
        dto.setKmList(reqModel.getKmList());

		ksKsccService.kscctj(dto);
        KscctjRespModel respModel = new KscctjRespModel();

		log.info("处理请求结束：[考试场次添加][/manager/identityverify/ksgl/kscctj]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试场次编辑
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/ksccbj")
    public Wrapper<KsccbjRespModel> ksccbj(@RequestBody KsccbjReqModel reqModel) {
		log.info("收到请求开始：[考试场次编辑][/manager/identityverify/ksgl/ksccbj]reqModel:"+reqModel.toString());
		KsccbjDTO dto = new KsccbjDTO();
        dto.setCcm(reqModel.getCcm());
        dto.setCcmc(reqModel.getCcmc());
        dto.setRcjzsj(reqModel.getRcjzsj());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setZc(reqModel.getZc());
        dto.setKmList(reqModel.getKmList());

		ksKsccService.ksccbj(dto);
        KsccbjRespModel respModel = new KsccbjRespModel();

		log.info("处理请求结束：[考试场次编辑][/manager/identityverify/ksgl/ksccbj]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考生导入前科目名称核验
    * @param reqModel
    * @return
    */
//    @PostMapping(value = "/manager/identityverify/ksgl/ksdrqhy")
//    public Wrapper<KsdrqhyRespModel> ksdrqhy( @RequestPart(value = "file", required = false) MultipartFile file) {
//		log.info("收到请求开始：[考生导入前科目名称核验][/manager/identityverify/ksgl/ksdrqhy]");
//        String path = rootPath+File.separator + _xlsxFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
//                DateUtil.FULL_DATETIMEFORMAT) + File.separator;
//        String filePath = path + file.getOriginalFilename();
//        File zipFile = new File(filePath);
//        if (!zipFile.getParentFile().exists()){
//            zipFile.getParentFile().mkdirs();
//        }
//        try {
//            file.transferTo(zipFile);
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//		KsdrqhyVO result = xxwjdrService.ksdrqhy(zipFile);
//        KsdrqhyRespModel respModel = new KsdrqhyRespModel();
//        respModel.setHyjg(result.getHyjg());
//		log.info("处理请求结束：[考生导入前科目名称核验][/manager/identityverify/ksgl/ksdrqhy]respModel:"+respModel.toString());
//        return WrapMapper.ok(respModel);
//    }
   /**
    * 考试场次详情
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ksccxq")
    @PostMapping(value = "/manager/identityverify/ksgl/ksccxq")
    public Wrapper<KsccxqRespModel> ksccxq(@RequestBody KsccxqReqModel reqModel) {
		log.info("收到请求开始：[考试场次详情][/manager/identityverify/ksgl/ksccxq]reqModel:"+reqModel.toString());
		KsccxqDTO dto = new KsccxqDTO();
        dto.setCcm(reqModel.getCcm());
        dto.setKsjhbh(reqModel.getKsjhbh());

		KsccxqVO result = ksKsccService.ksccxq(dto);
        KsccxqRespModel respModel = new KsccxqRespModel();
        respModel.setCcm(result.getCcm());
        respModel.setCcmc(result.getCcmc());
        respModel.setRcjzsj(result.getRcjzsj());
        respModel.setKsjhbh(result.getKsjhbh());
        respModel.setZc(result.getZc());
        respModel.setKssj(result.getKssj());
        respModel.setJssj(result.getJssj());
        respModel.setKmList(result.getKmList());
		log.info("处理请求结束：[考试场次详情][/manager/identityverify/ksgl/ksccxq]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 改变考试计划默认状态
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/gbksjhmrzt")
    public Wrapper<GbksjhmrztRespModel> gbksjhmrzt(@RequestBody GbksjhmrztReqModel reqModel) {
		log.info("收到请求开始：[改变考试计划默认状态][/manager/identityverify/ksgl/gbksjhmrzt]reqModel:"+reqModel.toString());
		GbksjhmrztDTO dto = new GbksjhmrztDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setSfmr(reqModel.getSfmr());
		ksKsjhService.gbksjhmrzt(dto);
        GbksjhmrztRespModel respModel = new GbksjhmrztRespModel();

		log.info("处理请求结束：[改变考试计划默认状态][/manager/identityverify/ksgl/gbksjhmrzt]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 入场信息初始化
    * @param reqModel
    * @return
    */
	@Permission("ksgl:rcxxcsh")
    @PostMapping(value = "/manager/identityverify/ksgl/rcxxcsh")
    public Wrapper<RcxxcshRespModel> rcxxcsh(@RequestBody RcxxcshReqModel reqModel) {
		log.info("收到请求开始：[入场信息初始化][/manager/identityverify/ksgl/rcxxcsh]reqModel:"+reqModel.toString());

        String ksjhbh = reqModel.getKsjhbh();
        String rylx = reqModel.getRylx();
        if ("1".equals(rylx)){
            ksJkryRcxxService.jkrcxxcsh(ksjhbh);
        } else if ("2".equals(rylx)){
            ksKsrcxxService.ksrcxxcsh(ksjhbh);
        } else {
            throw new IdentityVerifyException("rylx: 人员类型参数非法");
        }

        RcxxcshRespModel respModel = new RcxxcshRespModel();

		log.info("处理请求结束：[入场信息初始化][/manager/identityverify/ksgl/rcxxcsh]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试计划定时下发配置
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ksjhdsxfpz")
   @PostMapping(value = "/manager/identityverify/ksgl/ksjhdsxfpz")
   public Wrapper<KsjhdsxfpzRespModel> ksjhdsxfpz(@RequestBody KsjhdsxfpzReqModel reqModel) {
       log.info("收到请求开始：[考试计划定时下发配置][/manager/identityverify/ksgl/ksjhdsxfpz]reqModel:" + reqModel.toString());
       KsjhdsxfpzDTO dto = new KsjhdsxfpzDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       dto.setQydsxf(reqModel.getQydsxf());
       dto.setDsxfsj(reqModel.getDsxfsj());
       ksKsjhService.ksjhdsxfpz(dto);
       KsjhdsxfpzRespModel respModel = new KsjhdsxfpzRespModel();
       log.info("处理请求结束：[考试计划定时下发配置][/manager/identityverify/ksgl/ksjhdsxfpz]reqModel:" + reqModel.toString()
               + ",respModel:" + respModel.toString());
       return WrapMapper.ok(reqModel, respModel);
   }

    /**
     * 考试计划定时下发配置查询
     *
     * @param reqModel
     * @return
     */
	@Permission("ksgl:ksjhdsxfpzcx")
    @PostMapping(value = "/manager/identityverify/ksgl/ksjhdsxfpzcx")
    public Wrapper<KsjhdsxfpzcxRespModel> ksjhdsxfpzcx(@RequestBody KsjhdsxfpzcxReqModel reqModel) {
        log.info("收到请求开始：[考试计划定时下发配置查询][/manager/identityverify/ksgl/ksjhdsxfpzcx]reqModel:" + reqModel.toString());
        KsjhdsxfpzcxDTO dto = new KsjhdsxfpzcxDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        KsjhdsxfpzcxVO result = ksKsjhService.ksjhdsxfpzcx(dto);
        KsjhdsxfpzcxRespModel respModel = new KsjhdsxfpzcxRespModel();
        respModel.setQydsxf(result.getQydsxf());
        respModel.setDsxfsj(result.getDsxfsj());
        log.info("处理请求结束：[考试计划定时下发配置查询][/manager/identityverify/ksgl/ksjhdsxfpzcx]reqModel:" + reqModel.toString()
                + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
   /**
    * 考试计划复核下发任务新建
    * @param reqModel
    * @return
    */
	@Permission("ksgl:ksjhfhxfrwxj")
   @PostMapping(value = "/manager/identityverify/ksgl/ksjhfhxfrwxj")
   public Wrapper<KsjhfhxfrwxjRespModel> ksjhfhxfrwxj(@RequestBody KsjhfhxfrwxjReqModel reqModel) {
       log.info("收到请求开始：[考试计划复核下发任务新建][/manager/identityverify/ksgl/ksjhfhxfrwxj]reqModel:" + reqModel.toString());
       KsjhfhxfrwxjDTO dto = new KsjhfhxfrwxjDTO();
       dto.setKsjhbh(reqModel.getKsjhbh());
       dto.setSfcxdb(reqModel.getSfcxdb());
       dto.setType(reqModel.getType());
       dto.setDsrwsj(reqModel.getDsrwsj());
       ksKsjhService.ksjhfhxfrwxj(dto);
       KsjhfhxfrwxjRespModel respModel = new KsjhfhxfrwxjRespModel();
       log.info("处理请求结束：[考试计划复核下发任务新建][/manager/identityverify/ksgl/ksjhfhxfrwxj]reqModel:" + reqModel.toString()
               + ",respModel:" + respModel.toString());
       return WrapMapper.ok(reqModel, respModel);
   }

   /**
    * 考场教室对应关系完整核验
    * @param reqModel
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/kcjsdygxwzhy")
    public Wrapper<KcjsdygxwzhyRespModel> kcjsdygxwzhy(@RequestBody KcjsdygxwzhyReqModel reqModel) {
		log.info("收到请求开始：[考场教室对应关系完整核验][/manager/identityverify/ksgl/kcjsdygxwzhy]reqModel:"+reqModel.toString());
		KcjsdygxwzhyDTO dto = new KcjsdygxwzhyDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        dto.setSffzdqtcc(reqModel.getSffzdqtcc());
        dto.setYfpkchList(reqModel.getYfpkchList());
        KcjsdygxwzhyVO result = ksKcxxService.kcjsdygxwzhy(dto);
        KcjsdygxwzhyRespModel respModel = new KcjsdygxwzhyRespModel();
        respModel.setStatus(result.getStatus());
        respModel.setWdywcccList(result.getWdywcccList());
		log.info("处理请求结束：[考场教室对应关系完整核验][/manager/identityverify/ksgl/kcjsdygxwzhy]reqModel:"+reqModel.toString()
			+",respModel:"+respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }

   /**
    * 考生照片包导入
    * @param
    * @return
    */
    @PostMapping(value = "/manager/identityverify/ksgl/kszpbdr")
    public Wrapper<Void> kszpbdr(@RequestPart(value = "file") MultipartFile file, @RequestParam(value = "ksjhbh") String ksjhbh) {
        log.info("收到请求开始：[考生照片包导入][/manager/identityverify/ksgl/kszpbdr]ksjhbh:" + ksjhbh);
        ksBmxxService.kszpbdr(file, ksjhbh);
        log.info("处理请求结束：[考生照片包导入][/manager/identityverify/ksgl/kszpbdr]ksjhbh:" + ksjhbh);
        return WrapMapper.ok();
    }
    @PostMapping(value = "/manager/identityverify/ksgl/cxjkrylb")
    public Wrapper<CxjkrylbRespModel> cxjkrylb(@RequestBody CxjkrylbReqModel reqModel) {
        log.info("收到请求开始：[查询监考人员列表][/manager/identityverify/ksgl/cxjkrylb]reqModel:" + reqModel.toString());
        CxjkrylbDTO dto = new CxjkrylbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setXm(reqModel.getXm());
        dto.setXbm(reqModel.getXbm());
        dto.setSfzh(reqModel.getSfzh());
        dto.setZp(reqModel.getZp());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        CxjkrylbVO result = ksJkryJbxxService.cxjkrylb(dto);
        CxjkrylbRespModel respModel = new CxjkrylbRespModel();
        respModel.setTzzqy(result.getTzzqy());
        respModel.setJkrylb(result.getJkrylb());
        respModel.setPageNum(result.getPageNum());
        respModel.setPageSize(result.getPageSize());
        respModel.setTotalRows(result.getTotalRows());
        log.info("处理请求结束：[查询监考人员列表][/manager/identityverify/ksgl/cxjkrylb]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    @PostMapping(value = "/manager/identityverify/ksgl/hqjkryxq")
    public Wrapper<HqjkryxqRespMopdel> hqjkryxq(@RequestBody HqjkryxqReqModel reqModel) {
        log.info("收到请求开始：[获取监考人员详情][/manager/identityverify/ksgl/hqjkryxq]reqModel:" + reqModel.toString());
        String jkrybzid = reqModel.getJkrybzid();

        KsJkryJbxx ksJkryJbxx;
        if(StringUtils.isNotBlank(jkrybzid)){
            ksJkryJbxx = ksJkryJbxxService.selectSingleByKey(jkrybzid);
        }else{
            Example example = new Example(KsJkryJbxx.class);
            example.createCriteria().andEqualTo("ksjhdm", reqModel.getKsjhbh())
                    .andEqualTo("zjh", reqModel.getSfzh());
            List<KsJkryJbxx> ksJkryJbxxes = ksJkryJbxxService.selectListByExample(example);
            if (CollectionUtils.isEmpty(ksJkryJbxxes)) {
                return WrapMapper.ok(reqModel, new HqjkryxqRespMopdel());
            }
            ksJkryJbxx = ksJkryJbxxes.get(0);
        }
        String zjh = ksJkryJbxx.getZjh();
        // 对证件号进行脱敏
        zjh = HsUtils.EncryptionSFZH(zjh);
        String zp = ksJkryJbxx.getZp();
        if (StringUtils.isNotBlank(zp)) {
            TimePathVO timePathVO = XcDfsClient.timePath(2L, 0, zp);
            List<FileItemVO> fileList = timePathVO.getFileList();
            if (CollectionUtils.isNotEmpty(fileList)) {
                FileItemVO fileItemVO = fileList.get(0);
                String url = fileItemVO.getUrl();
                zp = dfsServer + "/remote/file/" + url;

            }
        }

        HqjkryxqRespMopdel respModel = new HqjkryxqRespMopdel();
        respModel.setJkrybzid(ksJkryJbxx.getJkrybzid());
        respModel.setXm(ksJkryJbxx.getXm());
        respModel.setXbm(ksJkryJbxx.getXbm());
        respModel.setSfzh(zjh);
        respModel.setZpdz(zp);


        log.info("处理请求结束：[获取监考人员详情][/manager/identityverify/ksgl/hqjkryxq]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    @PostMapping(value = "/manager/identityverify/ksgl/xzjkry")
    public Wrapper<XzjkryRespModel> xzjkry(@RequestParam( name ="ksjhbh", required = true) String ksjhbh,
                                @RequestParam( name ="xm", required = true) String xm,
                                @RequestParam( name ="xbm", required = true) String xbm,
                                @RequestParam( name ="sfzh", required = true) String sfzh,
                                @RequestPart( name = "zp", required = false) MultipartFile zp
                                ){
        log.info("收到请求开始：[新增监考人员][/manager/identityverify/ksgl/xzjkry]ksjhbh:" + ksjhbh);
        KsJkryJbxx ksJkryJbxx = ksJkryJbxxService.selectJkryBySfzh(ksjhbh,sfzh);
        if (ksJkryJbxx != null) {
            throw new IdentityVerifyException("该考试计划下该监考人员已存在");
        }
        String zpid = null;
        String zpdz = null;
        if(zp != null && !zp.isEmpty()){
            String path = rootPath + File.separator + _jkryFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
                    DateUtil.FULL_DATETIMEFORMAT) + File.separator;
            String filePath = path + zp.getOriginalFilename();
            File zpFile = new File(filePath);
            if (!zpFile.getParentFile().exists()) {
                zpFile.getParentFile().mkdirs();
            }
            try {
                zp.transferTo(zpFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            UploadVO uploadVO = XcDfsClient.uploadStream(filePath);
            List<UploadItemVO> list = uploadVO.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                UploadItemVO uploadItemVO = list.get(0);
                zpid = uploadItemVO.getId();
                zpdz = dfsServer + "/remote/file/" + uploadItemVO.getFilePath();
            }
            // 删除本地文件
            zpFile.delete();
        }
        XzjkryDTO xzjkryDTO = new XzjkryDTO();
        xzjkryDTO.setKsjhbh(ksjhbh);
        xzjkryDTO.setXm(xm);
        xzjkryDTO.setXbm(xbm);
        xzjkryDTO.setSfzh(sfzh);
        xzjkryDTO.setZpid(zpid);
        XzjkryVO xzjkry = ksJkryJbxxService.xzjkry(xzjkryDTO);
        XzjkryRespModel xzjkryRespModel = new XzjkryRespModel();
        xzjkryRespModel.setJkrybzid(xzjkry.getJkrybzid());
        xzjkryRespModel.setKsjhbh(xzjkry.getKsjhbh());
        xzjkryRespModel.setXm(xzjkry.getXm());
        xzjkryRespModel.setXbm(xzjkry.getXbm());
        xzjkryRespModel.setSfzh(xzjkry.getSfzh());
        xzjkryRespModel.setZpdz(zpdz);
        log.info("处理请求结束：[新增监考人员][/manager/identityverify/ksgl/xzjkry]ksjhbh:" + ksjhbh + ",respModel:" + xzjkryRespModel.toString());
        return WrapMapper.ok(xzjkryRespModel);
    }
    @PostMapping(value = "/manager/identityverify/ksgl/jkryxxxgzp")
    public Wrapper<Void> jkryxxxgzp(@RequestParam (name = "jkrybzid", required = true) String jkrybzid,
                                    @RequestPart(name = "zp", required = true) MultipartFile zp) {
        log.info("收到请求开始：[监考人员信息修改(仅更新照片)][/manager/identityverify/ksgl/jkryxxxgzp]jkrybzid:" + jkrybzid);
        String zpid = null;
        if (zp != null && !zp.isEmpty()) {
            // 检查文件类型, 这里只允许上传图片
            String contentType = zp.getContentType();
            if (!contentType.startsWith("image/")) {
                throw new IdentityVerifyException("上传的文件类型不正确, 只允许上传图片");
            }
            String path = rootPath + File.separator + _jkryFileRootPath + DateUtil.format(new Date(System.currentTimeMillis()),
                    DateUtil.FULL_DATETIMEFORMAT) + File.separator;
            String filePath = path + zp.getOriginalFilename();
            File zpFile = new File(filePath);
            if (!zpFile.getParentFile().exists()) {
                zpFile.getParentFile().mkdirs();
            }
            try {
                zp.transferTo(zpFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            UploadVO uploadVO = XcDfsClient.uploadStream(filePath);
            List<UploadItemVO> list = uploadVO.getList();
            if (CollectionUtils.isNotEmpty(list)) {
                UploadItemVO uploadItemVO = list.get(0);
                zpid = uploadItemVO.getId();
                Example example = new Example(KsJkryJbxx.class);
                example.createCriteria().andEqualTo("jkrybzid", jkrybzid);
                KsJkryJbxx ksJkryJbxx = new KsJkryJbxx();
                ksJkryJbxx.setZp(zpid);
                ksJkryJbxx.setUpdateTime(new Date());
                ksJkryJbxxService.updateByExampleSelective(ksJkryJbxx, example);
            }
            // 删除本地文件
            zpFile.delete();
        }
        log.info("处理请求结束：[监考人员信息修改(仅更新照片)][/manager/identityverify/ksgl/jkryxxxgzp]jkrybzid:" + jkrybzid);
        return WrapMapper.ok();
    }
    // 监考人员信息删除
    @PostMapping(value = "/manager/identityverify/ksgl/jkryxxsc")
    public Wrapper<Void> jkryxxsc(@RequestBody JkryxxscReqModel reqModel) {
        log.info("收到请求开始：[监考人员信息删除][/manager/identityverify/ksgl/jkryxxsc]reqModel:" + reqModel.toString());
        List<String> jkrybzidList = reqModel.getJkrybzidList();
        ksJkryJbxxService.jkryxxsc(jkrybzidList);
        log.info("处理请求结束：[监考人员信息删除][/manager/identityverify/ksgl/jkryxxsc]reqModel:" + reqModel.toString());
        return WrapMapper.ok();
    }
    @PostMapping(value = "/manager/identityverify/ksgl/cxjkbplb")
    public Wrapper<CxjkbplbRespModel> cxjkbplb(@RequestBody CxjkbplbReqModel reqModel) {
        log.info("收到请求开始：[查询监考编排列表][/manager/identityverify/ksgl/cxjkbplb]reqModel:" + reqModel.toString());
        CxjkbplbDTO dto = new CxjkbplbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setXm(reqModel.getXm());
        dto.setSfzh(reqModel.getSfzh());
        dto.setCcm(reqModel.getCcm());
        dto.setGwzzm(reqModel.getGwzzm());
        dto.setKch(reqModel.getKch());
        dto.setPageNum(reqModel.getPageNum());
        dto.setPageSize(reqModel.getPageSize());
        JkrybpListVO result = ksJkryBpxxService.cxjkbplb(dto);
        CxjkbplbRespModel respModel = new CxjkbplbRespModel();
        respModel.setJkrybpList(result.getJkrybpList());
        respModel.setTotalRows(result.getTotalRows());
        log.info("处理请求结束：[查询监考编排列表][/manager/identityverify/ksgl/cxjkbplb]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    @PostMapping(value = "/manager/identityverify/ksgl/cxmccxkclb")
    public Wrapper<CxmccxkclbRespModel> cxmccxkclb(@RequestBody CxmccxkclbReqModel reqModel) {
        log.info("收到请求开始：[查询某场次下考场列表][/manager/identityverify/ksgl/cxmccxkclb]reqModel:" + reqModel.toString());
        CxmccxkclbDTO dto = new CxmccxkclbDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());
        List<CxmccxkclbItemVO> kclb = ksKcxxService.cxmccxkclb(dto.getKsjhbh(), dto.getCcm());
        CxmccxkclbRespModel respModel = new CxmccxkclbRespModel();
        respModel.setKclb(kclb);
        log.info("处理请求结束：[查询某场次下考场列表][/manager/identityverify/ksgl/cxmccxkclb]reqModel:" + reqModel.toString() + ",respModel:" + respModel.toString());
        return WrapMapper.ok(reqModel, respModel);
    }
    @PostMapping(value = "/manager/identityverify/ksgl/jkrybptj")
    public Wrapper<Void> jkrybptj(@RequestBody JkrybptjReqModel reqModel) {
        log.info("收到请求开始：[监考人员编排添加][/manager/identityverify/ksgl/jkrybptj]reqModel:" + reqModel.toString());
        JkrybptjDTO dto = new JkrybptjDTO();
//        dto.setCcmList(reqModel.getCcmList());
        dto.setGwzzm(reqModel.getGwzzm());
        dto.setKcidList(reqModel.getKcidList());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setSfzh(reqModel.getSfzh());
        dto.setXm(reqModel.getXm());
        dto.setXbm(reqModel.getXbm());
        dto.setCcm(reqModel.getCcm());
        ksJkryBpxxService.validateJkryGwAndKc(dto.getGwzzm(), dto.getKcidList());
        if(CollectionUtils.isEmpty(reqModel.getKcidList())){
            ksJkryBpxxService.jkrybptjdcc(dto);
        }else{
            ksJkryBpxxService.jkrybptj(dto);
        }

        log.info("处理请求结束：[监考人员编排添加][/manager/identityverify/ksgl/jkrybptj]reqModel:" + reqModel.toString());
        return WrapMapper.ok();
    }
    @PostMapping(value = "/manager/identityverify/ksgl/jkrybpxg")
    public Wrapper<Void> jkrybpxg(@RequestBody JkrybpxgReqModel reqModel) {
        log.info("收到请求开始：[监考人员编排修改][/manager/identityverify/ksgl/jkrybpxg]reqModel:" + reqModel.toString());
        JkrybpxgDTO dto = new JkrybpxgDTO();
        dto.setJkrybpbzid(reqModel.getJkrybpbzid());
        dto.setGwzzm(reqModel.getGwzzm());
        dto.setKcidList(reqModel.getKcidList());
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setCcm(reqModel.getCcm());

        ksJkryBpxxService.jkrybpxg(dto);

        log.info("处理请求结束：[监考人员编排修改][/manager/identityverify/ksgl/jkrybpxg]reqModel:" + reqModel.toString());
        return WrapMapper.ok();
    }
    @PostMapping(value = "/manager/identityverify/ksgl/jkrybpsc")
    public Wrapper<Void> jkrybpsc(@RequestBody JkrybpscReqModel reqModel) {
        log.info("收到请求开始：[监考人员编排删除][/manager/identityverify/ksgl/jkrybpsc]reqModel:" + reqModel.toString());
        JkrybpscDTO dto = new JkrybpscDTO();
        dto.setJkrybpidList(reqModel.getJkrybpidList());

        ksJkryBpxxService.jkrybpplsc(dto.getJkrybpidList());

        log.info("处理请求结束：[监考人员编排删除][/manager/identityverify/ksgl/jkrybpsc]reqModel:" + reqModel.toString());
        return WrapMapper.ok();
    }
    @PostMapping(value = "/manager/identityverify/ksgl/jkrybpbcbfzdsycc")
    public Wrapper<Void> jkrybpbcbfzdsycc(@RequestBody JkrybpbcbfzdsyccReqModel reqModel) {
        log.info("收到请求开始：[监考人员编排保存并复制到所有场次][/manager/identityverify/ksgl/jkrybpbcbfzdsycc]reqModel:" + reqModel.toString());
        JkrybpbcbfzdsyccDTO dto = new JkrybpbcbfzdsyccDTO();
        dto.setKsjhbh(reqModel.getKsjhbh());
        dto.setSfzh(reqModel.getSfzh());
        dto.setGwzzm(reqModel.getGwzzm());
        dto.setKchList(reqModel.getKchList());
        dto.setXm(reqModel.getXm());
        dto.setXbm(reqModel.getXbm());
        dto.setCcm(reqModel.getCcm());
        String jkrybpbzid = reqModel.getJkrybpbzid();
        if(StringUtils.isNotBlank(jkrybpbzid)){
            KsJkryBpxx ksJkryBpxx = ksJkryBpxxService.selectSingleByKey(jkrybpbzid);
            if (ksJkryBpxx == null) {
                throw new IdentityVerifyException("监考人员编排不存在");
            }
            Example jkryJbxxExample = new Example(KsJkryJbxx.class);
            jkryJbxxExample.createCriteria().andEqualTo("ksjhdm", reqModel.getKsjhbh())
                    .andEqualTo("zjh", ksJkryBpxx.getGrbsm());
            List<KsJkryJbxx> ksJkryJbxxList = ksJkryJbxxService.selectListByExample(jkryJbxxExample);
            if (CollectionUtils.isEmpty(ksJkryJbxxList)) {
                throw new IdentityVerifyException("监考人员不存在");
            }
            KsJkryJbxx ksJkryJbxx = ksJkryJbxxList.get(0);
            dto.setSfzh(ksJkryJbxx.getZjh());
            dto.setXm(ksJkryJbxx.getXm());
            dto.setXbm(ksJkryJbxx.getXbm());

        }

        ksJkryBpxxService.jkrybpbcbfzdsycc(dto);

        log.info("处理请求结束：[监考人员编排保存并复制到所有场次][/manager/identityverify/ksgl/jkrybpbcbfzdsycc]reqModel:" + reqModel.toString());
        return WrapMapper.ok();
    }
}