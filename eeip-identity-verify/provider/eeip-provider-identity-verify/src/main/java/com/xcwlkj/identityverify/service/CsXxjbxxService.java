/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service;

import com.xcwlkj.identityverify.model.domain.CsXxjbxx;
import com.xcwlkj.identityverify.model.dos.KdAndJsxxDO;
import com.xcwlkj.identityverify.model.dto.sbzc.CxkdxxbyjgbhDTO;
import com.xcwlkj.identityverify.model.dto.sysconfig.BxxxpzDTO;
import com.xcwlkj.identityverify.model.dto.sysconfig.HqbxpzxxDTO;
import com.xcwlkj.identityverify.model.vo.jcxx.XqlbItemVO;
import com.xcwlkj.identityverify.model.vo.sbzc.CxkdxxbyjgbhVO;
import com.xcwlkj.identityverify.model.vo.sysconfig.HqbxpzxxVO;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 学校基本信息服务
 * <AUTHOR>
 * @version $Id: CsXxjbxxService.java, v 0.1 2023年09月19日 17时13分 xcwlkj.com Exp $
 */
@Service
public interface CsXxjbxxService extends BaseService<CsXxjbxx> {


    /**
     * 获取学校场所列表
     * @auther: kkn
     */
    List<XqlbItemVO> getCslb();

    CxkdxxbyjgbhVO cxkdxxbyjgbh(CxkdxxbyjgbhDTO dto);

    CxkdxxbyjgbhVO cxkdxxbyjgbhCc(CxkdxxbyjgbhDTO dto);

    KdAndJsxxDO getKdAndJsxxByJgdm(String jgdm);

    /**
     * 根据场所编号和场所类型获取标准化考场id列表
     * @param csbh
     * @param cslx
     * @return
     * @auther: kkn
     */
    List<String> getBzhkcidsByCsbhAndCslx(String csbh, String cslx);

    /**
     * 获取当前所在学校的bzhkdid
     * @return
     */
    String getBzhkdid();

    /**
     * 本校信息配置
     * @param dto
     */
    void bxxxpz(BxxxpzDTO dto);

    /**
     * 获取本校配置信息
     * @param dto
     * @return
     */
    HqbxpzxxVO hqbxpzxx(HqbxpzxxDTO dto);

    List<CsXxjbxx> selectAll();
}