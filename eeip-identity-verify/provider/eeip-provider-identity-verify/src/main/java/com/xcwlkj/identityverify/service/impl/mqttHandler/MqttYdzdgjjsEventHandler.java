package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xcwlkj.biz.unifyaccess.mqtt.service.IMessageService;
import com.xcwlkj.identityverify.model.domain.SbSbxx;
import com.xcwlkj.identityverify.service.SbSbxxService;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttFuncInvokeEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.AppGjjsVO;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeAppGjjsVO;
import com.xcwlkj.identityverify.util.HsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class MqttYdzdgjjsEventHandler  extends MqttBaseEventHandler {

    @Autowired
    private IMessageService messageService;
    @Resource
    private SbSbxxService sbSbxxService;

    @Override
    public void handleFuncInvoke(String productId, String deviceId, MqttFuncInvokeEnum funcInvokeEnum, String msg){
        ObjectMapper objectMapper = new ObjectMapper();
        String status = "-1";
        AppGjjsVO appGjjsVO = null;
        try {
            FunctionInvokeAppGjjsVO functionInvokeVO = objectMapper.readValue(msg, FunctionInvokeAppGjjsVO.class);
            appGjjsVO = functionInvokeVO.formatToAppGjjsVO();

        } catch (JsonProcessingException e) {
            e.printStackTrace();
            log.error(e.getMessage(),e);
        } catch (Exception e){
            log.error(e.getMessage(),e);
        }
        if(appGjjsVO!=null){
            String sendMsg = buildResult(status,appGjjsVO);
            doSendUpperHsPlatYdzdgjjsResult(sendMsg,funcInvokeEnum);
        }
        if(StringUtils.equals(status,"1")){
            //下载成功以后成功以后进行分发，即发送获取移动终端app固件版本消息到考场网关
//            sbSbxxService.selectByExample()
        }
    }

    @Override
    protected HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {
        return null;
    }

    @Override
    protected MqttUnifyAccessEventHandlerEnum getEventHandlerType() {
        return MqttUnifyAccessEventHandlerEnum.YDZDGJJS_EVENT_HANDLER;
    }

    public void doSendUpperHsPlatYdzdgjjsResult(String msg,MqttFuncInvokeEnum devEventEnum){
        log.info("发送移动终端固定下载完成消息到上级平台[{}]",msg);
        messageService.sendToMqtt(msg, HsUtils.buildEventTopic(devEventEnum.getCode()));
    }

    /**
     * {
     *   "headers": {
     *     "productId": "CENCGW100_S",
     *     "_uid": "1947928233098633216"
     *   },
     *   "data": {
     *     "TIMESTAMP": "1753257256000",
     *     "Data": {
     *       "messageId": "1947927612782043136",
     *       "orgCode": "K330100030",
     *       "sn": "CENCGW800_S03MC11BDJ000099",
     *       "devType": "170",
     *       "version": "1.8.9",
     *       "versionOrder": "61",
     *       "status": "1",
     *       "parameters": {
     *         "packageName": "com.hisome.gxicv"
     *       }
     *     },
     *     "OPERATION": "NOTIFY_XFYDZDGJ"
     *   },
     *   "messageType": "EVENT",
     *   "sign": "037C59B073508117022771A1DC7BA48F210BDDC2AE856AC4CA969E4E38F49607F5B8A79C9648EC6F2AF7BD8E5F5ED115883B917DBB53BEF8F229E36E14BCF615",
     *   "event": "XFYDZDGJ",
     *   "deviceId": "CENCGW800_S03MC11BDJ000099",
     *   "token": "fd91c28806b94546a1f7614c20c9ffcc",
     *   "timestamp": 1753257256000
     * }
     * 装配填入data、deviceId、timestamp
     * @param status
     * @param appGjjsVO
     */
    public String buildResult(String status,AppGjjsVO appGjjsVO){

        return "";
    }
}