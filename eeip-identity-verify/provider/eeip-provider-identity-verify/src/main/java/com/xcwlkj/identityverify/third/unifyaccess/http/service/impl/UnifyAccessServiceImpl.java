package com.xcwlkj.identityverify.third.unifyaccess.http.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.JetlinksWrapper;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.enums.UnifyAccessUrlEnum;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.req.*;
import com.xcwlkj.identityverify.third.unifyaccess.http.model.resp.*;
import com.xcwlkj.identityverify.third.unifyaccess.http.service.UnifyAccessService;
import com.xcwlkj.msgque.model.domain.RespBaseModel;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.ResolvableType;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 统一接入平台 处理类
 * <AUTHOR>
 */
@Service("idvUnifyAccessService")
@Slf4j
public class UnifyAccessServiceImpl implements UnifyAccessService {

	private RestTemplate restTemplate = new RestTemplate();
	@Value("${xc.tyjr.urlPrefix}")
	String urlPrefix;
	@Value("${xc.tyjr.appId}")
	String appId;
	@Value("${xc.tyjr.accessKey}")
	String accessKey;
	@Autowired
	private RedisUtil redisUtil;
	
	private String platKey = "unifyAccess:idvPlatKey";
	
	private String HTTP_HEADERS_TOKEN = "token";
	
	private int CODE_PLAT_TOKEN_EXPIRE = 80000002;
	
	private long expire_time = 3600*3;

	//jetlinks配置
	private String jetlinksApiKey = "unifyAccess:idvPlatKeyJetlinksApiKey";
	@Value("${xc.tyjr.jetlinksUsername:sfhypt}")
	private String jetlinksUsername;
	@Value("${xc.tyjr.jetlinksPassword:sfhypt@2022}")
	private String jetlinksPassword;

	@Override
	public Wrapper<PlatLoginRespModel> platLogin(PlatLoginReqModel login) {
		String url = urlPrefix+ UnifyAccessUrlEnum.login.getCode();
		if(StringUtils.isBlank(login.getAppId())||StringUtils.isBlank(login.getAccessKey())) {
			log.warn("登录参数为空使用默认appId[{}],accessKey[{}]",appId,accessKey);
			login.setAccessKey(accessKey);
			login.setAppId(appId);
		}
		log.info("url[{}]参数[{}]",url,login);
		ResponseEntity<Wrapper<PlatLoginRespModel>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
                        new HttpEntity<PlatLoginReqModel>(login),
                        new ParameterizedTypeReference<Wrapper<PlatLoginRespModel>>() {
                        });
		log.info("url[{}]响应[{}]",url,responseEntity);
		return responseEntity.getBody();
	}

	@Override
	public Wrapper<RemoteRespBaseModel> deviceValidate(DeviceValidateReqModel deviceValidate) {
		String url = urlPrefix+UnifyAccessUrlEnum.validate.getCode();
		Wrapper<RemoteRespBaseModel> result = buildCommonPost(deviceValidate, RemoteRespBaseModel.class, url);
		return result;
	}

	@Override
	public Wrapper<RespBaseModel> sendCommand(SendCommandReqModel reqModel) {
		String url = urlPrefix+UnifyAccessUrlEnum.sendCommand.getCode();
		if(StringUtils.isBlank(reqModel.getAppIdSrc())) {
			reqModel.setAppIdSrc(appId);
		}
		Wrapper<RespBaseModel> result = buildCommonPost(reqModel, RespBaseModel.class, url);
		return result;
	}

	@Override
	public Wrapper<RemoteRespBaseModel> batchCommand(BatchCommandReqModel reqModel) {
		String url = urlPrefix+UnifyAccessUrlEnum.batchCommand.getCode();
		if(StringUtils.isBlank(reqModel.getAppIdSrc())) {
			reqModel.setAppIdSrc(appId);
		}
		Wrapper<RemoteRespBaseModel> result = buildCommonPost(reqModel, RemoteRespBaseModel.class, url);
		return result;
	}

	@Override
	public Wrapper<BatchCommandResultRespModel> queryBatchCommandResult(String msgId) {
		String url = urlPrefix+UnifyAccessUrlEnum.queryBatchCommandResult.getCode();
		BatchCommandResultReqModel reqModel = new BatchCommandResultReqModel(msgId);
		Wrapper<BatchCommandResultRespModel> result = buildCommonPost(reqModel, BatchCommandResultRespModel.class, url);
		return result;
	}
	
	@Override
	public Wrapper<DeviceStatusRespModel> queryDeviceStatus(String... devNames) {
		String url = urlPrefix+UnifyAccessUrlEnum.queryDeviceStatus.getCode();
		DeviceStatusReqModel reqModel = new DeviceStatusReqModel();
		List<String> list = Arrays.asList(devNames);
		reqModel.setDeviceNames(list);
		Wrapper<DeviceStatusRespModel> result = buildCommonPost(reqModel, DeviceStatusRespModel.class, url);
		return result;
	}
	
	protected <T,U> Wrapper<U> buildCommonPost(T reqModel,Class<U> clazz,String url){
		log.info("url[{}]参数[{}]",url,reqModel);
		int flag =0;
		Wrapper<U> result = null;
		while (flag<2) {
			try{
				ResponseEntity<Wrapper<U>> responseEntity = restTemplate.exchange(url,
						HttpMethod.POST, new HttpEntity<T>(reqModel, buildTokenHeaders()),
						new ParameterizedTypeReference<Wrapper<U>>() {
						});
				if(responseEntity.getBody().getCode()==CODE_PLAT_TOKEN_EXPIRE) {
					flag++;
					redisUtil.del(platKey);
				}else {
					result = responseEntity.getBody();
					log.info("发送给接入返回结果result={}", result);
					if(result.getResult()!=null){
//						U resultSub = JSON.parseObject(JSON.toJSONString(result.getResult()), clazz);
						U resultSub = result.getResult();
						result.setResult(resultSub);
					}
					break;
				}
			}catch (Exception e){
				e.printStackTrace();
				log.error(e.getMessage(), e);
				String emsg = e.getMessage();
				//redis中key过期
				if(StringUtils.contains(emsg, "400 Bad Request")){
					redisUtil.del(platKey);
				}
			}finally {
				flag++;
			}
		}
		return result;
	}

	protected String getToken() {
		String token="";
		Object val = redisUtil.get(platKey);
		if(val==null) {
			Wrapper<PlatLoginRespModel> result = platLogin(new PlatLoginReqModel());
			token = result.getResult().getToken();
			redisUtil.set(platKey, token, expire_time);
		}else {
			token = (String)val;
		}
		return token;
	}
	
	protected HttpHeaders buildTokenHeaders() {
		String token = getToken();
		HttpHeaders headers = new HttpHeaders();
		headers.add(HTTP_HEADERS_TOKEN, token);
		return headers;
	}

	@Override
	public boolean deviceValidate(String token) {
		boolean flag = false;
		DeviceValidateReqModel deviceValidate = new DeviceValidateReqModel();
		deviceValidate.setDeviceToken(token);
		Wrapper<RemoteRespBaseModel> result = this.deviceValidate(deviceValidate);
		if(result!=null&&result.getCode()==200) {
			flag = true;
		}else {
			log.warn("设备验签失败[{}]",result);
		}
		return flag;
	}

	@Override
	public Wrapper<RespBaseModel> sendCommand(String sn, String data) {
		SendCommandReqModel reqModel = new SendCommandReqModel();
    	reqModel.setAppIdTarget(sn);
    	reqModel.setData(data);
    	Wrapper<RespBaseModel> res  = sendCommand(reqModel);
		return res;
	}

	@Override
	public JetlinksWrapper<JetlinksLoginRespModel> jetlinksLogin(JetlinksLoginReqModel login) {
		String url = urlPrefix+UnifyAccessUrlEnum.jetlinksLogin.getCode();
		if(StringUtils.isBlank(login.getUsername())||StringUtils.isBlank(login.getPassword())) {
			log.warn("jetlinks登录参数为空使用默认jetlinksUsername[{}],jetlinksPassword[{}]",jetlinksUsername,jetlinksPassword);
			login.setUsername(jetlinksUsername);
			login.setPassword(jetlinksPassword);
		}
		log.info("url[{}]参数[{}]",url,login);
		ResponseEntity<JetlinksWrapper<JetlinksLoginRespModel>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
				new HttpEntity<JetlinksLoginReqModel>(login),
				new ParameterizedTypeReference<JetlinksWrapper<JetlinksLoginRespModel>>() {
				});
		return responseEntity.getBody();
	}

	protected <T, U> JetlinksWrapper<JetlinksPageRespModel<U>> jetlinksPageBuildCommonPost(HttpMethod httpMethod, T reqModel, Class<U> clazz, String url) {
		log.info("url[{}]参数[{}]", url, reqModel);
		int flag = 0;
		JetlinksWrapper<JetlinksPageRespModel<U>> result = null;
		while (flag < 2) {
			try {
				// 动态构建完整的泛型类型信息
				ResolvableType innerType = ResolvableType.forClassWithGenerics(JetlinksPageRespModel.class, clazz);
				ResolvableType wrapperType = ResolvableType.forClassWithGenerics(JetlinksWrapper.class, innerType);
				ParameterizedTypeReference<JetlinksWrapper<JetlinksPageRespModel<U>>> typeRef = ParameterizedTypeReference.forType(wrapperType.getType());

				// 发起请求并获取响应
				ResponseEntity<JetlinksWrapper<JetlinksPageRespModel<U>>> responseEntity = restTemplate.exchange(url, httpMethod,
						new HttpEntity<>(reqModel, jetlinksBuildTokenHeaders()),
						typeRef  // 使用动态构建的类型引用
				);

				if (Objects.requireNonNull(responseEntity.getBody(), "请求结果【restTemplate.exchange().getBody()】为空").getStatus() == CODE_PLAT_TOKEN_EXPIRE) {
					flag++;
					redisUtil.del(jetlinksApiKey);
				} else {
					result = responseEntity.getBody();
					log.info("发送给接入返回结果result={}", result);
					break;
				}
			} catch (RestClientException e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				String emsg = e.getMessage();
				//redis中key过期
				if (StringUtils.contains(emsg, "400 Bad Request")) {
					redisUtil.del(jetlinksApiKey);
				}
			} finally {
				flag++;
			}
		}
		return result;
	}

	protected <T, U> JetlinksWrapper<U> jetlinksBuildCommonPost(HttpMethod httpMethod, T reqModel, Class<U> clazz, String url) {
		log.info("url[{}]参数[{}]", url, reqModel);
		int flag = 0;
		JetlinksWrapper<U> result = null;
		while (flag < 2) {
			try {
				// 发起请求并获取响应
				ResponseEntity<JetlinksWrapper<U>> responseEntity = restTemplate.exchange(url, httpMethod,
						new HttpEntity<>(reqModel, jetlinksBuildTokenHeaders()),
						new ParameterizedTypeReference<JetlinksWrapper<U>>() {}
				);

				if (Objects.requireNonNull(responseEntity.getBody(), "请求结果【restTemplate.exchange().getBody()】为空").getStatus() == CODE_PLAT_TOKEN_EXPIRE) {
					flag++;
					redisUtil.del(jetlinksApiKey);
				} else {
					result = responseEntity.getBody();
					log.info("发送给接入返回结果result={}", result);
					break;
				}
			} catch (RestClientException e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				String emsg = e.getMessage();
				//redis中key过期
				if (StringUtils.contains(emsg, "400 Bad Request")) {
					redisUtil.del(jetlinksApiKey);
				}
			} finally {
				flag++;
			}
		}
		return result;
	}

	protected HttpHeaders jetlinksBuildTokenHeaders() {
		String token = jetlinksGetToken();
		HttpHeaders headers = new HttpHeaders();
		headers.add("X-Access-Token", token);
		return headers;
	}

	protected String jetlinksGetToken() {
		String token="";
		Object val = redisUtil.get(jetlinksApiKey);
		if(val==null) {
			JetlinksWrapper<JetlinksLoginRespModel> result = jetlinksLogin(new JetlinksLoginReqModel());
			token = result.getResult().getToken();
			long expire_time = result.getResult().getExpires()/1000-60;
			redisUtil.set(jetlinksApiKey, token, expire_time);
		}else {
			token = (String)val;
		}
		return token;
	}

	@Override
	public JetlinksWrapper<JetlinksPageRespModel<JetlinksDeviceFirewareRespModel>> jetlinksQueryPageDeviceFireware(JetlinksDeviceFirewareReqModel reqModel) {
		String url = urlPrefix+UnifyAccessUrlEnum.jetlinksQueryPageDeviceFireware.getCode();

//		log.info("url[{}]参数[{}]",url,reqModel);
//
//		HttpEntity<JetlinksDeviceFirewareReqModel> httpEntity = new HttpEntity<>(reqModel, jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<JetlinksPageRespModel<JetlinksDeviceFirewareRespModel>>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<JetlinksPageRespModel<JetlinksDeviceFirewareRespModel>>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksPageBuildCommonPost(HttpMethod.POST, reqModel, JetlinksDeviceFirewareRespModel.class, url);
	}

	@Override
	public JetlinksWrapper<JetlinksPageRespModel<DeviceUpGradeStatusRespModel>> jetlinksQueryDeviceFirewareUpgrade(List<String> taskIds, Integer pageNum, Integer pageSize) {
		String url = urlPrefix+UnifyAccessUrlEnum.jetlinksQueryDeviceFirewareUpgrade.getCode();
		url = url + String.format("?pageSize=%d&pageIndex=%d&terms[0].column=taskId&terms[0].termType=in&terms[0].value=%s", pageSize, pageNum, String.join(",", taskIds));

//		log.info("url {}", url);
//
//		HttpEntity<Object> httpEntity = new HttpEntity<>(jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<JetlinksPageRespModel<DeviceUpGradeStatusRespModel>>> responseEntity = restTemplate.exchange(url, HttpMethod.GET,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<JetlinksPageRespModel<DeviceUpGradeStatusRespModel>>>() {
//				});
//
//		return responseEntity.getBody();

		return jetlinksPageBuildCommonPost(HttpMethod.GET, null, DeviceUpGradeStatusRespModel.class, url);
	}

	@Override
	public JetlinksWrapper<JetlinksPageRespModel<DeviceProductRespModel>> queryPageDeviceProduct(DeviceProductReqModel reqModel) {
		String url = urlPrefix + UnifyAccessUrlEnum.queryPageDeviceProduct.getCode();

//		log.info("url[{}]参数[{}]",url,reqModel);
//		HttpEntity<DeviceProductReqModel> httpEntity = new HttpEntity<>(reqModel, jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<JetlinksPageRespModel<DeviceProductRespModel>>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<JetlinksPageRespModel<DeviceProductRespModel>>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksPageBuildCommonPost(HttpMethod.POST, reqModel, DeviceProductRespModel.class, url);
	}

	@Override
	public JetlinksWrapper<JetlinksPageRespModel<DeviceFirewareRespModel>> queryPageDeviceFireware(DeviceFirewareReqModel reqModel) {
		String url = urlPrefix + UnifyAccessUrlEnum.queryPageDeviceFireware.getCode();

//		log.info("url[{}]参数[{}]",url,reqModel);
//		HttpEntity<DeviceFirewareReqModel> httpEntity = new HttpEntity<>(reqModel, jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<JetlinksPageRespModel<DeviceFirewareRespModel>>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<JetlinksPageRespModel<DeviceFirewareRespModel>>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksPageBuildCommonPost(HttpMethod.POST, reqModel, DeviceFirewareRespModel.class, url);
	}

	@Override
	public JetlinksWrapper<FirmwareUpgradeTaskRespModel> createFirmwareUpgradeTask(FirmwareUpgradeTaskReqModel reqModel) {
		String url = urlPrefix + UnifyAccessUrlEnum.createFirmwareUpgradeTask.getCode();

//		log.info("url[{}]参数[{}]",url,reqModel);
//		HttpEntity<FirmwareUpgradeTaskReqModel> httpEntity = new HttpEntity<>(reqModel, jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<FirmwareUpgradeTaskRespModel>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<FirmwareUpgradeTaskRespModel>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksBuildCommonPost(HttpMethod.POST, reqModel, FirmwareUpgradeTaskRespModel.class, url);
	}

	@Override
	public JetlinksWrapper<Object> publishUpgradeTaskToDevice(PublishUpgradeTaskToDeviceReqModel reqModel, String taskId) {
		String url = urlPrefix + UnifyAccessUrlEnum.publishUpgradeTaskToDevice.getCode().replace("{id}", taskId);

//		log.info("url[{}]参数[{}]",url,reqModel);
//		HttpEntity<List<String>> httpEntity = new HttpEntity<>(reqModel.getStrings(), jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<Object>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<Object>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksBuildCommonPost(HttpMethod.POST, reqModel, Object.class, url);
	}

	@Override
	public JetlinksWrapper<Object> pushUpgradeByTaskId(String taskId) {
		String url = urlPrefix + UnifyAccessUrlEnum.pushUpgradeByTaskId.getCode().replace("{id}", taskId);

//		log.info("url[{}]",url);
//		HttpEntity<Object> httpEntity = new HttpEntity<>(jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<Object>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<Object>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksBuildCommonPost(HttpMethod.POST, null, Object.class, url);
	}

	@Override
	public JetlinksWrapper<JetlinksDeviceInfoRespModel> getDeviceInstance(String sn) {
		String url = urlPrefix+UnifyAccessUrlEnum.deviceInstance.getCode().replace("{id}", sn);

//		log.info("url {}", url);
//		HttpEntity<Object> httpEntity = new HttpEntity<>(jetlinksBuildTokenHeaders());
//		ResponseEntity<JetlinksWrapper<JetlinksDeviceInfoRespModel>> responseEntity = restTemplate.exchange(url, HttpMethod.GET,
//				httpEntity,
//				new ParameterizedTypeReference<JetlinksWrapper<JetlinksDeviceInfoRespModel>>() {
//				});
//		return responseEntity.getBody();

		return jetlinksBuildCommonPost(HttpMethod.GET, null, JetlinksDeviceInfoRespModel.class, url);
	}

	@Override
	public Wrapper<EmqxSubscribersAddRespModel> emqxSubscribersAdd(String...topics) {
		String url = urlPrefix+UnifyAccessUrlEnum.emqxSubscribersAdd.getCode();
		EmqxSubscribersAddReqModel reqModel = new EmqxSubscribersAddReqModel();
		List<String> list = Arrays.asList(topics);
		reqModel.setTopics(list);
		Wrapper<EmqxSubscribersAddRespModel> result = buildCommonPost(reqModel, EmqxSubscribersAddRespModel.class, url);
		return result;
	}

	@Override
	public Wrapper<EmqxSubscribersDelRespModel> emqxSubscribersDel(String...topics) {
		String url = urlPrefix+UnifyAccessUrlEnum.emqxSubscribersDel.getCode();
		EmqxSubscribersDelReqModel reqModel = new EmqxSubscribersDelReqModel();
		List<String> list = Arrays.asList(topics);
		reqModel.setTopics(list);
		Wrapper<EmqxSubscribersDelRespModel> result = buildCommonPost(reqModel, EmqxSubscribersDelRespModel.class, url);
		return result;
	}

	@Override
	public Wrapper<EmqxSubscribersListRespModel> emqxSubscribersList() {
		String url = urlPrefix+UnifyAccessUrlEnum.emqxSubscribersList.getCode();
		EmqxSubscribersListReqModel reqModel = new EmqxSubscribersListReqModel();
		HttpEntity<EmqxSubscribersListReqModel> httpEntity = new HttpEntity<>(reqModel, buildTokenHeaders());
		log.info(UnifyAccessUrlEnum.emqxSubscribersList.getDesc()+" url={}",url.toString());
		ResponseEntity<Wrapper<EmqxSubscribersListRespModel>> responseEntity = restTemplate.exchange(url, HttpMethod.POST,
				httpEntity, new ParameterizedTypeReference<Wrapper<EmqxSubscribersListRespModel>>() {
				});
		Wrapper<EmqxSubscribersListRespModel> result = responseEntity.getBody();
		log.info(UnifyAccessUrlEnum.emqxSubscribersList.getDesc()+" result={}",result.toString());
		if(result!=null&&!CollectionUtils.isEmpty(result.getResult().getSubscribers())){
			List<String> subscribers = result.getResult().getSubscribers();
			List<String> list = Lists.newArrayList(subscribers.size());
			for(String topic :subscribers){
//				String str = StringUtils.substringBetween(topic, "topic:", ",");
//				list.add(str.trim());
				list.add(topic);
			}
			result.getResult().setSubscribers(list);
		}
		return result;
	}

	@Override
	public JetlinksWrapper<List<DeviceCategoryRespModel>> deviceCategory() {
		String url = urlPrefix+UnifyAccessUrlEnum.deviceCategory.getCode();
		url = url + "?terms[0].column=parentId&terms[0].value=-5-";

		log.info("url {}", url);
		
		int flag = 0;
		JetlinksWrapper<List<DeviceCategoryRespModel>> result = null;
		while (flag < 2) {
			try {
				HttpEntity<Object> httpEntity = new HttpEntity<>(jetlinksBuildTokenHeaders());
				ResponseEntity<JetlinksWrapper<List<DeviceCategoryRespModel>>> responseEntity = restTemplate.exchange(url, HttpMethod.GET,
						httpEntity,
						new ParameterizedTypeReference<JetlinksWrapper<List<DeviceCategoryRespModel>>>() {}
				);

				if (Objects.requireNonNull(responseEntity.getBody(), "请求结果【restTemplate.exchange().getBody()】为空").getStatus() == CODE_PLAT_TOKEN_EXPIRE) {
					flag++;
					redisUtil.del(jetlinksApiKey);
				} else {
					result = responseEntity.getBody();
					log.info("发送给接入返回结果result={}", result);
					break;
				}
			} catch (RestClientException e) {
				e.printStackTrace();
				log.error(e.getMessage(), e);
				String emsg = e.getMessage();
				//redis中key过期
				if (StringUtils.contains(emsg, "400 Bad Request")) {
					redisUtil.del(jetlinksApiKey);
				}
			} finally {
				flag++;
			}
		}
		return result;
	}

}
