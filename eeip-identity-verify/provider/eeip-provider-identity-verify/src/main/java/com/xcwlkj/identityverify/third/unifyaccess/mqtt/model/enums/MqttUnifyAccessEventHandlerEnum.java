package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums;

public enum MqttUnifyAccessEventHandlerEnum {

    SBZD_EVENT_HANDLER("SBZD_EVENT_HANDLER","设备终端事件处理")
    ,KSANDJKRC_EVENT_HANDLER("KSANDJKRC_EVENT_HANDLER","考生监考入场事件处理")
    ,SBMY_EVENT_HANDLER("SBMY_EVENT_HANDLER","设备密钥、激活事件处理")
    ,KSSJ_EVENT_HANDLER("KSSJ_EVENT_HANDLER","考试数据事件处理")
    ,WGKS_EVENT_HANDLER("WGKS_EVENT_HANDLER","违规考生事件处理")
    ,KWMSG_EVENT_HANDLER("KWMSG_EVENT_HANDLER","考务消息事件处理")
    ,URGENCYMSG_EVENT_HANDLER("URGENCYMSG_EVENT_HANDLER","紧急呼叫事件处理")
    ,KCQK_EVENT_HANDLER("KCQK_EVENT_HANDLER","考场情况事件处理")
    ,KQZL_EVENT_HANDLER("KQZL_EVENT_HANDLER","考情总览事件处理")
    ,KCLB_EVENT_HANDLER("KCLB_EVENT_HANDLER","考场列表事件处理")
    ,KWBD_EVENT_HANDLER("KWBD_EVENT_HANDLER","考务报到事件处理")
    ,KWTJ_EVENT_HANDLER("KWTJ_EVENT_HANDLER","考务统计事件处理")

    ,DEVICE_EVENT_HANDLER("DEVICE_EVENT_HANDLER","设备事件处理")
    ,JXSJ_EVENT_HANDLER("JXSJ_EVENT_HANDLER","教学数据事件处理")
    ,JKRYRC_EVENT_HANDLER("JKRC_EVENT_HANDLER","监考人员入场事件处理")
    ,SJGJ_EVENT_HANDLER("SJGJ_EVENT_HANDLER","升级固件事件处理")
    ,WJFWCSPZCX_ENENT_HANDLER("WJFWCSPZCX_ENENT_HANDLER","文件服务参数配置查询事件处理")
    ,WGCSPZXF_EVENT_HANDLER("WGCSPZXF_EVENT_HANDLER","网关参数配置下发事件处理")
    ,XFYDZDGJ_EVENT_HANDLER("XFYDZDGJ_EVENT_HANDLER","移动终端固件下发事件处理")
    ,KSSJDR_EVENT_HANDLER("KSSJDR_EVENT_HANDLER","考试数据导入事件处理")
    ,YDZDGJJS_EVENT_HANDLER("YDZDGJJS_EVENT_HANDLER","移动终端固件接收事件处理")
    ;

    /**
     * 主题key
     */
    String code;
    /**
     * 下行functionId
     */
    String functionId;
    /**
     * 名称
     */
    String name;

    MqttUnifyAccessEventHandlerEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
