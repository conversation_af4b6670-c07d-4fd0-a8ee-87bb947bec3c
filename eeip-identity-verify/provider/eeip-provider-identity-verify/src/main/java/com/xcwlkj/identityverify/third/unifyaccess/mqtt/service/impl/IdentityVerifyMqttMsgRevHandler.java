package com.xcwlkj.identityverify.third.unifyaccess.mqtt.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xcwlkj.biz.unifyaccess.mqtt.service.MqttMsgBizHandler;
import com.xcwlkj.identityverify.service.KsKssjDistributeTaskService;
import com.xcwlkj.identityverify.service.MqttUnifyAccessEventService;
import com.xcwlkj.identityverify.service.impl.SjptdrServiceImpl;
import com.xcwlkj.identityverify.service.impl.mqttHandler.MqttUnifyAccessEventExecutorRegistry;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttFuncInvokeEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeVO;
import com.xcwlkj.identityverify.util.HsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;

@Service
@Slf4j
public class IdentityVerifyMqttMsgRevHandler implements MqttMsgBizHandler {

    @Resource
    private SjptdrServiceImpl sjptdrService;
    @Resource
    private MqttUnifyAccessEventExecutorRegistry mqttUnifyAccessEventExecutorRegistry;
    @Resource
    private KsKssjDistributeTaskService kssjDistributeTaskService;

    @Override
    public void handle(String topic, Object payload) {
        if (payload instanceof String) {
            String payloadStr = (String) payload;
            if (StringUtils.equals(topic, HsUtils.generateTopicFunctionInvoke())) {
                String[] array = StringUtils.split(topic, "/");
                JSONObject jsonObject = JSONObject.parseObject(payloadStr);
                String functionId = jsonObject.getString("functionId");
                if (array.length >= 4) {
                    String productId = array[0];
                    String deviceId = array[1];
                    MqttFuncInvokeEnum devEventEnum = MqttFuncInvokeEnum.getEventByFuncId(functionId);
                    if (devEventEnum != null) {
                        MqttUnifyAccessEventService muaes = mqttUnifyAccessEventExecutorRegistry.getExecutor(devEventEnum.getEventHandlerEnum());
                        muaes.handleFuncInvoke(productId, deviceId, devEventEnum, payloadStr);
                    } else {
                        log.warn("校级平台网关事件,topic[{}]无法处理", topic);
                    }
                }
//                handleFunctionInvoke(payloadStr);
            } else if (StringUtils.equals(topic, HsUtils.generateTopicTokenGetReply())) {
                handleTokenGetReply(payloadStr);
            } else if (StringUtils.contains(topic, "/event/")) {
                String[] array = StringUtils.split(topic, "/");
                if (array.length >= 4) {
                    String productId = array[0];
                    String deviceId = array[1];
                    String eventId = array[3];
                    MqttDevEventEnum devEventEnum = MqttDevEventEnum.getEvent(eventId);
                    boolean exist = judgeDeviceIdExist(deviceId);
                    if (exist) {
                        if (devEventEnum != null) {
                            MqttUnifyAccessEventService muaes = mqttUnifyAccessEventExecutorRegistry.getExecutor(devEventEnum.getEventHandlerEnum());
                            muaes.handleDevEvent(productId, deviceId, devEventEnum, payloadStr);
                        } else {
                            log.warn("设备上报事件,topic[{}]无法处理", topic);
                        }
                    } else {
                        log.warn("设备上报事件,设备ID[{}]不存在,topic[{}]忽略", deviceId, topic);
                    }
                } else {
                    log.error("设备上报事件topic[" + topic + "] 不符合规则");
                }
            } else {
                log.info("topic[" + topic + "] no method can handle this topic...");
            }
        }
    }


//    private void handleFunctionInvoke(String msg) {
//        ObjectMapper objectMapper = new ObjectMapper();
//        try {
//            log.info("开始导入上级平台下发数据[{}]",msg);
//            long st = System.currentTimeMillis();
//            FunctionInvokeVO functionInvokeVO = objectMapper.readValue(msg, FunctionInvokeVO.class);
//            sjptdrService.ksjhdr(functionInvokeVO);
//            long cost = System.currentTimeMillis()-st;
//            log.info("完成导入上级平台下发数据,耗时[{}]ms",cost);
//            sjptdrService.sbkdxzzt(functionInvokeVO);
//            log.info("数据同步-数据下发任务新建");
//            long start = System.currentTimeMillis();
//            String ksbh = functionInvokeVO.getInputs().get(0).getValue().getKsbh();
//            kssjDistributeTaskService.sjxfrwxj(ksbh);
//            long time = System.currentTimeMillis()-start;
//            log.info("完成数据下发任务新建,耗时[{}]ms",time);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error(e.getMessage(), e);
//        }
//    }

    private void handleTokenGetReply(String msg) {
        log.info("handleTokenGetReply:{}", msg);
    }

    private boolean judgeDeviceIdExist(String deviceId) {
        log.info("judgeDeviceIdExist:{}", deviceId);
        if(StringUtils.equals(deviceId, HsUtils.generateComputerIdentifier())){
            log.info("id={} 为自身数据,不处理......");
            return false;
        }
        return true;
    }
}
