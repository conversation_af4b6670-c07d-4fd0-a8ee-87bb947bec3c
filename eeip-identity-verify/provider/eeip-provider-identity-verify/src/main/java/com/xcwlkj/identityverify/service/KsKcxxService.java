/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service;

import com.xcwlkj.identityverify.model.domain.KsKcxx;
import com.xcwlkj.identityverify.model.dos.KdKcxxDO;
import com.xcwlkj.identityverify.model.dos.KdKcxxQueryDO;
import com.xcwlkj.identityverify.model.dto.ksgl.*;
import com.xcwlkj.identityverify.model.dto.sjxf.HqkclbDTO;
import com.xcwlkj.identityverify.model.vo.ksgl.*;
import com.xcwlkj.identityverify.model.vo.sjxf.HqkclbVO;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.KclbQueryReq;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.resp.KclbQueryResp;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;


/**
 * 考试考场信息服务
 * <AUTHOR>
 * @version $Id: KsKcxxService.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Service
public interface KsKcxxService extends BaseService<KsKcxx>  {


    KcbplbVO kcbplb(KcbplbDTO dto);

    void kcjsdygxExcelsjdr(String filePath,String ksjhbh);

    KcjsdygxExceldcVO kcjsdygxExceldc(KcjsdygxExceldcDTO dto);

    void kcjsdygxQxgl(KcjsdygxQxglDTO dto);

    KcgxFpqkVO kcgxFpqk(KcgxFpqkDTO dto);

    KcgxWfpkclbVO kcgxWfpkclb(KcgxWfpkclbDTO dto);

    KcgxBzhkclbVO kcgxBzhkclb(KcgxBzhkclbDTO dto);

    void kcgxBcgl(KcgxBcglDTO dto);

    HqkclbVO hqkclb(HqkclbDTO dto);

    /**
     * 获取考场号map  <bzhkcid, kch列表 逗号分隔>
     *
     * @param ksjhbh
     * @param bzhkcids
     * @return
     */
    Map<String, String> getKchMap(String ksjhbh, List<String> bzhkcids);

    /**
     * 考场教室对应关系解除本场关联
     * @param dto
     */
    void kcjsdygxJcbcgl(KcjsdygxJcbcglDTO dto);

    /**
     * 复制备用考场关联关系到其它场次
     * @param dto
     */
    void fzbykcglgxdqtcc(FzbykcglgxdqtccDTO dto);

    /**
     * 获取打包考场列表
     * @param ksjhbh
     * @return
     */
    List<KsKcxx> getdbkcList(String ksjhbh);

    /**
     * 考场教室对应关系完整核验
     * @param dto
     * @return
     */
    KcjsdygxwzhyVO kcjsdygxwzhy(KcjsdygxwzhyDTO dto);

    Map<String, String> getKcmcMap(String ksjhbh, List<String> bzhkcids);

    List<KdKcxxDO> selectKcxxDO(KdKcxxQueryDO kcxx);

    /**
     * 查询某场次下考场列表
     * @param ksjhbh
     * @param ccm
     * @return
     */
    List<CxmccxkclbItemVO> cxmccxkclb(String ksjhbh, String ccm);
}