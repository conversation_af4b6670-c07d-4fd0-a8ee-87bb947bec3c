package com.xcwlkj.identityverify.service.impl.mqttHandler;

import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import com.xcwlkj.identityverify.service.MqttUnifyAccessEventService;
import com.xcwlkj.identityverify.third.unifyaccess.http.service.UnifyAccessService;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttDevEventEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttFuncInvokeEnum;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums.MqttUnifyAccessEventHandlerEnum;
import com.xcwlkj.identityverify.util.AccessJsonUtils;
import com.xcwlkj.msgque.model.domain.RespBaseModel;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

@Slf4j
public abstract class MqttBaseEventHandler implements MqttUnifyAccessEventService, InitializingBean {

    @Autowired
    private UnifyAccessService unifyAccessService;
    @Resource
    private MqttUnifyAccessEventExecutorRegistry mqttEventExecutorRegistry;

    public void handleDevEvent(String productId, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr) {

//        ObjectMapper objectMapper = new ObjectMapper();
        try {
//            Object reqObj = objectMapper.readValue(payloadStr, devEventEnum.getReqClass());
            JSONObject jsonObject = JSONObject.parseObject(payloadStr);
            JSONObject dataJson = jsonObject.getJSONObject("data");
            Object reqObj = JSONObject.parseObject(dataJson.toJSONString(), devEventEnum.getReqClass());
            HanleResult hanleResult = doHanleEvent(productId, reqObj, deviceId, devEventEnum, payloadStr);
            if (hanleResult.needToSendResp) {
                Object respObj = hanleResult.respObj;
                sendCommonMsgToDev(devEventEnum, deviceId, respObj, payloadStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("处理消息异常", e);
        }
    }

    protected abstract HanleResult doHanleEvent(String productId, Object req, String deviceId, MqttDevEventEnum devEventEnum, String payloadStr);


    private void sendCommonMsgToDev(MqttDevEventEnum devEventEnum, String deviceId, Object dataJson, String payloadStr) {

        JSONObject jsonObject = AccessJsonUtils.generateAccessJson(devEventEnum.getFunctionId(), dataJson);

        Wrapper<RespBaseModel> remoteRespBaseModelWrapper = unifyAccessService.sendCommand(deviceId, jsonObject.toJSONString());

        if (remoteRespBaseModelWrapper.getCode() == 200) {
            log.info("发送成功 {}", jsonObject);
        } else {
            log.info("发送失败 {}", payloadStr);
        }
    }

    protected class HanleResult {
        Object respObj;
        boolean needToSendResp;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        mqttEventExecutorRegistry.register(getEventHandlerType(), this);
    }

    protected abstract MqttUnifyAccessEventHandlerEnum getEventHandlerType();

    public void handleFuncInvoke(String productId, String deviceId, MqttFuncInvokeEnum funcInvokeEnum, String payloadStr){}
}
