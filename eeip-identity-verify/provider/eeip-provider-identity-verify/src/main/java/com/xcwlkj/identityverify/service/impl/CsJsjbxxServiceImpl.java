/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.xcwlkj.identityverify.mapper.CsXxjzwjbxxMapper;
import com.xcwlkj.identityverify.model.domain.CsJsjbxx;
import com.xcwlkj.identityverify.model.domain.CsXqjbxx;
import com.xcwlkj.identityverify.model.domain.CsXxjzwjbxx;
import com.xcwlkj.identityverify.model.dos.CssbxxDO;
import com.xcwlkj.identityverify.model.dos.JswzxxDO;
import com.xcwlkj.identityverify.model.dto.ksyw.KclblqDTO;
import com.xcwlkj.identityverify.model.dto.zygl.JsxxbjDTO;
import com.xcwlkj.identityverify.model.dto.zygl.JsxxlbcxDTO;
import com.xcwlkj.identityverify.model.dto.zygl.JsxxtjDTO;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.ksyw.KclbItemVO;
import com.xcwlkj.identityverify.model.vo.ksyw.KclblqVO;
import com.xcwlkj.identityverify.model.vo.zygl.JsxxlbItemVO;
import com.xcwlkj.identityverify.model.vo.zygl.JsxxlbcxVO;
import com.xcwlkj.identityverify.model.vo.zygl.JsxxmbxzVO;
import com.xcwlkj.identityverify.provincePlatform.client.ProvincePlatformClient;
import com.xcwlkj.identityverify.provincePlatform.request.KclbRequest;
import com.xcwlkj.identityverify.provincePlatform.response.KclbResponse;
import com.xcwlkj.identityverify.provincePlatform.response.items.KclbRespItem;
import com.xcwlkj.identityverify.service.CsXqjbxxService;
import com.xcwlkj.identityverify.service.CsXxjbxxService;
import com.xcwlkj.identityverify.service.CsXxjzwjbxxService;
import com.xcwlkj.identityverify.util.ExcelWidthStyleStrategy;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.Wrapper;
import com.xcwlkj.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.xcwlkj.identityverify.mapper.CsJsjbxxMapper;
import com.xcwlkj.identityverify.service.CsJsjbxxService;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 教室基本信息服务
 * <AUTHOR>
 * @version $Id: CsJsjbxxServiceImpl.java, v 0.1 2023年09月19日 17时12分 xcwlkj.com Exp $
 */
@Slf4j
@Service("csJsjbxxService")
public class CsJsjbxxServiceImpl extends BaseServiceImpl<CsJsjbxxMapper,CsJsjbxx> implements CsJsjbxxService  {
    @Value("${xc.temp.path}")
    private String sysFilePath;
    @Resource
    private CsJsjbxxMapper modelMapper;

    @Resource
    private CsXxjzwjbxxMapper csXxjzwjbxxMapper;
    @Resource
    private CsXxjzwjbxxService csXxjzwjbxxService;
    @Resource
    private CsXqjbxxService csXqjbxxService;
    @Resource
    private CsXxjbxxService csXxjbxxService;
    @Resource
    private ProvincePlatformClient provinceClient;
    @Resource
    private AttachmentHandler attachmentHandler;


    @Override
    public List<CsJsjbxx> selectByExample(Example jsjbxxExample) {
        return modelMapper.selectByExample(jsjbxxExample);
    }

    @Override
    public JswzxxDO getClassRoomInfo(String jsh) {
        return modelMapper.selectJswzxxByJsh(jsh);
    }

    @Override
    public List<JswzxxDO> getClassRoomInfoListByBzhkcids(List<String> bzhkcidList) {
        return modelMapper.selectJswzxxByJshList(bzhkcidList);
    }

    @Override
    public CsJsjbxx selectOneByExample(Example jsxxExample) {
        return modelMapper.selectOneByExample(jsxxExample);
    }


    @Override
    public synchronized KclblqVO kclblq(KclblqDTO dto) {
        KclblqVO kclblqVO = new KclblqVO();
        // 因为省平台拉取的信息没有校区，考虑到树列表的结构需要校区，因此添加默认的校区信息
        csXqjbxxService.insertIfNotExistDefault();

        KclbRequest request = new KclbRequest();

        request.setBzhkdid(csXxjbxxService.getBzhkdid());
//        Wrapper<KclbResponse> responseWrapper = kclbClient.send(request);
        Wrapper<KclbResponse> responseWrapper = provinceClient.kclblq(request);
        List<KclbRespItem> kclbRespItemList = responseWrapper.getResult().getKclb();
        if(CollectionUtils.isEmpty(kclbRespItemList)){
            log.warn("拉取考场列表信息为空");
            return kclblqVO;
        }
        kclbRespItemList.forEach(entity -> {
            if (StringUtil.isBlank(entity.getJzmc())){
                entity.setJzmc("默认教学楼");
            }
        });
        List<String> jzmcs = kclbRespItemList.stream().map(KclbRespItem::getJzmc).collect(Collectors.toList());
        Map<String,KclbRespItem> bzhkcidMap = kclbRespItemList.stream().collect(Collectors.toMap(KclbRespItem::getBzhkcid, kclbRespItem -> kclbRespItem));
        // 检查建筑物名称是否存在，不存在则添加
        // 1、查询所有已存在的建筑物名称
        Example jzwmcExample = new Example(CsXxjzwjbxx.class);
        List<CsXxjzwjbxx> csXxjzwjbxxes = csXxjzwjbxxMapper.selectByExample(jzwmcExample);
        Set<String> existJzwmcs = csXxjzwjbxxes.stream().map(CsXxjzwjbxx::getJzwmc).collect(Collectors.toSet());
        Map<String,CsXxjzwjbxx> jzwmcJzwMap = new HashMap<>();
        csXxjzwjbxxes.forEach(csXxjzwjbxx -> {
            jzwmcJzwMap.put(csXxjzwjbxx.getJzwmc(),csXxjzwjbxx);
        });
        // 2、过滤出不存在的建筑物名称
        Set<String> notExistJzmcs = jzmcs.stream().filter(jzwmc -> !existJzwmcs.contains(jzwmc)).collect(Collectors.toSet());
        // 3、把不存在的建筑物名称添加到默认校区
        if(!notExistJzmcs.isEmpty()){
            int preId = csXxjzwjbxxMapper.selectMaxId();
            List<CsXxjzwjbxx> xxjzwjbxxes = new ArrayList<>();
            for (String jzwmc : notExistJzmcs) {
                CsXxjzwjbxx xxjzwjbxx = new CsXxjzwjbxx();
                xxjzwjbxx.setJzwmc(jzwmc);
                xxjzwjbxx.setXqh("-1");
                xxjzwjbxx.setCreateTime(new Date());
                xxjzwjbxx.setUpdateTime(new Date());
                ++preId;
                xxjzwjbxx.setJzwh(String.valueOf(preId));
                xxjzwjbxx.setSczt(ScztEnum.NOTDEL.getCode());
                // 保存建筑物名称和建筑物号的映射关系
                jzwmcJzwMap.put(jzwmc,xxjzwjbxx);
                xxjzwjbxxes.add(xxjzwjbxx);
            }
            csXxjzwjbxxService.insertListSelective(xxjzwjbxxes);
        }
//        jzmcs.forEach(jzwmc->{
//            CsXxjzwjbxx xxjzwjbxx = new CsXxjzwjbxx();
//            xxjzwjbxx.setJzwmc(jzwmc);
//            xxjzwjbxx.setXqh("-1");
//            xxjzwjbxx.setCreateTime(new Date());
//            xxjzwjbxx.setUpdateTime(new Date());
//            csXxjzwjbxxService.insertIfNotExist(jzwmc);
//        });
        // 检查教室是否存在，不存在则添加
        // 1、查询所有已存在的教室
        Example jsxxExample = new Example(CsJsjbxx.class);
        // 查询教室号和bzhkcid
        jsxxExample.selectProperties("jsh", "bzhkcid", "jxlh", "sczt");
        List<CsJsjbxx> csJsjbxxes = modelMapper.selectByExample(jsxxExample);
        Map<String, String> existBzhkcids = csJsjbxxes.stream().map(entity -> {
            if (StringUtil.isBlank(entity.getBzhkcid())){
                entity.setBzhkcid(entity.getJsh());
            }
            return entity;
        }).collect(Collectors.toMap(CsJsjbxx::getBzhkcid, CsJsjbxx::getSczt));
        Map<String, String> bzhkcidAndJxlhMap = csJsjbxxes.stream().collect(Collectors.toMap(CsJsjbxx::getBzhkcid, CsJsjbxx::getJxlh));
        // 2、过滤出不存在的教室
        Set<String> notExistBzhkcids = bzhkcidMap.keySet().stream().filter(bzhkcid -> !existBzhkcids.containsKey(bzhkcid)).collect(Collectors.toSet());
        // 过滤出修改过的教室
        Set<String> updateBzhkcids = bzhkcidMap.entrySet().stream().filter(entity -> (
                        StringUtil.isNotBlank(entity.getValue().getJzmc()) &&
                        !StringUtil.equals(bzhkcidAndJxlhMap.get(entity.getValue().getBzhkcid()), jzwmcJzwMap.get(entity.getValue().getJzmc()).getJzwh()))
                        // 之前被删除并且当前恢复存在的教室标记为修改 防止key重复
                        || (existBzhkcids.containsKey(entity.getKey()) && existBzhkcids.get(entity.getKey()).equals(ScztEnum.DEL.getCode()))
                )
                .map(Map.Entry::getKey).collect(Collectors.toSet());
        // 数据库中删除修改过的教室信息
        if (!updateBzhkcids.isEmpty()){
            notExistBzhkcids.addAll(updateBzhkcids);
            jsxxExample.clear();
            jsxxExample.createCriteria().andIn("bzhkcid", updateBzhkcids);
            modelMapper.deleteByExample(jsxxExample);
        }
        // 过滤出多余的教室信息
        List<String> deleteBzhkcids = existBzhkcids.entrySet().stream().filter(entity -> (!bzhkcidMap.containsKey(entity.getKey()) && entity.getValue().equals(ScztEnum.NOTDEL.getCode())))
                .map(Map.Entry::getKey).collect(Collectors.toList());
        if (!deleteBzhkcids.isEmpty()){
            jsxxExample.clear();
            jsxxExample.createCriteria().andIn("bzhkcid", deleteBzhkcids);
            CsJsjbxx deleteJsjbxx = new CsJsjbxx();
            deleteJsjbxx.setSczt(ScztEnum.DEL.getCode());
            modelMapper.updateByExampleSelective(deleteJsjbxx, jsxxExample);
        }
        // 3、把不存在的教室添加到数据库
        List<CsJsjbxx> jsxxes = notExistBzhkcids.stream().map(bzhkcid -> {
            KclbRespItem kclbRespItem = bzhkcidMap.get(bzhkcid);
            CsJsjbxx csJsjbxx = new CsJsjbxx();
            csJsjbxx.setJsh(bzhkcid);
            csJsjbxx.setSczt(ScztEnum.NOTDEL.getCode());
            csJsjbxx.setJslxm(kclbRespItem.getKclx());
            csJsjbxx.setBzhkcid(kclbRespItem.getBzhkcid());
            csJsjbxx.setJsmc(kclbRespItem.getBzhkcmc());
            csJsjbxx.setZws(Integer.valueOf(kclbRespItem.getKcrl()));
            csJsjbxx.setSzlc(kclbRespItem.getSzlc());
            csJsjbxx.setXqh(jzwmcJzwMap.get(kclbRespItem.getJzmc()).getXqh());
            csJsjbxx.setJxlh(jzwmcJzwMap.get(kclbRespItem.getJzmc()).getJzwh());
            csJsjbxx.setCreateTime(new Date());
            csJsjbxx.setUpdateTime(new Date());
            return csJsjbxx;
        }).collect(Collectors.toList());
        if(!jsxxes.isEmpty()){
            modelMapper.insertListSelective(jsxxes);
        }
//        bzhkcidMap.keySet().forEach(bzhkcid->{
//            KclbRespItem kclbRespItem = bzhkcidMap.get(bzhkcid);
//            this.insertIfNotExist(bzhkcid,kclbRespItem);
//        });
        List<KclbItemVO> collect = kclbRespItemList.stream().map(kclbRespItem -> {
            KclbItemVO kclbItemVO = new KclbItemVO();
            BeanUtils.copyProperties(kclbRespItem, kclbItemVO);
            return kclbItemVO;
        }).collect(Collectors.toList());
        kclblqVO.setKclb(collect);
        return kclblqVO;
    }

    @Override
    public String getBzhkcidByJshOrBzhkcid(String jsh) {
        Example jsxxExample = new Example(CsJsjbxx.class);
        jsxxExample.createCriteria()
                .andEqualTo("bzhkcid",jsh).orEqualTo("jsh",jsh);
        List<CsJsjbxx> csJsjbxxList = modelMapper.selectByExample(jsxxExample);
        if(!CollectionUtils.isEmpty(csJsjbxxList)){
            return csJsjbxxList.get(0).getBzhkcid();
        }
        log.warn("教室号{}不存在",jsh);
        return null;
    }

    @Override
    public String getJshByBzhkcid(String bzhkcid) {
        Example jsxxExample = new Example(CsJsjbxx.class);
        jsxxExample.createCriteria()
                .andEqualTo("bzhkcid",bzhkcid);
        CsJsjbxx csJsjbxx = modelMapper.selectOneByExample(jsxxExample);
        if(csJsjbxx != null){
            return csJsjbxx.getJsh();
        }
        log.warn("标准化考场id{}不存在",bzhkcid);
        return null;
    }

    @Override
    public JsxxlbcxVO jsxxlbcx(JsxxlbcxDTO dto) {
        PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
        List<JsxxlbItemVO> jsxxlbItemVOS = modelMapper.jsxxlbcx(dto.getJsmc(),dto.getJsh(),dto.getBzhkcid(),dto.getJzwh());
        PageInfo<JsxxlbItemVO> jsxxlbItemVOPageInfo = new PageInfo<>(jsxxlbItemVOS);
        JsxxlbcxVO jsxxlbcxVO = new JsxxlbcxVO();
        jsxxlbcxVO.setJsxxlb(jsxxlbItemVOPageInfo.getList());
        jsxxlbcxVO.setTotalRows((int) jsxxlbItemVOPageInfo.getTotal());
        return jsxxlbcxVO;
    }

    @Override
    public void jsxxsc(List<String> jsbhlb) {
        Example example = new Example(CsJsjbxx.class);
        example.createCriteria()
                .andIn("jsh",jsbhlb);
        CsJsjbxx csJsjbxx = new CsJsjbxx();
        csJsjbxx.setSczt(ScztEnum.DEL.getCode());
        csJsjbxx.setUpdateTime(new Date());
        modelMapper.updateByExampleSelective(csJsjbxx,example);
    }

    @Override
    public void jsxxbj(JsxxbjDTO dto) {
        Example example = new Example(CsJsjbxx.class);
        example.createCriteria()
                .andEqualTo("jsh",dto.getJsh());
        CsJsjbxx csJsjbxx = new CsJsjbxx();
        csJsjbxx.setJsmc(dto.getCsmc());
        csJsjbxx.setSzlc(dto.getSzlc());
        csJsjbxx.setJxlh(dto.getJzwh());
        csJsjbxx.setBzhkcid(dto.getBzhkcid());
        csJsjbxx.setXqh(dto.getXqh());
        csJsjbxx.setZws(dto.getZws());
        csJsjbxx.setUpdateTime(new Date());
        modelMapper.updateByExampleSelective(csJsjbxx,example);
    }

    @Override
    public void jsxxtj(JsxxtjDTO dto) {
        // 检查教室号是否存在
        Example jsxxExample = new Example(CsJsjbxx.class);
        jsxxExample.createCriteria()
                .andEqualTo("jsh",dto.getJsh());
        if(modelMapper.selectCountByExample(jsxxExample) > 0){
            log.warn("教室号{}已存在",dto.getJsh());
            throw new RuntimeException("教室号已存在");
        }
        CsJsjbxx csJsjbxx = new CsJsjbxx();
        csJsjbxx.setJsh(dto.getJsh());
        csJsjbxx.setJsmc(dto.getJsmc());
        csJsjbxx.setXqh(dto.getXqh());
        csJsjbxx.setJxlh(dto.getJzwh());
        csJsjbxx.setBzhkcid(dto.getBzhkcid());
        csJsjbxx.setSzlc(dto.getSzlc());
        csJsjbxx.setZws(dto.getZws());
        csJsjbxx.setSczt(ScztEnum.NOTDEL.getCode());
        csJsjbxx.setCreateTime(new Date());
        csJsjbxx.setUpdateTime(new Date());
        modelMapper.insertSelective(csJsjbxx);
    }

    @Override
    public void jsxxExcelDr(String filePath) {
        List<CsJsjbxx> csJsjbxxes = new ArrayList<>();
        try {
            List<List<String>> datas = ExcelUtil.readBigFile(filePath, -1);
            int currentRow = 0;
            if(CollectionUtils.isEmpty(datas) || datas.size() < 2){
                log.warn("Excel文件内容为空");
                throw new RuntimeException("文件内容为空");
            }
            List<String> title = datas.get(0);
            // 查询所有已存在的校区号
            Example xqhExample = new Example(CsXqjbxx.class);
            xqhExample.createCriteria();
            xqhExample.selectProperties("xqh");
            List<CsXqjbxx> csXqjbxxes = csXqjbxxService.selectListByExample(xqhExample);
            Set<String> existXqhs = csXqjbxxes.stream().map(CsXqjbxx::getXqh).collect(Collectors.toSet());
            // 查询所有的建筑物号
            Example jzwmcExample = new Example(CsXxjzwjbxx.class);
            jzwmcExample.createCriteria()
                    .andEqualTo("sczt",ScztEnum.NOTDEL.getCode());
            jzwmcExample.selectProperties("jzwh");
            List<CsXxjzwjbxx> csXxjzwjbxxes = csXxjzwjbxxMapper.selectByExample(jzwmcExample);
            Set<String> existJzwhs = csXxjzwjbxxes.stream().map(CsXxjzwjbxx::getJzwh).collect(Collectors.toSet());
            for (List<String> data : datas) {
                if(currentRow == 0){
                    currentRow++;
                    continue;
                }
                CsJsjbxx csJsjbxx = new CsJsjbxx();
                csJsjbxx.setJsh(data.get(0).trim());
                csJsjbxx.setJsmc(data.get(1).trim());
                if(!existXqhs.contains(data.get(2).trim())){
                    log.warn("校区号{}不存在",data.get(2));
                    throw new RuntimeException("第 "+currentRow+" 行校区号 "+ data.get(2) +" 不存在");
                }
                csJsjbxx.setXqh(data.get(2).trim());
                csJsjbxx.setJxlh(data.get(3).trim());
                if (!existJzwhs.contains(data.get(3).trim())) {
                    log.warn("建筑物号{}不存在",data.get(3));
                    throw new RuntimeException("第 "+currentRow+" 行建筑物号 "+ data.get(3) +" 不存在");
                }
                csJsjbxx.setBzhkcid(data.get(4).trim());
                csJsjbxx.setSzlc(data.get(5).trim());
                csJsjbxx.setSczt(ScztEnum.NOTDEL.getCode());
                csJsjbxx.setCreateTime(new Date());
                csJsjbxx.setUpdateTime(new Date());
                csJsjbxxes.add(csJsjbxx);
            }
        }catch (RuntimeException e){
            throw e;
        }catch (Exception e) {
            throw new RuntimeException("读取Excel文件失败");
        }
        if(!csJsjbxxes.isEmpty()){
            modelMapper.insertListSelective(csJsjbxxes);
        }
    }

    @Override
    public JsxxmbxzVO jsxxmbxz() {
        String filePath = null;
        String suffix = ".xlsx";
        String fileName = "教室信息模板";
        String dir = DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT);
        String filepath = sysFilePath + dir + File.separator + fileName + suffix;
        File file = new File(sysFilePath + dir);
        if(!file.exists()){
            file.mkdir();
        }
        List<List<String>> head = new ArrayList<>();
        List<String> head1 = new ArrayList<>();
        head.add(Collections.singletonList("教室号"));
        head.add(Collections.singletonList("教室名称"));
        head.add(Collections.singletonList("校区号"));
        head.add(Collections.singletonList("教学楼号"));
        head.add(Collections.singletonList("标准化考场id"));
        head.add(Collections.singletonList("所在楼层"));
        log.info("开始生成教室信息导入模板 filepath:{}",filepath);
        EasyExcel.write(filepath)
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 32, (short) 17))
                .head(head)
                .sheet("教室信息导入模板")
                .doWrite(new ArrayList<>());
        log.info("生成教室信息导入模板成功 filepath:{}",filepath);
        Date expireTIme = DateUtil.offsetHour(DateUtil.getCurrentDT(), 2);
        UploadAttachmentReturnUrlVO returnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(filepath, expireTIme, null);
        log.info("returnUrlVO:" + returnUrlVO);
        String attachmentUrl = "";
        if (returnUrlVO != null) {
            attachmentUrl = returnUrlVO.getAttachmentUrl();
        }
        try {
            FileUtils.deleteDirectory(new File(sysFilePath + dir));
        } catch (Exception e) {
            e.printStackTrace();
        }
        JsxxmbxzVO jsxxmbxzVO = new JsxxmbxzVO();
        jsxxmbxzVO.setWjlj(attachmentUrl);
        return jsxxmbxzVO;
    }

    @Override
    public CsJsjbxx getByBzhkcid(String csbh) {
        Example jsxxExample = new Example(CsJsjbxx.class);
        jsxxExample.createCriteria()
                .andEqualTo("bzhkcid",csbh);
        return modelMapper.selectOneByExample(jsxxExample);
    }

    private void insertIfNotExist(String bzhkcid, KclbRespItem kclbRespItem) {
        Example jsxxExample = new Example(CsJsjbxx.class);
        jsxxExample.createCriteria()
                .andEqualTo("bzhkcid",bzhkcid);
        if(modelMapper.selectCountByExample(jsxxExample) > 0){
            return;
        }
        CsJsjbxx csJsjbxx = new CsJsjbxx();
        csJsjbxx.setJsh(bzhkcid);
        csJsjbxx.setSczt("0");
        csJsjbxx.setJslxm(kclbRespItem.getKclx());
        csJsjbxx.setJsh(kclbRespItem.getBzhkcid());
        csJsjbxx.setBzhkcid(kclbRespItem.getBzhkcid());
        csJsjbxx.setJsmc(kclbRespItem.getBzhkcmc());
        csJsjbxx.setZws(Integer.valueOf(kclbRespItem.getKcrl()));
        csJsjbxx.setSzlc(kclbRespItem.getSzlc());
        csJsjbxx.setXqh("-1");
        Example jxwExample = new Example(CsXxjzwjbxx.class);
        jxwExample.createCriteria()
                .andEqualTo("jzwmc",kclbRespItem.getJzmc());
        List<CsXxjzwjbxx> csXxjzwjbxxes = csXxjzwjbxxMapper.selectByExample(jxwExample);
        if(!CollectionUtils.isEmpty(csXxjzwjbxxes)){
            csJsjbxx.setJxlh(csXxjzwjbxxes.get(0).getJzwh());
        }
        csJsjbxx.setCreateTime(new Date());
        csJsjbxx.setUpdateTime(new Date());
        modelMapper.insertSelective(csJsjbxx);
    }

    @Override
    public List<CssbxxDO> getCssbxx() {
        return modelMapper.getCssbxx();
    }
}
