package com.xcwlkj.identityverify.model.constant;

public class GlobalKeys {
    public static final String PROVINCE_USERNAME_KEY = "hisomeProvicePlat.username";
    public static final String PROVINCE_PASSWORD_KEY = "hisomeProvicePlat.userpwd";
    public static final String PROVINCE_URL_KEY = "hisomeProvicePlat.url";
    /**
     * @deprecated 应使用对应平台的参数进行拼接使用，如 HISOME_subDevType
     * @com.xcwlkj.identityverify.third.superior.http.model.enums.HisomeParamEnum
     */
    @Deprecated
    public static final String PROVINCE_SUBDEV_TYPE = "HISOME_subDevType";
    public static final String PROVINCE_PLAT_INFO = "hisomeProvicePlat.platInfo";
    public static final String PROVINCE_USER_INFO = "hisomeProvicePlat.userInfo";
    public static final String PROVINCE_ACCESS_TOKEN = "province:accessToken";
    public static final String PROVINCE_TOKEN_TYPE = "province:tokenType";
    public static final String PROVINCE_USER_TOKEN = "province:userToken";
    // xc_client_sessionid
    public static final String PROVINCE_SESSIONID = "province:xcClientSessionid";
    public static final String DEFAULT_PLAT = "defaultPlat";
}
