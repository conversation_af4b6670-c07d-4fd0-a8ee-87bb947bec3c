package com.xcwlkj.identityverify.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.dfs.exceptions.DfsSdkBusiException;
import com.xcwlkj.dfs.model.vo.SubFileInfoVO;
import com.xcwlkj.dfs.model.vo.UploadItemVO;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.cache.MbCacheOperateService;
import com.xcwlkj.identityverify.mapper.SbSbcsgxMapper;
import com.xcwlkj.identityverify.model.constant.EventPeriodKey;
import com.xcwlkj.identityverify.model.domain.*;
import com.xcwlkj.identityverify.model.domain.view.KsBmxxTzzView;
import com.xcwlkj.identityverify.model.domain.view.KsJkryJbxxTzzView;
import com.xcwlkj.identityverify.model.dos.*;
import com.xcwlkj.identityverify.model.enums.*;
import com.xcwlkj.identityverify.model.vo.datapack.DistributePackVO;
import com.xcwlkj.identityverify.model.vo.datapack.StartPackVO;
import com.xcwlkj.identityverify.model.vo.sjxf.ExamPlanVO;
import com.xcwlkj.identityverify.model.vo.sjxf.FileDetailVO;
import com.xcwlkj.identityverify.service.*;
import com.xcwlkj.identityverify.service.impl.dataPkg.DataPkgServices;
import com.xcwlkj.identityverify.taskcenter.distributeTask.DistributeRedisRateLimiter;
import com.xcwlkj.identityverify.taskcenter.distributeTask.RateLimiter;
import com.xcwlkj.identityverify.taskcenter.distributeTask.exception.ToppingOffException;
import com.xcwlkj.identityverify.third.unifyaccess.http.service.UnifyAccessService;
import com.xcwlkj.identityverify.util.AccessJsonUtils;
import com.xcwlkj.identityverify.util.CompressUtil;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.msgque.model.domain.RespBaseModel;
import com.xcwlkj.msgque.service.XcMsgService;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service
public class ExamDataServiceImpl implements ExamDataService {

    protected final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Value("${xc.identityVerify.pkg.path}")
    private String pkgPath;
    @Value("${xc.identityVerify.pkg.secret.enable}")
    private boolean secretEnable;
    @Value("${xc.identityVerify.pkg.secret.key}")
    private String secretKey;
    @Value("${xc.identityVerify.distributePkg.max_retry_count}")
    private int maxRetryCount;
    @Value("${xc.identityVerify.distributePkg.tokenNum:10}")
    private int tokenNum;
    @Value("${xc.identityVerify.distributePkg.tokenSupplyIntervalSec:10}")
    private int tokenSupplyIntervalSec;
    @Value("${xc.rocketMq.revDistributeOverTimeTopic:XFCS}")
    private String revDistributeOverTimeTopic;
    @Value("${xc.identityVerify.distributePkg.overTime:1800}")
    private int distributeOverTime;

    private final String SuffixTxt = ".txt";

    private final String gxRygwlxmb = "jy_rygwlxmb";

    private final String gxRygwzzmb = "jy_rygwzzmb";

    // 下发线程状态标记key前缀
    private final static String distributeThreadFlagRedisPrefix = SubSysEnum.IDENTITYVERIFY.getCode() + ":KSYW:distributeThreadFlag";
    // 下发线程状态标记key过期时间
    private final static int distributeThreadFlagRedisKeyExpireTime = 60 * 60;

    @Resource
    private DistributeRedisRateLimiter distributeRedisRateLimiter;
    @Resource
    private KsBmxxService ksBmxxService;
    @Resource
    private KsBpxxService ksBpxxService;
    @Resource
    private KsKssjPkgFileService ksKssjPkgFileService;
    @Resource
    private KsKsjhService ksKsjhService;
    @Resource
    private KsKdxxService ksKdxxService;
    @Resource
    private KsKsccService ksKsccService;
    @Resource
    private KsKcxxService ksKcxxService;
    @Resource
    private KsJkryJbxxService ksJkryJbxxService;
    @Resource
    private KsJkryBpxxService ksJkryBpxxService;
    @Resource
    private MbCacheOperateService mbCacheOperateService;
    @Resource
    private KsKssjPkgExceptionService ksKssjPkgExceptionService;
    @Resource
    private KsKssjPkgStatusService ksKssjPkgStatusService;
    @Resource
    private KsKssjPkgTaskService ksKssjPkgTaskService;
    @Resource
    private KsKssjDistributeStatusService ksDistributeStatusService;
    @Resource
    private KsKssjDistributeTaskService ksDistributeTaskService;
    @Resource
    private KsKssjDistributeTaskCsService distributeTaskCsService;
    @Resource
    private KsKssjDistributeTaskLogService ksDistributeTaskLogService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private DataPkgServices dataPkgServices;
    @Resource
    private XcMsgService xcMsgService;
    @Resource
    private KsProcessEventService ksProcessEventService;
    @Resource
    private KsProcessPeriodService ksProcessPeriodService;
    @Resource
    private SbSbcsgxMapper sbcsgxMapper;
    @Autowired
    private UnifyAccessService unifyAccessService;
    @Autowired
    private AttachmentService attachmentService;

//	@Override
//	public GetExamArrgNumInfoV1VO getExamArrgNumInfoV1(String orgcode,String examPlanCode) {
//		KdKdxx kdxx = new KdKdxx();
//		kdxx.setBzhkdid(orgcode);
//		kdxx.setKsjhbh(examPlanCode);
//		List<KsBpxx> bpxx = getBpxxByKd(kdxx);
//		List<KsBmxx> bmxx = getBmxxByBp(bpxx);
//
//		GetExamArrgNumInfoV1VO getExamArrgNumInfoV1VO = new GetExamArrgNumInfoV1VO();
//		getExamArrgNumInfoV1VO.setUserExamInfoTotalNum(bpxx.size());
//		getExamArrgNumInfoV1VO.setUserInfoTotalNum(bmxx.size());
//
//		return getExamArrgNumInfoV1VO;
//	}

    @Override
    public StartPackVO packZip(PackParamDO paramDO) {

        // 获取任务信息
        KsKssjPkgTask task = null;
        if (StringUtils.isNotBlank(paramDO.getTaskId())) {
            task = ksKssjPkgTaskService.selectSingleByKey(paramDO.getTaskId());
        } else {
            throw new IdentityVerifyException("任务不存在");
        }

        List<String> successKdList = new ArrayList<>();
        List<String> failKdList = new ArrayList<>();

        String ksjhbh = paramDO.getKsjhbh();
        String rootFilePath = pkgPath + File.separator + ksjhbh + File.separator + DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
        if (StringUtils.isNotBlank(ksjhbh)) {
            ksProcessPeriodService.initPeriod(EventPeriodKey.SJDB, ksjhbh, "数据打包");
            if (paramDO.getPackMode().equals(PkgCatalogEnum.KD.getValue()) || paramDO.getPackMode().equals(PkgCatalogEnum.QL.getValue())) {
                ksProcessPeriodService.initPeriod(EventPeriodKey.SJDB_KD, ksjhbh, "数据打包");
            }
            if (paramDO.getPackMode().equals(PkgCatalogEnum.KC.getValue()) || paramDO.getPackMode().equals(PkgCatalogEnum.QL.getValue())) {
                ksProcessPeriodService.initPeriod(EventPeriodKey.SJDB_KC, ksjhbh, "数据打包");
            }
            // 任务开始设置阶段状态
            ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB, ksjhbh, PeriodStatusEnum.DOING, "0");

//            double totalProgress = 100.0;
//            if (paramDO.getPackEnumList().contains(PackEnum.Pack_GxHisomeFbJkry)) {
//                totalProgress = 50.0;
//                try {
//                    zipJkryFb(paramDO.getKsjhbh(), task, rootFilePath);
//                } catch (Exception e) {
//                    logger.error("非标监考人员数据打包失败, " + e.getMessage(), e);
//                }
//                paramDO.getPackEnumList().remove(PackEnum.Pack_GxHisomeFbJkry);
//            }
//            double currentProgress = Double.parseDouble(task.getTProgress());

            // 打包
            logger.info("开始打包");
            Map<String, PackFileParam> fileParamMap = new HashMap<>();
            Example kdxxExam = new Example(KsKdxx.class);
            kdxxExam.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
            kdxxExam.selectProperties("bzhkdid", "bzhkdmc", "kdmc");
            kdxxExam.setDistinct(true);
            List<KsKdxx> kdxxes = ksKdxxService.selectListByExample(kdxxExam);
            KsKdxx ksKdxx = new KsKdxx();
            if (kdxxes != null && kdxxes.size() != 0) {
                ksKdxx = kdxxes.get(0);
            }
            if (paramDO.getPackMode().equals(PkgCatalogEnum.KD.getValue()) || paramDO.getPackMode().equals(PkgCatalogEnum.QL.getValue())) {
                initKdPkgStatus(paramDO);
                PackFileParam param = new PackFileParam();
                if (ksKdxx != null) {
                    param.bzhkdid = ksKdxx.getBzhkdid();
                    param.bzhkdmc = ksKdxx.getBzhkdmc();
                    param.kdmc = ksKdxx.getKdmc();
                }
                param.rootFilePath = rootFilePath;
                param.ksjhbh = ksjhbh;
                param.taskId = paramDO.getTaskId();
                param.useCache = paramDO.getUseCache();
                param.mode = PkgCatalogEnum.KD.getValue();
                fileParamMap.put(ksKdxx.getBzhkdid(), param);
            }
            List<KsKcxx> kcxxList = ksKcxxService.getdbkcList(ksjhbh);
            if (paramDO.getPackMode().equals(PkgCatalogEnum.KC.getValue()) || paramDO.getPackMode().equals(PkgCatalogEnum.QL.getValue())) {
                for (KsKcxx ksKcxx : kcxxList) {
                    PackFileParam param = new PackFileParam();
                    if (ksKdxx != null) {
                        param.bzhkdid = ksKdxx.getBzhkdid();
                        param.bzhkdmc = ksKdxx.getBzhkdmc();
                        param.kdmc = ksKdxx.getKdmc();
                    }
                    param.rootFilePath = rootFilePath;
                    param.ksjhbh = ksjhbh;
                    param.taskId = paramDO.getTaskId();
                    param.useCache = paramDO.getUseCache();
                    param.mode = PkgCatalogEnum.KC.getValue();
                    param.kcxx = ksKcxx;
                    fileParamMap.put(ksKcxx.getBzhkcid(), param);
                }
            }
            // 当前任务打包完成数
            int kdChilPkgDoneNum = 0;
            int kcPkgDoneNUm = 0;
            double progress = 0;
            // 当前任务打包总数（考场）
            int fileTatol = 0;
            // 打包总数 （考场 + 考点）
            int dbFileTatol = kcxxList.size() + kdxxes.size();
            for (PackFileParam param : fileParamMap.values()) {
                String dataType = "";
                PackEnum currentPackEnum = null;
                boolean flag = true;
                List<String> errorDesc = new ArrayList<>();
                if (param.kcxx == null) {
                    // 当前任务打包总数（考点）
                    fileTatol = paramDO.getPackEnumList().size();
                } else if (param.kcxx != null) {
                    initKcPkgStatus(param);
                    fileTatol = kcxxList.size();
                }
                for (PackEnum packEnum : paramDO.getPackEnumList()) {
                    currentPackEnum = packEnum;
                    int sjbQk = SjbQkEnum.IN_PROGRESS.getValue();
                    try {
                        // 打包
                        handleStatus(param, currentPackEnum, sjbQk, null);
                        handlePkgByKd(param, packEnum);
                        sjbQk = SjbQkEnum.FINISH.getValue();
                        // 更新打包事件阶段进度
                        if (param.kcxx == null) {
                            kdChilPkgDoneNum++;
                            progress = kdChilPkgDoneNum * 100 / fileTatol;
                            ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB_KD, ksjhbh, PeriodStatusEnum.DOING,
                                    String.format("%.2f", progress));
                            if (paramDO.getPackMode().equals(PkgCatalogEnum.KD.getValue())) {
                                task.setTProgress(String.format("%.2f", progress));
                            }
                        }
                        // 更新相应数据包打包事件状态为已处理(如果存在未处理)
                        ksProcessEventService.insertSjdb(ksjhbh, packEnum.getType(), EventTypeSjdbEnum.SUCCESS, EventHandleEnum.TREATED, "", "", param.useCache.toString(), param.kcxx);

                    } catch (Exception e) {
                        flag = false;
                        dataType = packEnum.getType();
                        if (!StringUtils.contains(e.getMessage(), "下载文件错误")
                                && !StringUtils.contains(e.getMessage(), "打包文件错误")
                                && !StringUtils.contains(e.getMessage(), "上传文件错误")) {
                            errorDesc.add(packEnum.getDesc() + StringUtils.substring(e.getMessage(), 0, 120));
                        }
                        e.printStackTrace();
                        logger.error(e.getMessage(), e);
                        logger.warn("打包失败-->", e);
                        logger.error("打包失败,打包模块:{}", packEnum.getDesc());
                        String errorMsg = "异常为[" + e.getMessage() + "]";
                        errorMsg = StringUtils.substring(errorMsg, 0, 1995);
                        KsKssjPkgException exception = new KsKssjPkgException();
                        exception.setId(IdGenerateUtil.generateId());
                        exception.setPkgTaskId(param.taskId);
                        exception.setKsjhbh(param.ksjhbh);
                        exception.setTDesc(errorMsg);
                        exception.setTMode(PkgExceptionModeEnum.getByPkgEnum(packEnum.getCode()).getCode());
                        exception.setTType(PkgExceptionTypeEnum.getByPkgEnumAndType(packEnum.getCode(), "020").getCode());
                        exception.setBzhkdid(param.bzhkdid);
                        exception.setKdmc(param.kdmc);
                        exception.setCreateTime(DateUtil.getCurrentDT());
                        exception.setUpdateTime(DateUtil.getCurrentDT());
                        if (param.kcxx != null) {
                            exception.setBzhkcbh(param.kcxx.getBzhkcid());
                            exception.setBzhkcmc(param.kcxx.getBzhkcmc());
                        }
                        ksKssjPkgExceptionService.insertSingle(exception);

                        sjbQk = SjbQkEnum.FAILURE.getValue();
                    } finally {
                        String exportFileId = param.exportFileId;
                        handleStatus(param, currentPackEnum, sjbQk, exportFileId);
                    }
                }
                // 更新打包事件阶段进度
                if (param.kcxx != null) {
                    kcPkgDoneNUm++;
                    progress = kcPkgDoneNUm * 100 / fileTatol;
                    ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB_KC, ksjhbh, PeriodStatusEnum.DOING,
                            String.format("%.2f", progress));
                    task.setTProgress(String.format("%.2f", progress));
                }
                logger.info("打包完成");
                ksKssjPkgTaskService.updateByPrimaryKey(task);

                if (flag) {
                    // 插入事件
                    ksProcessEventService.insertSjdb(ksjhbh, dataType, EventTypeSjdbEnum.SUCCESS, EventHandleEnum.DEFAULT, "打包数据成功", "打包数据成功", param.useCache.toString(), param.kcxx);
                }
                if (!errorDesc.isEmpty()) {
                    // 插入事件
                    ksProcessEventService.insertSjdb(ksjhbh, dataType, EventTypeSjdbEnum.FAIL_DBSB, EventHandleEnum.UNTREATED, "打包数据错误: 其他错误", "打包数据错误:" + StringUtils.substring(String.join(",", errorDesc), 0, 1990), param.useCache.toString(), param.kcxx);
                }
            }

            // 设置考点或考场阶段状态
            Integer packMode = paramDO.getPackMode();
            int fileFinish = getPkgProcessPeriodStatus(ksjhbh, packMode);
            if (packMode == 1) {
                if (fileFinish == 1) {
                    ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB_KD, ksjhbh, PeriodStatusEnum.DONE, "");
                }
            } else if (packMode == 2) {
                if (fileFinish == kcxxList.size()) {
                    ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB_KC, ksjhbh, PeriodStatusEnum.DONE, "");
                }
            } else if (packMode == 3) {
                if (fileFinish == dbFileTatol) {
                    ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB_KD, ksjhbh, PeriodStatusEnum.DONE, "");
                    ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB_KC, ksjhbh, PeriodStatusEnum.DONE, "");
                }
            }

            // 任务结束设置总体阶段状态
            int dbFileFinish = getPkgProcessPeriodStatus(ksjhbh, 3);
            progress = dbFileFinish * 100 / dbFileTatol;
            if (dbFileFinish == dbFileTatol) {
                ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB, ksjhbh, PeriodStatusEnum.DONE, String.format("%.2f", progress));
            } else {
                ksProcessPeriodService.updatePeriod(EventPeriodKey.SJDB, ksjhbh, PeriodStatusEnum.DOING, String.format("%.2f", progress));
            }
        }
        initDistributeStatus(paramDO.getKsjhbh());

        StartPackVO startPackVO = new StartPackVO();
        startPackVO.setSuccessKdList(successKdList);
        startPackVO.setFailKdList(failKdList);
        return startPackVO;
    }

    private void initDistributeStatus(String ksjhbh){
        KsKsjh ksjh = ksKsjhService.selectSingleByKey(ksjhbh);
        Integer kzqy = ksjh.getKzqy();
        KsKssjDistributeStatus initDistributeStatus = new KsKssjDistributeStatus();
        initDistributeStatus.setCommonPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        initDistributeStatus.setStuinfPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        initDistributeStatus.setStuzpPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0) {
            initDistributeStatus.setJkrybpxxPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        }
        if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0) {
            initDistributeStatus.setJkryjbxxPkgStatus(DataDistributeStatusEnum.Wait.getCode());
            initDistributeStatus.setJkryzpPkgStatus(DataDistributeStatusEnum.Wait.getCode());
        }
        Example example = new Example(KsKssjDistributeStatus.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh);

        ksDistributeStatusService.updateByExampleSelective(initDistributeStatus, example);
    }

    private void initKcPkgStatus(PackFileParam param) {
        String ksjhbh = param.ksjhbh;

        Example example = new Example(KsKssjPkgStatus.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("pkgCatalog", PkgCatalogEnum.KC.getCode())
                .andEqualTo("bzhkcbh", param.kcxx.getBzhkcid());
        List<KsKssjPkgStatus> list = ksKssjPkgStatusService.selectListByExample(example);
        KsKssjPkgStatus kdStatus = new KsKssjPkgStatus();
        if (!CollectionUtils.isEmpty(list)) {
            kdStatus = list.get(0);
        }

        // 第一次打包初始化为未启用
        if (StringUtils.isBlank(kdStatus.getId())) {
            kdStatus.setKssjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setPzsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkryjbsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkrybpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setKszpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkryzpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }
        // 考试计划配置为不打包的设置为未启用
        KsKsjh ksKsjh = ksKsjhService.selectSingleByKey(ksjhbh);
        Integer kzqy = ksKsjh.getKzqy();

        if ((kzqy & KzqyEnum.TZZQY.getValue()) == 0) {

        }
        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) == 0) {
            kdStatus.setJkrybpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }
        if ((kzqy & KzqyEnum.JKRYQY.getValue()) == 0) {
            kdStatus.setJkryjbsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkryzpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }
        if (StringUtils.isBlank(kdStatus.getId())) {
            kdStatus.setId(IdGenerateUtil.generateId());
            kdStatus.setKsjhbh(ksjhbh);
            kdStatus.setCreateTime(new Date());
            kdStatus.setUpdateTime(new Date());
            kdStatus.setPkgCatalog(PkgCatalogEnum.KC.getCode());
            kdStatus.setBzhkdid(param.bzhkdid);
            kdStatus.setBzhkdmc(param.bzhkdmc);
            kdStatus.setBzhkcbh(param.kcxx.getBzhkcid());
            kdStatus.setBzhkcmc(param.kcxx.getBzhkcmc());
            ksKssjPkgStatusService.insertSingle(kdStatus);
        } else {
            kdStatus.setUpdateTime(new Date());
            ksKssjPkgStatusService.updateByPrimaryKeySelective(kdStatus);
        }
    }


    private void initKdPkgStatus(PackParamDO param) {
        String ksjhbh = param.getKsjhbh();

        Example example = new Example(KsKssjPkgStatus.class);
        example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("pkgCatalog", PkgCatalogEnum.KD.getCode());
        List<KsKssjPkgStatus> list = ksKssjPkgStatusService.selectListByExample(example);
        KsKssjPkgStatus kdStatus = new KsKssjPkgStatus();
        if (!CollectionUtils.isEmpty(list)) {
            kdStatus = list.get(0);
        }

        // 第一次打包初始化为未启用
        if (StringUtils.isBlank(kdStatus.getId())) {
            kdStatus.setKssjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setPzsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkryjbsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkrybpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setKszpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkryzpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }
        // 考试计划配置为不打包的设置为未启用
        KsKsjh ksKsjh = ksKsjhService.selectSingleByKey(ksjhbh);
        Integer kzqy = ksKsjh.getKzqy();

        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) == 0) {
            kdStatus.setJkrybpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }

        if ((kzqy & KzqyEnum.JKRYQY.getValue()) == 0) {
            kdStatus.setJkryjbsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
            kdStatus.setJkryzpsjbQk(SjbQkEnum.NOT_ENABLED.getValue());
        }
        for (PackEnum packEnum : param.getPackEnumList()) {
            int sjbQk = SjbQkEnum.NOT_STARTED.getValue();
            switch (packEnum) {
                case Pack_GxHisomeStu:
                    kdStatus.setKssjbQk(sjbQk);
                    break;
                case Pack_GxHisomeJkry:
                    kdStatus.setJkryjbsjbQk(sjbQk);
                    break;
                case Pack_GxHisomeJkryBp:
                    kdStatus.setJkrybpsjbQk(sjbQk);
                    break;
                case Pack_GxHisomeCommon:
                    kdStatus.setPzsjbQk(sjbQk);
                    break;
                case Pack_GxHisomeKdKsZp:
                    kdStatus.setKszpsjbQk(sjbQk);
                    break;
                case Pack_GxHisomeKdJkryZp:
                    kdStatus.setJkryzpsjbQk(sjbQk);
                    break;
            }
        }

        if (StringUtils.isBlank(kdStatus.getId())) {
            kdStatus.setId(IdGenerateUtil.generateId());
            kdStatus.setKsjhbh(ksjhbh);
            kdStatus.setCreateTime(new Date());
            kdStatus.setUpdateTime(new Date());
            kdStatus.setPkgCatalog(PkgCatalogEnum.KD.getCode());
            ksKssjPkgStatusService.insertSingle(kdStatus);
        } else {
            kdStatus.setUpdateTime(new Date());
            ksKssjPkgStatusService.updateByPrimaryKeySelective(kdStatus);
        }

    }

    /**
     * 获取打包完成状态
     *
     * @param ksjhbh   考试计划编号
     * @param packMode 打包类型 1-考点 2-考场 3-考点+考场
     * @return 打包完成数
     */
    private int getPkgProcessPeriodStatus(String ksjhbh, Integer packMode) {
        Integer sjbQk = SjbQkEnum.FINISH.getValue();
        Example emStatus = new Example(KsKssjPkgStatus.class);
        Criteria criteria = emStatus.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("kssjbQk", sjbQk)
                .andEqualTo("pzsjbQk", sjbQk)
                .andEqualTo("kszpsjbQk", sjbQk);

        KsKsjh ksjh = ksKsjhService.selectSingleByKey(ksjhbh);
        Integer kzqy = ksjh.getKzqy();
        if ((kzqy & KzqyEnum.JKRYBPQY.getValue()) != 0) {
            criteria.andEqualTo("jkrybpsjbQk", sjbQk);
        }
        if ((kzqy & KzqyEnum.JKRYQY.getValue()) != 0) {
            criteria.andEqualTo("jkryjbsjbQk", sjbQk);
            criteria.andEqualTo("jkryzpsjbQk", sjbQk);
        }
        if (packMode.equals(PkgCatalogEnum.KC.getValue())) {
            criteria.andEqualTo("pkgCatalog", PkgCatalogEnum.KC.getCode());
        }
        if (packMode.equals(PkgCatalogEnum.KD.getValue())) {
            criteria.andEqualTo("pkgCatalog", PkgCatalogEnum.KD.getCode());
        }

        List<KsKssjPkgStatus> ksKsxxExportStatuses = ksKssjPkgStatusService.selectListByExample(emStatus);
        return ksKsxxExportStatuses.size();
    }

    protected KsKssjPkgFile handlePkgByKd(PackFileParam param, PackEnum packEnum) throws IOException {
        return doHandlePkg(param, packEnum);
    }

    protected KsKssjPkgFile doHandlePkg(PackFileParam param, PackEnum packEnum) throws IOException {
        KsKssjPkgFile packResult = new KsKssjPkgFile();

        try {
            packResult = writeFile(param, packEnum);
            packResult.setCreateTime(new Date());

            // 将整个考点内的信息打包
            String zipTarget = param.targetFilePath;
            String zipFilePath = param.rootFilePath + File.separator + "ZIP";
            String kcPath = "";
            if (param.kcxx != null) {
                kcPath = "_" + param.kcxx.getBzhkcid();
            }
            String zipFileName = param.ksjhbh + kcPath + "_" + packEnum.getBz() + ".zip";

            String result = "";
            try {
                if (secretEnable) {
                    result = CompressUtil.zip(zipTarget, zipFilePath + File.separator + zipFileName, secretKey);
                } else {
                    result = CompressUtil.zip(zipTarget, zipFilePath + File.separator + zipFileName, "");
                }
            } catch (Exception e) {
                ksProcessEventService.insertSjdb(param.ksjhbh, packEnum.getType(), EventTypeSjdbEnum.FAIL_DBSB, EventHandleEnum.UNTREATED, "打包文件错误：压缩文件失败", "打包文件错误：压缩文件失败：" + e.getMessage(), param.useCache.toString(), param.kcxx);
                logger.error("打包失败,", e);
                throw e;
            }

            ZipFile zipFile = new ZipFile(result);
            if (!zipFile.isValidZipFile()) {
                ksProcessEventService.insertSjdb(param.ksjhbh, packEnum.getType(), EventTypeSjdbEnum.FAIL_DBSB, EventHandleEnum.UNTREATED, "打包文件错误：压缩包校验错误", "打包文件错误：压缩包校验错误", param.useCache.toString(), param.kcxx);
                throw new IdentityVerifyException("压缩包异常");
            }

            packResult.setFilePath(result);
            packResult.setFileName(zipFileName);

            //上传到dfs文件服务器
            DfsResultDO dfsResult = null;
            try {
                dfsResult = uploadToDfs(result);
            } catch (Exception e) {
                ksProcessEventService.insertSjdb(param.ksjhbh, packEnum.getType(), EventTypeSjdbEnum.FAIL_SCCW, EventHandleEnum.UNTREATED, "上传文件错误：上传文件到DFS失败", "上传文件错误：上传文件到DFS失败：" + e.getMessage(), param.useCache.toString(), param.kcxx);
                logger.error("打包失败,", e);
                throw new IdentityVerifyException("上传文件错误");
            }
            packResult.setDfsFileObjUri(dfsResult.dfsId);
            packResult.setDfsFilePathUri(dfsResult.dfsPath);

            //设置md5值
            String fileMd = ksKssjPkgFileService.getFileHash(result);
            packResult.setFileMd(fileMd);

        } catch (Exception e) {
            throw e;
        } finally {
            // 打包基础信息
            if (StringUtils.isBlank(packResult.getId())) {
                packResult.setId(IdGenerateUtil.generateId());
            }
            packResult.setKsjhbh(param.ksjhbh);
            packResult.setPkgType(packEnum.getCode());
            packResult.setUpdateTime(new Date());
            packResult.setSczt(ScztEnum.NOTDEL.getCode());
            packResult.setPkgCatalog(PkgCatalogEnum.getByValue(param.mode).getCode());
            packResult.setKdmc(param.kdmc);
            packResult.setBzhkdid(param.bzhkdid);
            packResult.setBzhkdmc(param.bzhkdmc);

            // 版本
            Example example = new Example(KsKssjPkgFile.class);
            example.createCriteria().andEqualTo("ksjhbh", param.ksjhbh)
                    .andEqualTo("pkgType", packEnum.getCode());
            if (param.kcxx != null) {
                example.and().andEqualTo("pkgCatalog", PkgCatalogEnum.KC.getCode())
                        .andEqualTo("bzhkcbh", param.kcxx.getBzhkcid());
            } else {
                example.and().andEqualTo("pkgCatalog", PkgCatalogEnum.KD.getCode());
            }
            example.orderBy("version").desc();
            example.selectProperties("version");
            List<KsKssjPkgFile> files = ksKssjPkgFileService.selectListByExample(example);
            if (files != null && files.size() != 0) {
                Integer version = Integer.valueOf(files.get(0).getVersion()) + 1;
                packResult.setVersion(String.valueOf(version));
                KsKssjPkgFile pkgFile = new KsKssjPkgFile();
                pkgFile.setSczt(ScztEnum.DEL.getCode());
                ksKssjPkgFileService.updateByExampleSelective(pkgFile, example);
            } else {
                packResult.setVersion("1");
            }

            ksKssjPkgFileService.insertSingle(packResult);

            param.exportFileId = packResult.getId();
        }

        return packResult;
    }

    protected void handleStatus(PackFileParam param, PackEnum packEnum, int sjbQk, String exportFileId) {
        String ksjhbh = param.ksjhbh;
        PkgCatalogEnum catalogEnum = PkgCatalogEnum.getByValue(param.mode);
        Example example = new Example(KsKssjPkgStatus.class);
        Criteria criteria = example.createCriteria().andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("pkgCatalog", catalogEnum.getCode());
        if (catalogEnum.equals(PkgCatalogEnum.KC)) {
            criteria.andEqualTo("bzhkcbh", param.kcxx.getBzhkcid());
        }
        List<KsKssjPkgStatus> list = ksKssjPkgStatusService.selectListByExample(example);
        KsKssjPkgStatus kdStatus = new KsKssjPkgStatus();
        if (!CollectionUtils.isEmpty(list)) {
            kdStatus = list.get(0);
        }

//		int sjbQk = 1;
        switch (packEnum) {
            case Pack_GxHisomeStu:
                kdStatus.setKssjbQk(sjbQk);
                kdStatus.setFileKssjbId(exportFileId);
                break;
            case Pack_GxHisomeJkry:
                kdStatus.setJkryjbsjbQk(sjbQk);
                kdStatus.setFileJkryjbsjbId(exportFileId);
                break;
            case Pack_GxHisomeJkryBp:
                kdStatus.setJkrybpsjbQk(sjbQk);
                kdStatus.setFileJkrybpsjbId(exportFileId);
                break;
            case Pack_GxHisomeCommon:
                kdStatus.setPzsjbQk(sjbQk);
                kdStatus.setFilePzsjbId(exportFileId);
                break;
            case Pack_GxHisomeKdKsZp:
                kdStatus.setKszpsjbQk(sjbQk);
                kdStatus.setFileKszpsjbId(exportFileId);
                break;
            case Pack_GxHisomeKdJkryZp:
                kdStatus.setJkryzpsjbQk(sjbQk);
                kdStatus.setFileJkryzpsjbId(exportFileId);
                break;
            default:
                break;
        }
        if (StringUtils.isBlank(kdStatus.getId())) {
            kdStatus.setId(IdGenerateUtil.generateId());
            kdStatus.setKsjhbh(ksjhbh);
            kdStatus.setCreateTime(new Date());
            kdStatus.setUpdateTime(new Date());
            if (catalogEnum.equals(PkgCatalogEnum.KC)) {
                kdStatus.setBzhkcbh(param.kcxx.getBzhkcid());
                kdStatus.setBzhkcmc(param.kcxx.getBzhkcmc());
            }
            ksKssjPkgStatusService.insertSingle(kdStatus);
        } else {
            kdStatus.setUpdateTime(new Date());
            kdStatus.setLatestOperateTime(new Date());
            kdStatus.setBzhkdid(param.bzhkdid);
            kdStatus.setBzhkdmc(param.bzhkdmc);
            ksKssjPkgStatusService.updateByPrimaryKeySelective(kdStatus);
        }

    }


    protected KsKssjPkgFile writeFile(PackFileParam param, PackEnum packType) throws IOException {


        // 打包结果信息
        KsKssjPkgFile packResult = null;

        switch (packType) {
            case Pack_ZjJyd:
                packResult = writeFileZjJyd(param);
                break;
            case Pack_ZjJrgw:
                packResult = writeFileZjJrgw(param);
                break;
            default:
                packResult = writeFileGxHisome(param, packType);
        }
        return packResult;
    }


    /**
     * 将一个考点下的考试信息写入文件
     *
     * @param param
     * @return
     */
    protected KsKssjPkgFile writeFileZjJyd(PackFileParam param) {


        String filePath = param.rootFilePath + File.separator + "UNZIP" + File.separator + param.ksjhbh;

        // 考生考试文件
        File userExamInfoFile = new File(filePath + File.separator + "userExamInfo.txt");
        // 考生信息文件
        File userInfoFile = new File(filePath + File.separator + "userInfo.txt");
        // 考生照片id
        Set<String> zpIds = new HashSet<>();

        try {
            FileUtils.write(userExamInfoFile, "考试场次编号\t证件号码\t准考证号\t考场号\t座位号\n", StandardCharsets.UTF_8);
            FileUtils.write(userInfoFile, "证件号\t姓名\t性别\n", StandardCharsets.UTF_8);
            // 保存写入过的考生信息，避免重复写入
            Set<String> zjhmExists = new HashSet<>();
            List<KsBpxx> bpxxList = doGetBpxx(param.ksjhbh);
            List<KsBmxx> bmxxList = getBmxxByBp(bpxxList);
            Map<String, KsBmxx> bmxxMap = getBmxxMap(bmxxList);
            // 写入考生考试信息
            for (KsBpxx ksBpxx : bpxxList) {
                KsBmxx ksBmxx = bmxxMap.get(ksBpxx.getKsh());
                if (ksBmxx != null) {
                    String outputBpxx = ksBpxx.getCcm() + "\t" + ksBmxx.getSfzjhm() + "\t"
                            + ksBpxx.getZkzh() + "\t" + ksBpxx.getKcbh() + "\t" + ksBpxx.getZwh() + "\n";  // 考试信息
                    FileUtils.write(userExamInfoFile, outputBpxx, StandardCharsets.UTF_8, true);
                }
            }

            // 写入考生报名信息
            for (KsBmxx ksBmxx : bmxxList) {

                zpIds.add(ksBmxx.getZp());

                String zjhm = ksBmxx.getSfzjhm();
                if (!zjhmExists.contains(zjhm)) {
                    String ouputInfo = zjhm + "\t" + ksBmxx.getXm() + "\t" + ksBmxx.getXb() + "\n"; //考生信息
                    FileUtils.write(userInfoFile, ouputInfo, StandardCharsets.UTF_8, true);
                    zjhmExists.add(zjhm);
                } else {
                    logger.warn("该考生已经添加入userInfo.txt[{}]", ksBmxx);
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
        }

        // 照片打包
        UploadItemVO uploadItemVO = zipPic(zpIds);
        KsKssjPkgFile ksKsxxExportFile = null;
        if (uploadItemVO != null) {

            // 照片打包信息记录
            ksKsxxExportFile = new KsKssjPkgFile();
            ksKsxxExportFile.setDfsFileObjUri(uploadItemVO.getId());
            ksKsxxExportFile.setDfsFilePathUri(uploadItemVO.getFilePath());
        }

        return ksKsxxExportFile;
    }

    /**
     * 将一个考点下的考试信息以场次_考场为单位打包
     *
     * @param param
     * @return
     * @throws IOException
     * @throws FileNotFoundException
     */
    protected KsKssjPkgFile writeFileZjJrgw(PackFileParam param) throws FileNotFoundException, IOException {

        String filePath = param.rootFilePath + File.separator + "UNZIP" + File.separator + "_" + param.ksjhbh;
        String filePathSrc = param.rootFilePath + File.separator + "UNZIP" + File.separator + "_" + param.ksjhbh + "_src";

        // 照片信息
        List<SubFileInfoVO> fileInfoVOS = new ArrayList<>();

        List<KsBpxx> bpxxList = doGetBpxx(param.ksjhbh);
        List<KsBmxx> bmxxList = getBmxxByBp(bpxxList);
        Map<String, KsBmxx> bmxxMap = getBmxxMap(bmxxList);
        Map<String, List<KsBpxx>> kcMap = getKcMap(bpxxList);

        for (Entry<String, List<KsBpxx>> entry : kcMap.entrySet()) {
            String key = entry.getKey();
            List<KsBpxx> kcBpxxList = entry.getValue();
            String kcFilePath = filePathSrc + File.separator + key;  // 考场文件路径

            File kcFileDir = new File(kcFilePath);
            if (!kcFileDir.exists()) {
                kcFileDir.mkdirs();
            }

            // 考生考试文件
            File userExamInfoFile = new File(kcFilePath + File.separator + "userExamInfo.txt");
            // 考生信息文件
            File userInfoFile = new File(kcFilePath + File.separator + "userInfo.txt");
            // 照片信息
            SubFileInfoVO subFileInfoVO = new SubFileInfoVO();
            subFileInfoVO.setFileDicName(key);
            Set<String> fileIds = new HashSet<>();

            try {
                FileUtils.write(userExamInfoFile, "考试场次编号\t证件号码\t准考证号\t考场号\t座位号\n", StandardCharsets.UTF_8);
                FileUtils.write(userInfoFile, "证件号\t姓名\t性别\n", StandardCharsets.UTF_8);
                for (KsBpxx ksBpxx : kcBpxxList) {
                    KsBmxx ksBmxx = bmxxMap.get(ksBpxx.getKsh());
                    if (ksBmxx != null) {
                        // 考生考试信息
                        String outputBpxx = ksBpxx.getCcm() + "\t" + ksBmxx.getSfzjhm() + "\t"
                                + ksBpxx.getZkzh() + "\t" + ksBpxx.getKcbh() + "\t" + ksBpxx.getZwh() + "\n";  // 考试信息
                        FileUtils.write(userExamInfoFile, outputBpxx, StandardCharsets.UTF_8, true);
                        // 考生信息
                        String ouputInfo = ksBmxx.getSfzjhm() + "\t" + ksBmxx.getXm() + "\t" + ksBmxx.getXb() + "\n"; //考生信息
                        FileUtils.write(userInfoFile, ouputInfo, StandardCharsets.UTF_8, true);
                        // 照片信息
                        fileIds.add(ksBmxx.getZp());
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
                logger.error(e.getMessage(), e);
            }

            // 考场打包
//				zipFile(filePath, key + ".zip", kcFilePath);

            subFileInfoVO.setKeys(new ArrayList<>(fileIds));
            fileInfoVOS.add(subFileInfoVO);
        }

        // 打包照片
        UploadItemVO uploadItemVO = zipPic(param.ksjhbh + "_zp", fileInfoVOS);
        KsKssjPkgFile ksKssjPkgFile = null;
        if (uploadItemVO != null) {

            // 照片打包信息记录
            ksKssjPkgFile = new KsKssjPkgFile();
            ksKssjPkgFile.setDfsFileObjUri(uploadItemVO.getId());
            ksKssjPkgFile.setDfsFilePathUri(uploadItemVO.getFilePath());
        }

        return ksKssjPkgFile;

    }


    protected KsKssjPkgFile writeFileGxHisome(PackFileParam param, PackEnum packType) throws IOException {
        KsKssjPkgFile ksKsxxExportFile = new KsKssjPkgFile();

        String type = "KD";
        if (param.mode.equals(PkgCatalogEnum.KC.getValue())) {
            type = "KC" + File.separator + param.kcxx.getBzhkcid();
        }

        String filePath = param.rootFilePath + File.separator + "UNZIP" + File.separator + type + File.separator;

        if (param.mode.equals(PkgCatalogEnum.KC.getValue())) {
            ksKsxxExportFile = dataPkgServices.pack(packType, param.ksjhbh, filePath, param.taskId, param.useCache, param.kcxx);
        }
        if (param.mode.equals(PkgCatalogEnum.KD.getValue())) {
            ksKsxxExportFile = dataPkgServices.pack(packType, param.ksjhbh, filePath, param.taskId, param.useCache);
        }
        param.targetFilePath = filePath + packType.getBz();

        return ksKsxxExportFile;

    }

//	protected KsKssjPkgFile doPkgGxCommon(PackFileParam param,String filePath) {
//
//		KsKssjPkgFile ksKsxxExportFile = new KsKssjPkgFile();
//
//		File ksjhFile = new File(filePath + File.separator + KsPkgFileEnum.KSJH.getCode()+ SuffixTxt);
//		File ksccFile = new File(filePath + File.separator + KsPkgFileEnum.KSCC.getCode()+ SuffixTxt);
//		File kcsbxxFile = new File(filePath + File.separator + KsPkgFileEnum.KCSBXX.getCode()+ SuffixTxt);
//
//		KsKsjh ksjh = ksKsjhService.selectSingleByKey(param.ksjhbh);
//		KsKsjh4PkgDO ksjhPkg =  PkgDOUtil.format(ksjh);
//
//		Example ksBpxxExample = new Example(KsBpxx.class);
//		ksBpxxExample.setDistinct(true);
//		ksBpxxExample.selectProperties("ccm");
//		ksBpxxExample.createCriteria().andEqualTo("scztw", ScztEnum.NOTDEL.getCode())
//				.andEqualTo("ksjhbh", param.ksjhbh);
//		List<KsBpxx> ksBpxxList = ksBpxxService.selectListByExample(ksBpxxExample);
//		Example ksccExample = new Example(KsKscc.class);
//		Criteria criteria = ksccExample.createCriteria().andEqualTo("ksjhbh", param.ksjhbh).andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
//		if (!CollectionUtils.isEmpty(ksBpxxList)) {
//			criteria.andIn("ccm", ksBpxxList.stream().map(KsBpxx::getCcm).distinct().collect(Collectors.toList()));
//		}
//		List<KsKscc> ksccList = ksKsccService.selectListByExample(ksccExample);
//
//		List<KsKsjh4PkgDO> ksKsjh4PkgDOS = new ArrayList<>();
//		ksKsjh4PkgDOS.add(ksjhPkg);
//		SqliteManager.insertUserInfo(ksjhFile.getParent(), SqliteDbNameEnum.Pzxx.getDbName(), ksKsjh4PkgDOS, KsKsjh4PkgDO.class);
//
//		int sqliteKsccCount = PkgDOUtil.writeSqliteKscc(ksccFile, ksccList);
//
//
//		int sqliteKskcSbxxCount = 0;
//			Example example = new Example(KsKcxx.class);
//			example.createCriteria().andEqualTo("ksjhbh", param.ksjhbh)
//					.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
//			List<KsKcxx> kdkcxxList = ksKcxxService.selectListByExample(example);
//
//			Map<String, List<GxSbxxLbItemVO>> gxSbxxMap = Maps.newHashMap();
//			Map<String, String> wlwzMap = new HashMap<>();
//			Map<String, List<String>> xlkcMap = new HashMap<>();
//			if (CollectionUtil.isNotEmpty(kdkcxxList)) {
//				List<String> bzhkcidList = kdkcxxList.stream().map(KdKcxx::getBzhkcid).distinct().collect(Collectors.toList());
//				Example emBzhkc = new Example(CsBzhkc.class);
//				emBzhkc.createCriteria()
//						.andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
//						.andIn("bzhkcid", bzhkcidList);
//				List<CsBzhkc> csBzhkcList = csBzhkcService.selectByExample(emBzhkc);
//				// 考场物理位置map
//				wlwzMap = csBzhkcList.stream().collect(Collectors.toMap(CsBzhkc::getBzhkcid, bzhkc -> bzhkc.getJzmc() + bzhkc.getSzlc() + "楼" + bzhkc.getBzhkcmc()));
//
//				CxSbxxLb4PkgDTO cxSbxxLb4PkgDto = new CxSbxxLb4PkgDTO();
//				cxSbxxLb4PkgDto.setJgbh(param.bzhkdid);
//				cxSbxxLb4PkgDto.setJglx("kd");
//				Wrapper<CxSbxxLb4PkgVO> wrapper = gxsfhyXcmcFeignApi.cxSbxxLb4Pkg(cxSbxxLb4PkgDto);
//				CxSbxxLb4PkgVO vo = wrapper.getResult();
//				if (vo != null && !CollectionUtils.isEmpty(vo.getGxSbxxLb())) {
//					List<GxSbxxLbItemVO> gxSbxxLb = vo.getGxSbxxLb();
//					gxSbxxLb.stream().forEach(sbxx -> {
//						gxSbxxMap.computeIfAbsent(sbxx.getCsbh(), k -> new ArrayList<>());
//						gxSbxxMap.get(sbxx.getCsbh()).add(sbxx);
//					});
//				}
//
//				// 获取相邻考场信息
//				xlkcMap = csXlkcService.generateXlkc(kdkcxxList);
//			}
//
//			//写入考场设备信息
//			sqliteKskcSbxxCount += PkgDOUtil.writeSqliteKskcSbxx(kcsbxxFile, kdkcxxList, gxSbxxMap, wlwzMap, xlkcMap,null);
//		}
//
//		ksKsxxExportFile.setPkgDesc(String.format("%s,%s,%s", StringFormatUtils.pkgDescFormat("考点",sqliteKdxxCount),
//				StringFormatUtils.pkgDescFormat("考试场次",sqliteKsccCount),
//				StringFormatUtils.pkgDescFormat("考场设备关系",sqliteKskcSbxxCount)));
//		return ksKsxxExportFile;
//	}

    protected KsKssjPkgFile doPkgGxStu(PackFileParam param, String filePath) throws IOException {
        KsKssjPkgFile ksKssjPkgFile = new KsKssjPkgFile();

        Set<String> zjhmExists = new HashSet<>();
        // 考生编排
        File userExamInfoFile = new File(filePath + File.separator + KsPkgFileEnum.KSBPXX.getCode() + SuffixTxt);
        // 考生基本信息
        File userInfoFile = new File(filePath + File.separator + KsPkgFileEnum.KSJBXX.getCode() + SuffixTxt);

        String tzzPath = filePath + File.separator + KsPkgFileEnum.KSTZZ.getCode();

        List<KsBpxx> bpxxList = doGetBpxx(param.ksjhbh);
        handleFileWriteGxStu(param, userExamInfoFile, userInfoFile, bpxxList, tzzPath, zjhmExists);

        return ksKssjPkgFile;
    }

//	/**
//	 * 打包考生照片
//	 * @param param
//	 * @param packKds
//	 * @param filePath
//	 * @return
//	 * @throws IOException
//	 */
//	protected KsKsxxExportFile doPkgGxStuZp(PackFileParam param,Map<String, List<KdKdxx>> packKds,String  filePath ) throws IOException {
//		KsKsxxExportFile ksKsxxExportFile = new KsKsxxExportFile();
//
//		Set<String> zjhmExists = new HashSet<>();
//
//		File ksZpInfoFile = new File(filePath + File.separator + KsPkgFileEnum.KSZP.getCode()+ SuffixTxt);
//
//		String kszpPath = filePath + File.separator + KsPkgFileEnum.KSZP.getCode();
//
//		// 打包数据数目记录
//		int sqliteBmxxCount = 0;
//		for (KdKdxx kdxx : packKds.get(param.bzhkdid)) {
//			List<KsBpxx> bpxxList = getBpxxByKd(kdxx);
//			List<KsBmxx> bmxxList = getBmxxByBp(bpxxList);
//			sqliteBmxxCount += PkgDOUtil.writeSqliteKsZp(ksZpInfoFile, bmxxList, zjhmExists,kszpPath,exceptionParam->{
//				//考生异常处理
//				KsBmxx view = (KsBmxx)exceptionParam.getObj();
//				String desc = "考生姓名["+view.getXm()+"],考生号["+view.getKsh()+"],报名号["+view.getKsbmh()+"],证件号码["+view.getSfzjhm()+"],照片地址["+view.getZp()+"] 异常为["+exceptionParam.getException()+"]";
//				desc = StringUtils.substring(desc, 0, 1995);
//				KsKsxxExportException exception = new KsKsxxExportException();
//				String id = IdGenerateUtil.generateId();
//				exception.setBzhkdid(kdxx.getBzhkdid());
//				exception.setExportTaskId(param.taskId);
//				exception.setKdbh(kdxx.getKdbh());
//				exception.setKdmc(kdxx.getKdmc());
//				exception.setKsjhbh(param.ksjhbh);
//				exception.setDesc(desc);
//				exception.setMode(ExportExceptionModeEnum.STU.getCode());
//				exception.setType(ExportExceptionTypeEnum.STU_TZZ.getCode());
//				exception.setId(id);
//				ksxxExportExceptionService.insert(exception);
//			});
//		}
//
//
//		// 详情记录
//		List<String> desc = new ArrayList<>();
//		desc.add(StringFormatUtils.pkgDescFormat("考生基本信息", sqliteBmxxCount));
//		File kszpPathFile = new File(kszpPath);
//		if (kszpPathFile.exists() && kszpPathFile.list().length > 0) {
//			desc.add(StringFormatUtils.pkgDescFormat("照片", kszpPathFile.list().length));
//		}else {
//			throw new FileNotFoundException("未找到考生照片文件");
//		}
//
//		ksKsxxExportFile.setPkgDesc(String.join(",", desc));
//		return ksKsxxExportFile;
//	}


    protected Map<String, Integer> handleFileWriteGxStu(PackFileParam param, File userExamInfoFile, File userInfoFile, List<KsBpxx> bpxxList, String tzzPath, Set<String> zjhmExists) throws FileNotFoundException {
        List<KsBmxx> bmxxList = getBmxxByBp(bpxxList);
        Map<String, KsBmxx> bmxxMap = getBmxxMap(bmxxList);
        // 写入考生编排信息
        int sqliteBpxxCount = PkgDOUtil.writeSqliteBpxx(userExamInfoFile, bpxxList, bmxxMap);
        // 写入考生报名信息
        int sqliteBmxxCount = PkgDOUtil.writeSqliteBmxx(userInfoFile, bmxxList, zjhmExists, tzzPath, exceptionParam -> {
            //考生异常处理
            KsBmxxTzzView view = (KsBmxxTzzView) exceptionParam.getObj();
            String desc = "考生姓名[" + view.getXm() + "],准考证号[" + view.getZkzh() + "],考生号[" + view.getKsh() + "],证件号码[" + view.getSfzjhm() + "],特征值地址[" + view.getTzzid() + "] 异常为[" + exceptionParam.getException() + "]";
            desc = StringUtils.substring(desc, 0, 1995);
            KsKssjPkgException exception = new KsKssjPkgException();
            String id = IdGenerateUtil.generateId();
            exception.setPkgTaskId(param.taskId);
            exception.setKsjhbh(param.ksjhbh);
            exception.setTDesc(desc);
            exception.setTMode(PkgExceptionModeEnum.STU.getCode());
            exception.setTType(PkgExceptionTypeEnum.STU_TZZ.getCode());
            exception.setId(id);

            ksKssjPkgExceptionService.insertSingle(exception);
        });

        Map<String, Integer> resultMap = new LinkedHashMap<>();
        File tzzFile = new File(tzzPath);
        resultMap.put("考生基本信息", sqliteBmxxCount);
        resultMap.put("考生编排信息", sqliteBpxxCount);
        if (tzzFile.exists() && tzzFile.list().length > 0) {
            resultMap.put("生物特征文件", tzzFile.list().length);
        } else {
            throw new FileNotFoundException("未找到生物特征文件");
        }

        return resultMap;
    }

    protected KsKssjPkgFile doPkgGxJkry(PackFileParam param, String filePath) throws IOException {
        KsKssjPkgFile ksKssjPkgFile = new KsKssjPkgFile();

        Set<String> zjhmExists = new HashSet<>();

        int sqliteJkryJbxxCount = 0;
        // 监考人员基本信息
        File userInfoFile = new File(filePath + File.separator + KsPkgFileEnum.JKRYJBXX.getCode() + SuffixTxt);
        String tzzPath = filePath + File.separator + KsPkgFileEnum.JKYTZZ.getCode();

        List<KsJkryJbxxTzzView> jbxxList = getJkryJbxxTzzByKd(param.ksjhbh);
        // 写入监考人员基本信息
        sqliteJkryJbxxCount += PkgDOUtil.writeSqliteJkryJbxx(userInfoFile, jbxxList, zjhmExists, tzzPath, SqliteDbNameEnum.KsJkryBasic.getDbName(), exceptionParam -> {
            //监考人员异常处理
            KsJkryJbxxTzzView view = (KsJkryJbxxTzzView) exceptionParam.getObj();
            String desc = "监考人员[" + view.getXm() + "],个人标识码[" + view.getGrbsm() + "],证件号码[" + view.getZjh() + "],特征值地址[" + view.getTzz() + "] 异常为[" + exceptionParam.getException() + "]";
            desc = StringUtils.substring(desc, 0, 1995);
            KsKssjPkgException exception = new KsKssjPkgException();
            exception.setPkgTaskId(param.taskId);
            exception.setKsjhbh(param.ksjhbh);
            exception.setTDesc(desc);
            exception.setTMode(PkgExceptionModeEnum.STU.getCode());
            exception.setTType(PkgExceptionTypeEnum.STU_TZZ.getCode());
            exception.setId(IdGenerateUtil.generateId());
            ksKssjPkgExceptionService.insertSingle(exception);
        });

        return ksKssjPkgFile;
    }

    protected KsKssjPkgFile doPkgGxJkryZp(PackFileParam param, String filePath) throws IOException {
        KsKssjPkgFile ksKsxxExportFile = new KsKssjPkgFile();

        Set<String> zjhmExists = new HashSet<>();

        int sqliteJkryJbxxCount = 0;
        // 监考人员基本信息
        File userInfoFile = new File(filePath + File.separator + KsPkgFileEnum.JKRYZP.getCode() + SuffixTxt);
        String zpPath = filePath + File.separator + KsPkgFileEnum.JKRYZP.getCode();
        List<KsJkryJbxx> jbxxList = getJkryJbxxByKd(param.ksjhbh);
        // 写入监考人员基本信息
        sqliteJkryJbxxCount += PkgDOUtil.writeSqliteJkryZp(userInfoFile, jbxxList, zjhmExists, zpPath, SqliteDbNameEnum.Jkryzp.getDbName(), exceptionParam -> {
            //监考人员异常处理
            KsJkryJbxx view = (KsJkryJbxx) exceptionParam.getObj();
            String desc = "监考人员[" + view.getXm() + "],个人标识码[" + view.getGrbsm() + "],证件号码[" + view.getZjh() + "],照片地址[" + view.getZp() + "] 异常为[" + exceptionParam.getException() + "]";
            desc = StringUtils.substring(desc, 0, 1995);
            KsKssjPkgException exception = new KsKssjPkgException();
            exception.setPkgTaskId(param.taskId);
            exception.setKsjhbh(param.ksjhbh);
            exception.setTDesc(desc);
            exception.setTMode(PkgExceptionModeEnum.JKRY.getCode());
            exception.setTType(PkgExceptionTypeEnum.JKRY_ZP.getCode());
            exception.setId(IdGenerateUtil.generateId());
            ksKssjPkgExceptionService.insertSingle(exception);
        });


        return ksKsxxExportFile;
    }

    protected KsKssjPkgFile doPkgGxJkryBp(PackFileParam param, String filePath) throws IOException {
        KsKssjPkgFile ksKsxxExportFile = new KsKssjPkgFile();

        List<Map<Object, Object>> rygwzzmbList = mbCacheOperateService.queryMbsjList(gxRygwzzmb);
        Map<String, String> rygwzzmbMap = Maps.newHashMap();
        rygwzzmbList.forEach(entity -> {
            rygwzzmbMap.put(entity.get("DM").toString(), entity.get("MC").toString());
        });
        // 监考人员编排
        File userExamInfoFile = new File(filePath + File.separator + KsPkgFileEnum.JKRYBPXX.getCode() + SuffixTxt);

        int sqliteJkryBpxxCount = 0;
        List<KsJkryBpxx> bpxxList = getJkryBpxxByKd(param.ksjhbh);
        List<KsJkryJbxx> jbxxList = getJkryJbxxByBp(bpxxList);
        Map<String, KsJkryJbxx> jbxxMap = getJkryJbxxMap(jbxxList);
        // 写入监考人员编排信息
        sqliteJkryBpxxCount += PkgDOUtil.writeSqliteJkryBpxx(userExamInfoFile, bpxxList, jbxxMap, rygwzzmbMap, SqliteDbNameEnum.KsJkryBp.getDbName());

        return ksKsxxExportFile;
    }

    /**
     * 压缩指定目录
     *
     * @param zipFilePath: 输出路径
     * @param zipFileName: 输出的 zip 文件名
     * @param target       : 要压缩的目录路径
     * @return
     * @throws IOException
     * @throws FileNotFoundException
     */
    protected String zipFile1(String zipFilePath, String zipFileName, String target) throws FileNotFoundException, IOException {
        String result = null;
        // 创建目录
        File zipFilePathFile = new File(zipFilePath);
        if (!zipFilePathFile.exists()) {
            zipFilePathFile.mkdirs();
        }

        File targetDir = new File(target);
        ZipOutputStream zipOutputStream = new ZipOutputStream(new FileOutputStream(zipFilePath + File.separator + zipFileName));
        File[] files = targetDir.listFiles();

        for (File file : files) {
            zipOutputStream.putNextEntry(new ZipEntry(file.getName()));
            try (InputStream inputStream = new BufferedInputStream(new FileInputStream(file))) {
                byte[] buffer = new byte[1000];
                int n;
                while ((n = inputStream.read(buffer)) != -1) { // 读取到缓冲区
                    zipOutputStream.write(buffer, 0, n);
                }
            }
            zipOutputStream.closeEntry();
        }
        result = zipFilePath + File.separator + zipFileName;
        return result;
    }


    /**
     * 根据照片id调用兴昌文件系统进行文件打包
     *
     * @param ids: 照片id
     * @return 打包文件的id，路径等
     */
    private UploadItemVO zipPic(Set<String> ids) {
        try {
            UploadVO uploadVO = XcDfsClient.batchPkg(ids.toArray(new String[0]));
            return uploadVO.getList().get(0);
        } catch (DfsSdkBusiException e) {
            logger.error("照片打包失败," + e.getMsg(), e);
            return null;
        }
    }


    /**
     * 根据照片id调用兴昌文件系统进行文件打包,形式为压缩包内嵌套压缩包
     *
     * @param downloadName
     * @param fileInfoVOS
     * @return
     */
    private UploadItemVO zipPic(String downloadName, List<SubFileInfoVO> fileInfoVOS) {
        try {
            UploadVO uploadVO = XcDfsClient.batchSubPkg(downloadName, fileInfoVOS);
            return uploadVO.getList().get(0);
        } catch (DfsSdkBusiException e) {
//			logger.error("照片打包失败,{}",e.getMsg());
            logger.error("照片打包失败," + e.getMsg(), e);
            return null;
        }
    }


//	/**
//	 * 获得等待打包的考点,将部分考点合并
//	 * @param ksjhbh: 考试计划编号
//	 * @param bzhkdids: 标准化考点，以","分隔表示分开打包，以"|"分隔表示合并打包
//	 * @return <标准化考点编号, 考点信息(需要合并时有多个)>
//	 */
//	private Map<String, List<KdKdxx>> getPackKds(String ksjhbh,String bzhkdids){
//		Map<String, List<KdKdxx>> map = new HashMap<>();
//		List<KdKdxx> kdKdxxList = kdKdxxService.selectDistentKdxx(ksjhbh);
//		if(StringUtil.isNotBlank(bzhkdids)){ // 传入标准化考点参数,选择需要打包和合并的考点
//			Set<String> bzhkdidSet = new HashSet<>(Arrays.asList(bzhkdids.split("[,|]")));
//			String[] bzhkdidKeys = bzhkdids.split(",");
//			// 选出等待打包的考点
//			for (KdKdxx kdKdxx : kdKdxxList) {
//				if(bzhkdidSet.contains(kdKdxx.getBzhkdid())){
//					map.put(kdKdxx.getBzhkdid(), new ArrayList<KdKdxx>(){{
//						add(kdKdxx);
//					}});
//				}
//			}
//			// 合并考点
//			for (String bzhkdidKey : bzhkdidKeys) {
//				if(bzhkdidKey.contains("|")){
//					String[] bzhkdid = bzhkdidKey.split("\\|");
//					String key = bzhkdid[0];
//					for(int i=1;i<bzhkdid.length;i++){
//						map.get(key).add(map.get(bzhkdid[i]).get(0));
//						map.remove(bzhkdid[i]);
//					}
//				}
//			}
//		}else{  // 不传入标准化考点参数,选择所有考点
//			for (KdKdxx kdKdxx : kdKdxxList) {
//				map.put(kdKdxx.getBzhkdid(), new ArrayList<KdKdxx>(){{
//					add(kdKdxx);
//				}});
//			}
//		}
//
//		return map;
//
//	}

    @Override
    public List<KsBpxx> doGetBpxx(String ksjhbh) {
        Example example = new Example(KsBpxx.class);
        Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhbh", ksjhbh).andEqualTo("scztw", ScztEnum.NOTDEL.getCode());

        //分页处理
        Page page = PageHelper.startPage(1, 1000);
        List<KsBpxx> mbpxxList = ksBpxxService.selectListByExample(example);
        List<KsBpxx> bpxxList = new ArrayList<>((int) page.getTotal());
        for (int i = 0; i < page.getPages(); i++) {
            if (!CollectionUtils.isEmpty(mbpxxList)) {
                bpxxList.addAll(mbpxxList);
            }
            PageHelper.startPage(i + 2, 1000);
            mbpxxList = ksBpxxService.selectListByExample(example);
        }

        if (bpxxList.isEmpty()) {
            throw new IdentityVerifyException("不存在考生编排信息");
        }

        return bpxxList;

    }


    /**
     * 根据编排信息（考生号）获得考生报名信息
     *
     * @param bpxxList
     * @return
     */
    public List<KsBmxx> getBmxxByBp(List<KsBpxx> bpxxList) {

        String ksjhbh = bpxxList.get(0).getKsjhbh();
        Set<String> kshSet = Sets.newHashSet();
        for (KsBpxx bpxx : bpxxList) {
            kshSet.add(bpxx.getKsh());
        }

        if (kshSet.isEmpty()) {
            logger.warn("不存在考生信息");
            return Collections.emptyList();
        }

        // 考生号超过1000会导致oracle查询报错, 修改为每1000考生号查询一次
        List<KsBmxx> bmxxList = new ArrayList<>();
        Set<String> _tempKshs = new HashSet<>();
        for (String ksh : kshSet) {
            _tempKshs.add(ksh);
            if (_tempKshs.size() == 1000) {
                Example bmExample = new Example(KsBmxx.class);
                Criteria bmCriteria = bmExample.createCriteria();
                bmCriteria.andEqualTo("ksjhid", ksjhbh)
                        .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
                bmCriteria.andIn("ksh", _tempKshs);
                List<KsBmxx> mbmxxList = ksBmxxService.selectListByExample(bmExample);
                if (!CollectionUtils.isEmpty(mbmxxList)) {
                    bmxxList.addAll(mbmxxList);
                }
                _tempKshs.clear();
            }
        }
        // 剩余数据
        if (_tempKshs.size() > 0) {
            Example bmExample = new Example(KsBmxx.class);
            Criteria bmCriteria = bmExample.createCriteria();
            bmCriteria.andEqualTo("ksjhid", ksjhbh)
                    .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
            bmCriteria.andIn("ksh", _tempKshs);
            List<KsBmxx> mbmxxList = ksBmxxService.selectListByExample(bmExample);
            if (!CollectionUtils.isEmpty(mbmxxList)) {
                bmxxList.addAll(mbmxxList);
            }
        }

        if (bmxxList.isEmpty()) {
            logger.warn("不存在考生报名信息");
            return Collections.emptyList();
        }

        return bmxxList;
    }


    public List<KsBmxxTzzView> getBmxxTzzByBp(List<KsBpxx> bpxxList) {

        String ksjhbh = bpxxList.get(0).getKsjhbh();
        String bzhkdid = bpxxList.get(0).getBzhkdbid();

        Map<String, String> kshMap = Maps.newHashMap();

//		Set<String> kshSet = Sets.newHashSet();
        for (KsBpxx bpxx : bpxxList) {
//			kshSet.add(bpxx.getKsh());
            kshMap.put(bpxx.getKsh(), bpxx.getZkzh());
        }
        Set<String> kshSet = kshMap.keySet();
        if (kshSet.isEmpty()) {
            logger.warn("标准化考点" + bzhkdid + "不存在考生信息");
            return Collections.emptyList();
        }

        KsBmxxTzzParam param = new KsBmxxTzzParam();
        param.setKsjhid(ksjhbh);
        param.setScztw(ScztEnum.NOTDEL.getCode());

        // 考生号超过1000会导致oracle查询报错, 修改为每1000考生号查询一次
        List<KsBmxxTzzView> bmxxList = new ArrayList<>();
        Set<String> _tempKshs = new HashSet<>();
        for (String ksh : kshSet) {
            _tempKshs.add(ksh);
            if (_tempKshs.size() == 1000) {
                param.setKshs(_tempKshs);
                List<KsBmxxTzzView> mbmxxList = ksBmxxService.selectKsBmxxTzzViewByParam(param);
                if (!CollectionUtils.isEmpty(mbmxxList)) {
                    bmxxList.addAll(mbmxxList);
                }
                _tempKshs.clear();
            }
        }
        // 剩余数据
        if (_tempKshs.size() > 0) {
            param.setKshs(_tempKshs);
            List<KsBmxxTzzView> mbmxxList = ksBmxxService.selectKsBmxxTzzViewByParam(param);
            if (!CollectionUtils.isEmpty(mbmxxList)) {
                bmxxList.addAll(mbmxxList);
            }
        }

        if (bmxxList.isEmpty()) {
            throw new IdentityVerifyException("标准化考点" + bzhkdid + "不存在考生报名信息");
        }

        bmxxList.forEach(bmxx -> {
            String zkzh = kshMap.get(bmxx.getKsh());
            bmxx.setZkzh(zkzh);
        });
        return bmxxList;
    }


    /**
     * 获得考生号和考生报名信息的对应 map
     *
     * @param bmxxList
     * @return
     */
    private Map<String, KsBmxx> getBmxxMap(List<KsBmxx> bmxxList) {
        Map<String, KsBmxx> bmxxMap = Maps.newTreeMap();
        for (KsBmxx bmxx : bmxxList) {
            bmxxMap.put(bmxx.getKsh(), bmxx);
        }
        return bmxxMap;
    }

    /**
     * 获得考生号和考生报名信息的对应 map
     *
     * @param bmxxList
     * @return
     */
    private Map<String, KsBmxxTzzView> getBmxxTzzMap(List<KsBmxxTzzView> bmxxList) {
        Map<String, KsBmxxTzzView> bmxxMap = Maps.newTreeMap();
        for (KsBmxxTzzView bmxx : bmxxList) {
            bmxxMap.put(bmxx.getKsh(), bmxx);
        }
        return bmxxMap;
    }

    /**
     * 获得考场和考场编排信息对应 Map
     *
     * @param bpxxList
     * @return map<场次码_考场号, 编排信息列表>
     */
    private Map<String, List<KsBpxx>> getKcMap(List<KsBpxx> bpxxList) {
        Map<String, List<KsBpxx>> kcMap = Maps.newTreeMap();
        for (KsBpxx bpxx : bpxxList) {
            String kcbh = bpxx.getCcm() + "_" + bpxx.getKcbh();
            if (kcMap.containsKey(kcbh)) {
                kcMap.get(kcbh).add(bpxx);
            } else {
                List<KsBpxx> suBpxxs = Lists.newArrayList();
                suBpxxs.add(bpxx);
                kcMap.put(kcbh, suBpxxs);
            }
        }
        return kcMap;
    }


    /**
     * 根据标准化考点查询监考人员编排信息
     *
     * @return 编排信息列表，查询失败返回空集合
     */
    private List<KsJkryBpxx> getJkryBpxxByKd(String ksjhbh) {

        List<KsJkryBpxx> bpxxList = ksJkryBpxxService.selectBpwithKc(ksjhbh);

        if (bpxxList.isEmpty()) {
            logger.warn("不存在编排信息");
            return Collections.emptyList();
        }

        return bpxxList;
    }


    /**
     * 根据监考人员编排信息获得监考人员基本信息
     *
     * @param bpxxList
     * @return
     */
    private List<KsJkryJbxx> getJkryJbxxByBp(List<KsJkryBpxx> bpxxList) {

        String ksjhbh = bpxxList.get(0).getKsjhdm();
        String bzhkdid = bpxxList.get(0).getBzhkdid();
        Set<String> grbsmSet = Sets.newHashSet();
        for (KsJkryBpxx bpxx : bpxxList) {
            grbsmSet.add(bpxx.getGrbsm());
        }

        if (grbsmSet.isEmpty()) {
            logger.warn("标准化考点[" + bzhkdid + "]不存在监考人员信息");
            return Collections.emptyList();
        }

        Example exampleJbxx = new Example(KsJkryJbxx.class);
        Criteria bmCriteria = exampleJbxx.createCriteria();
        bmCriteria.andEqualTo("ksjhdm", ksjhbh);
        if (!grbsmSet.isEmpty()) {
            bmCriteria.andIn("grbsm", grbsmSet);
        }

        List<KsJkryJbxx> jbxxList = ksJkryJbxxService.selectListByExample(exampleJbxx);
        return jbxxList;
    }

    /**
     * 根据监考人员编排信息获得监考人员基本信息
     *
     * @param bpxxList
     * @return
     */
    private List<KsJkryJbxxTzzView> getJkryJbxxTzzByBp(List<KsJkryBpxx> bpxxList) {

        String ksjhbh = bpxxList.get(0).getKsjhdm();
        String bzhkdid = bpxxList.get(0).getBzhkdid();
        Set<String> grbsmSet = Sets.newHashSet();
        for (KsJkryBpxx bpxx : bpxxList) {
            grbsmSet.add(bpxx.getGrbsm());
        }

        if (grbsmSet.isEmpty()) {
            logger.warn("标准化考点[" + bzhkdid + "]不存在监考人员信息");
            return Collections.emptyList();
        }

//		Example exampleJbxx = new Example(KsJkryJbxx.class);
//		Criteria bmCriteria = exampleJbxx.createCriteria();
//		bmCriteria.andEqualTo("ksjhdm", ksjhbh);
//		if(!grbsmSet.isEmpty()){
//			bmCriteria.andIn("grbsm", grbsmSet);
//		}
//		List<KsJkryJbxx> jbxxList = ksJkryJbxxService.selectListByExample(exampleJbxx);
        KsJkryJbxxTzzParam param = new KsJkryJbxxTzzParam();
        param.setKsjhid(ksjhbh);
        param.setGrbsms(grbsmSet);
        List<KsJkryJbxxTzzView> jbxxList = ksJkryJbxxService.selectTzzViewByParam(param);
        return jbxxList;
    }

    /**
     * 根据考点获得监考人员基本信息
     *
     * @return
     */
    private List<KsJkryJbxxTzzView> getJkryJbxxTzzByKd(String ksjhbh) {
        KsJkryJbxxTzzParam param = new KsJkryJbxxTzzParam();
        param.setKsjhid(ksjhbh);
        List<KsJkryJbxxTzzView> jbxxList = ksJkryJbxxService.selectTzzViewByParam(param);
        return jbxxList;
    }

    /**
     * 根据考点获得监考人员基本信息
     *
     * @return
     */
    private List<KsJkryJbxx> getJkryJbxxByKd(String ksjhbh) {

        Example emJkry = new Example(KsJkryJbxx.class);
        emJkry.createCriteria()
                .andEqualTo("ksjhdm", ksjhbh);
        List<KsJkryJbxx> jbxxList = ksJkryJbxxService.selectListByExample(emJkry);
        return jbxxList;
    }

    /**
     * 获得考生号和考生报名信息的对应 map
     *
     * @param bmxxList
     * @return
     */
    private Map<String, KsJkryJbxx> getJkryJbxxMap(List<KsJkryJbxx> bmxxList) {
        Map<String, KsJkryJbxx> bmxxMap = Maps.newTreeMap();
        for (KsJkryJbxx bmxx : bmxxList) {
            bmxxMap.put(bmxx.getGrbsm(), bmxx);
        }
        return bmxxMap;
    }

    /**
     * 上传文件到dfs
     *
     * @param filePath
     * @return
     */
    private DfsResultDO uploadToDfs(String filePath) {

        DfsResultDO dfs = new DfsResultDO();

        int flag = 3;
        String eMessage = "";
        do {
            eMessage = "";
            try {
                UploadVO uploadVO = XcDfsClient.uploadStream(filePath, 1, "private");
                UploadItemVO uploadItemVO = uploadVO.getList().get(0);
                dfs.dfsId = uploadItemVO.getId();
                dfs.dfsPath = uploadItemVO.getFilePath();
                Attachment att = new Attachment();
                att.setBucketName("chic-website");
                att.setId(dfs.dfsId);
                att.setPath(dfs.dfsId);
                att.setOriginalName(uploadItemVO.getOriFileName());
                att.setType(".zip");
                // 获取当前时间
                Calendar calendar = Calendar.getInstance();
                // 在当前时间基础上增加一年
                calendar.add(Calendar.YEAR, 1);
                att.setExpireTime(calendar.getTime());
                attachmentService.save(att);
            } catch (Exception e) {
                eMessage = StringUtils.isBlank(e.getMessage()) ? e.toString() : e.getMessage();
            }
        } while (--flag > 0 && StringUtils.isBlank(dfs.dfsId));

        if (StringUtils.isNotBlank(eMessage)) {
            throw new IdentityVerifyException(filePath + "上传到dfs失败[" + eMessage + "]");
        }

        return dfs;
    }


    /**
     * 非标监考人员信息打包
     *
     * @param ksjhbh
     * @param rootFilePath
     */
    private void zipJkryFb(String ksjhbh, KsKssjPkgTask task, String rootFilePath) throws IOException {
        KsKssjPkgFile packResult = new KsKssjPkgFile();

        try {
            String filePath = rootFilePath + File.separator + "UNZIP" + File.separator + ksjhbh + File.separator + PackEnum.Pack_GxHisomeFbJkry.getBz();
            packResult = writeFileFb(ksjhbh, task, filePath);

            // 打包
            String zipFilePath = rootFilePath + File.separator + "ZIP";
            String zipFileName = ksjhbh + "_" + PackEnum.Pack_GxHisomeFbJkry.getBz() + ".zip";
            String result = "";
            if (secretEnable) {
                result = CompressUtil.zip(filePath, zipFilePath + File.separator + zipFileName, secretKey);
            } else {
                result = CompressUtil.zip(filePath, zipFilePath + File.separator + zipFileName, "");
            }
            packResult.setFilePath(result);
            packResult.setFileName(zipFileName);

            //上传到dfs文件服务器
            DfsResultDO dfsResult = uploadToDfs(result);
            packResult.setDfsFileObjUri(dfsResult.dfsId);
            packResult.setDfsFilePathUri(dfsResult.dfsPath);

            //设置md5值
            packResult.setFileMd(ksKssjPkgFileService.getFileHash(result));

        } catch (IOException e) {

            throw e;
        } finally {
            // 打包基础信息
            packResult.setKsjhbh(ksjhbh);
            packResult.setPkgType(PackEnum.Pack_GxHisomeFbJkry.getCode());

            ksKssjPkgFileService.insertSingle(packResult);
        }
    }

    private KsKssjPkgFile writeFileFb(String ksjhbh, KsKssjPkgTask task, String filePath) throws FileNotFoundException {

        // 监考人员基本信息
        KsKssjPkgFile ksKsxxExportFile = new KsKssjPkgFile();

        Set<String> zjhmExists = new HashSet<>();

        File userInfoFile = new File(filePath + File.separator + KsPkgFileEnum.JKRYJBXX.getCode() + SuffixTxt);
        String tzzPath = filePath + File.separator + KsPkgFileEnum.JKYTZZ.getCode();


        List<Map<Object, Object>> rygwzzmbList = mbCacheOperateService.queryMbsjList(gxRygwzzmb);
        Map<String, String> rygwzzmbMap = Maps.newHashMap();
        rygwzzmbList.forEach(entity -> {
            rygwzzmbMap.put(entity.get("DM").toString(), entity.get("MC").toString());
        });

        List<KsJkryBpxx> bpxxList;
        List<KsJkryJbxxTzzView> jbxxList;
        int sqliteJkryBpxxCount = 0;
        int sqliteJkryJbxxCount = 0;
        // 监考人员基本信息
        Page<Object> page = PageHelper.startPage(1, 500);
        KsJkryJbxxTzzParam param = new KsJkryJbxxTzzParam();
        param.setKsjhid(ksjhbh);
        jbxxList = ksJkryJbxxService.selectTzzViewByParam(param);
        for (int i = 0; i < page.getPages(); i++) {
            // 写入监考人员基本信息
            sqliteJkryJbxxCount += PkgDOUtil.writeSqliteJkryJbxx(userInfoFile, jbxxList, zjhmExists, tzzPath, SqliteDbNameEnum.KsJkryFb.getDbName(), exceptionParam -> {
                //TODO 监考人员异常处理
            });
            // 进度
            double progress = (i + 1) * 25.0 / page.getPages();
            task.setTProgress(String.format("%.2f", progress));
            ksKssjPkgTaskService.updateByPrimaryKeySelective(task);

            PageHelper.startPage(i + 2, 500, false, false, true);
            jbxxList = ksJkryJbxxService.selectTzzViewByParam(param);
        }
        // 监考人员编排信息
        page = PageHelper.startPage(1, 500);
        bpxxList = ksJkryBpxxService.selectBpwithKc(ksjhbh);
        for (int i = 0; i < page.getPages(); i++) {
            // 写入监考人员编排
            File userExamInfoFile = new File(filePath + File.separator + KsPkgFileEnum.JKRYBPXX.getCode() + SuffixTxt);
            sqliteJkryBpxxCount += PkgDOUtil.writeSqliteJkryBpxx(userExamInfoFile, bpxxList, null, rygwzzmbMap, SqliteDbNameEnum.KsJkryFb.getDbName());

            // 进度
            double progress = 25.0 + (i + 1) * 25.0 / page.getPages();
            task.setTProgress(String.format("%.2f", progress));
            ksKssjPkgTaskService.updateByPrimaryKeySelective(task);

            PageHelper.startPage(i + 2, 500, false, false, true);
            bpxxList = ksJkryBpxxService.selectBpwithKc(ksjhbh);
        }

        StringBuilder desc = new StringBuilder()
                .append("非标监考人员基本信息数")
                .append(sqliteJkryJbxxCount)
                .append(",")
                .append("非标监考人员编排信息数")
                .append(sqliteJkryBpxxCount);
        File tzzFile = new File(tzzPath);
        if (tzzFile.exists() && tzzFile.list().length > 0) {
            desc.append(",生物特征文件数" + tzzFile.list().length);
        } else {
            throw new FileNotFoundException("未找到生物特征文件");
        }

        return ksKsxxExportFile;
    }

    private class PackFileParam {
        String rootFilePath;
        String ksjhbh;
        String bzhkdid;
        String bzhkdmc;
        String kdmc;
        String targetFilePath;
        String taskId;
        Integer mode = 1;
        KsKcxx kcxx;
        /**
         * 导出文件记录id
         */
        String exportFileId;
        // 是否使用缓存
        Boolean useCache;
    }

    private class DfsResultDO {
        String dfsId;
        String dfsPath;
    }

    @Override
    public DistributePackVO distributePack(DistributeParamDO paramDO) throws InterruptedException {
        doDistributePack(distributeRedisRateLimiter, paramDO);

        KsKssjDistributeTask task = new KsKssjDistributeTask();
        task.setComplete(DistributeTaskCompleteStatusEnum.Done.getCode());
        task.setCompleteTime(new Date());
        task.setTProgress("100.00");
        Example taskExample = new Example(KsKssjDistributeTask.class);
        taskExample.createCriteria().andEqualTo("id", paramDO.getTaskId());
        ksDistributeTaskService.updateByExampleSelective(task, taskExample);
        return null;
    }

    /**
     * 执行包分发
     *
     * @param limiter
     * @param paramDO
     */
    protected DistributePackVO doDistributePack(RateLimiter limiter, DistributeParamDO paramDO) throws InterruptedException {
        paramDO.setCsbhList(paramDO.getCsbhList().stream().sorted().collect(Collectors.toList()));

        // 设置阶段状态为开始
        ksProcessPeriodService.updatePeriod(EventPeriodKey.SJXF, paramDO.getKsjhbh(), PeriodStatusEnum.DOING, "");

        List<String> successCsList = new ArrayList<>();
        List<String> failCsList = new ArrayList<>(paramDO.getCsbhList());

        Map<String, KsKcxx> kcMap = new HashMap<>();
        if (StringUtils.equals(paramDO.getTaskType(), PkgTaskTypeEnum.KC.getCode())) {
            Example emBzhkc = new Example(KsKcxx.class);
            emBzhkc.createCriteria().andEqualTo("ksjhbh", paramDO.getKsjhbh()).andIn("bzhkcid", paramDO.getCsbhList());
            List<KsKcxx> csBzhkcs = ksKcxxService.selectListByExample(emBzhkc);
            if (csBzhkcs.size() > 0) {
                kcMap = csBzhkcs.stream().collect(Collectors.toMap(KsKcxx::getBzhkcid, Function.identity(), (oldV, newV) -> newV));
            }
        }

        Example emKdxx = new Example(KsKdxx.class);
        emKdxx.createCriteria()
                .andEqualTo("ksjhbh", paramDO.getKsjhbh())
                .andEqualTo("scztw", ScztEnum.NOTDEL.getCode());
        List<KsKdxx> ksKdxxes = ksKdxxService.selectListByExample(emKdxx);
        KsKdxx ksKdxx = null;
        if (!CollectionUtils.isEmpty(ksKdxxes)) {
            ksKdxx = ksKdxxes.get(0);
        }

        // 重试计数
        // 为了防止一直重试导致系统出错, 将一直重试设置为固定的最大值
        int retryCount = paramDO.getRetyrCount();

        String distributeThreadFlagRedisKey = distributeThreadFlagRedisPrefix + ":" + paramDO.getTaskId();
        redisUtil.set(distributeThreadFlagRedisKey, 1, distributeThreadFlagRedisKeyExpireTime);
        for (int i = 1; retryCount-- > 0; i++) {
            logger.debug("重试计数: 第{}次", i);
            // 判断任务是否手动结束, 若没有结束转为进行中
            redisUtil.lock(distributeThreadFlagRedisKey + "_lock", 5000, () -> {
                if (redisUtil.exists(distributeThreadFlagRedisKey)) {
                    redisUtil.set(distributeThreadFlagRedisKey, 1, distributeThreadFlagRedisKeyExpireTime);
                }
                return null;
            });
            try {
                startDistributeTask(limiter, paramDO, kcMap, retryCount, successCsList, failCsList, ksKdxx);
                if (retryCount > 0 && successCsList.size() < paramDO.getCsbhList().size()) {
                    logger.info("taskid = {}, 下发未全部完成", paramDO.getTaskId());
                    redisUtil.lock(distributeThreadFlagRedisKey + "_lock", 5000, () -> {
                        if (redisUtil.exists(distributeThreadFlagRedisKey)) {
                            // 设置标记为等待中
                            redisUtil.set(distributeThreadFlagRedisKey, 2, distributeThreadFlagRedisKeyExpireTime);
                        }
                        return null;
                    });
                    // 判断是否手动停止, 是则直接退出, 以免长时间等待数据更新
                    if (!redisUtil.exists(distributeThreadFlagRedisKey)) {
                        throw new ToppingOffException();
                    }
                    Thread.sleep(paramDO.getRetryInterval() * 1000);
                } else {
                    break;
                }
            } catch (ToppingOffException toppingOffException) {
                throw toppingOffException;
            } catch (Exception e) {
                logger.warn("下发错误," + e.getMessage(), e);
                throw e;
            }
        }
        redisUtil.del(distributeThreadFlagRedisKey);

        DistributePackVO distributePackVO = new DistributePackVO();
        distributePackVO.setFailCsList(failCsList);
        distributePackVO.setSuccessCsList(successCsList);
        return distributePackVO;
    }

    public void startDistributeTask(RateLimiter limiter, DistributeParamDO paramDO, Map<String, KsKcxx> kcMap, int retryCount, List<String> successCsList, List<String> failCsList, KsKdxx ksKdxx) throws InterruptedException {
        String distributeThreadFlagRedisKey = distributeThreadFlagRedisPrefix + ":" + paramDO.getTaskId();

        int successTotal = 0;
        int size = failCsList.size();

        // 最后一次下发, 记录之前已成功的总数
        if (retryCount == 0) {
            successTotal = successCsList.size();
        }

        for (int i = 0; i < size; i++) {
            // 判断是否中断
            if (!redisUtil.exists(distributeThreadFlagRedisKey)) {
                throw new ToppingOffException();
            }

            String csbh = failCsList.get(i);
            KsKssjDistributeTaskLog ksDistributeTaskLog = new KsKssjDistributeTaskLog();
            String rateLimiterKey = paramDO.getKsjhbh() + "_" + csbh;

            String bzhkdid = "";
            List<FileDetailVO> fileDetailVOS = new ArrayList<>();
            Date noticeTime = null;
            try {
                // 获取令牌
                limiter.acquire(rateLimiterKey);
                noticeTime = new Date();
                if (StringUtils.equals(paramDO.getTaskType(), PkgTaskTypeEnum.KC.getCode())) {
                    if (kcMap.get(csbh) != null && kcMap.get(csbh).getBzhkdid() != null) {
                        bzhkdid = kcMap.get(csbh).getBzhkdid();
                    } else if (ksKdxx != null) {
                        bzhkdid = ksKdxx.getBzhkdid();
                    }
                }

                // 设置日志基本数据
                ksDistributeTaskLog = ksDistributeTaskLogService.initLog(bzhkdid, paramDO.getKsjhbh(), paramDO.getTaskId(), noticeTime, csbh, paramDO.getTaskType());

                // 获取文件信息
                fileDetailVOS = ksKssjPkgStatusService.getFileDetail(paramDO.getKsjhbh(), csbh, FileFetchModeEnum.BZ, paramDO.getPkgType());
                if (CollectionUtils.isEmpty(fileDetailVOS)) {
                    throw new IdentityVerifyException("考试计划:" + paramDO.getKsjhbh() + " 考场：" + csbh + " 无对应数据包");
                }

                handleDistributeStatus(paramDO.getKsjhbh(), csbh, paramDO.getTaskType(), noticeTime, DataDistributeStatusEnum.Doing.getCode(), fileDetailVOS);

                ksDistributeTaskLogService.setVersion(ksDistributeTaskLog, fileDetailVOS);

                ArrayList<String> sblxList = new ArrayList<>();
                sblxList.add(JcsblxEnum.KCWG.getMsg());
                sblxList.add(JcsblxEnum.DZBP.getMsg());
                // 获取设备信息
                List<JcsbxxDO> result = sbcsgxMapper.jcsbxxlbcxV2(csbh, null);
                if (!CollectionUtils.isEmpty(result)) {
                    for (JcsbxxDO jcsbxxDO : result) {
                        ksDistributeTaskLog.setTSn(jcsbxxDO.getXlh());
                        ksDistributeTaskLog.setDevType(jcsbxxDO.getSblx());

                        // 设备状态检测
                        if (StringUtils.equals(jcsbxxDO.getZxzt(), "0")) {
//                            String detectDesc = sbjc(jcsbxxDO.getXlh());
                            String detectDesc = jcsbxxDO.getXlh() + "离线";
                            if (StringUtils.isNotBlank(detectDesc)) {
                                String eventDesc = "接入平台设备已离线，检测到" + detectDesc;
                                ksProcessEventService.insertSjxf(paramDO.getKsjhbh(), csbh, PkgTaskTypeEnum.get(paramDO.getTaskType()).getMc(), "", EventTypeSjxfEnum.FAIL_SBGZ, EventHandleEnum.TREATED, eventDesc, eventDesc, false);

                                logger.info("消息发送失败[csbh={}, sbxlh={}, {}]", csbh, jcsbxxDO.getXlh(), detectDesc);
                                handleDistributeJg(paramDO.getTaskId(), csbh, paramDO.getTaskType(), DistributeNoticeEnum.FAIL.getCode());

                                ksDistributeTaskLogService.setResult(ksDistributeTaskLog, DistributeLogResultEnum.SEND_FAIL);

                                // 下发失败状态设置
                                handleDistributeStatus(paramDO.getKsjhbh(), csbh, paramDO.getTaskType(), noticeTime, DataDistributeStatusEnum.OffLine.getCode(), fileDetailVOS);
                                limiter.triggerTokenSupply(rateLimiterKey);
                                continue;
                            }
                        }

                        // 组装下发数据
                        ExamPlanVO data = ksKsjhService.getExamPlanV1(paramDO.getKsjhbh(), "", "").get(0);
                        JSONObject dataJson = (JSONObject) JSONObject.toJSON(data);
                        dataJson.put("fileList", fileDetailVOS);
                        dataJson.put("taskId", paramDO.getTaskId());
                        dataJson.put("secret", ksKssjPkgFileService.getFileSecret());
                        JSONObject jsonObject = AccessJsonUtils.generateAccessJson("NOTIFY_XFKSJH", dataJson);

                        Wrapper<RespBaseModel> remoteRespBaseModelWrapper = unifyAccessService.sendCommand(jcsbxxDO.getXlh(), JSONObject.toJSONString(jsonObject, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty));
                        if (remoteRespBaseModelWrapper.getCode() == 200) {
                            logger.info("消息发送成功[csbh={}, sbxlh={}]", csbh, jcsbxxDO.getXlh());
                            handleDistributeJg(paramDO.getTaskId(), csbh, paramDO.getTaskType(), DistributeNoticeEnum.SUCCESS.getCode());

                            ksDistributeTaskLogService.setResult(ksDistributeTaskLog, DistributeLogResultEnum.SEND_SUCCESS);

                            if (!successCsList.contains(csbh)) {
                                successCsList.add(csbh);
                            }
                        } else {
                            logger.info("消息发送失败[csbh={}, sbxlh={}, {}]", csbh, jcsbxxDO.getXlh(), remoteRespBaseModelWrapper.getMessage());
                            handleDistributeJg(paramDO.getTaskId(), csbh, paramDO.getTaskType(), DistributeNoticeEnum.FAIL.getCode());

                            ksDistributeTaskLogService.setResult(ksDistributeTaskLog, DistributeLogResultEnum.SEND_FAIL);
                            // 下发失败状态设置
                            handleDistributeStatus(paramDO.getKsjhbh(), csbh, paramDO.getTaskType(), noticeTime, DataDistributeStatusEnum.Fail.getCode(), fileDetailVOS);
                            // 插入事件
                            ksProcessEventService.insertSjxf(paramDO.getKsjhbh(), csbh, PkgTaskTypeEnum.get(paramDO.getTaskType()).getMc(), "", EventTypeSjxfEnum.FAIL_SBGZ, EventHandleEnum.TREATED, "消息发送失败", "消息发送失败", true);

                            limiter.triggerTokenSupply(rateLimiterKey);
                        }
                    }
                } else {
                    logger.error("场所编号[{}]设备信息不存在", csbh);
                    handleDistributeJg(paramDO.getTaskId(), csbh, paramDO.getTaskType(), DistributeNoticeEnum.FAIL.getCode());

                    ksDistributeTaskLogService.setResult(ksDistributeTaskLog, DistributeLogResultEnum.DEV_NOT_FOUND);
                    // 下发失败状态设置
                    handleDistributeStatus(paramDO.getKsjhbh(), csbh, paramDO.getTaskType(), noticeTime, DataDistributeStatusEnum.Fail.getCode(), fileDetailVOS);
                    // 插入事件
                    ksProcessEventService.insertSjxf(paramDO.getKsjhbh(), csbh, PkgTaskTypeEnum.get(paramDO.getTaskType()).getMc(), "", EventTypeSjxfEnum.FAIL, EventHandleEnum.TREATED, "场所编号[" + csbh + "]设备信息不存在", "场所编号[" + csbh + "]设备信息不存在", true);

                    limiter.triggerTokenSupply(rateLimiterKey);
                }
            } catch (Exception e) {
                logger.error("下发失败 ", e);
                handleDistributeJg(paramDO.getTaskId(), csbh, paramDO.getTaskType(), DistributeNoticeEnum.FAIL.getCode());

                ksDistributeTaskLogService.setResult(ksDistributeTaskLog, DistributeLogResultEnum.SEND_FAIL);
                // 下发失败状态设置
                handleDistributeStatus(paramDO.getKsjhbh(), csbh, paramDO.getTaskType(), noticeTime, DataDistributeStatusEnum.Fail.getCode(), fileDetailVOS);
                // 插入事件
                ksProcessEventService.insertSjxf(paramDO.getKsjhbh(), csbh, PkgTaskTypeEnum.get(paramDO.getTaskType()).getMc(), "", EventTypeSjxfEnum.FAIL, EventHandleEnum.TREATED, "下发失败", "下发失败" + StringUtils.substring(e.toString(), 0, 1990), true);

                limiter.triggerTokenSupply(rateLimiterKey);
            }

            // 进度
            double progress;
            if (retryCount == 0) {
                progress = (i + 1 + successTotal) * 100.0 / paramDO.getCsbhList().size();
                logger.info("下发进度[{}/{}]", i + 1 + successTotal, paramDO.getCsbhList().size());
            } else {
                progress = successCsList.size() * 100.0 / paramDO.getCsbhList().size();
                logger.info("下发进度[{}/{}]", successCsList.size(), paramDO.getCsbhList().size());
            }
            KsKssjDistributeTask ksDistributeTask = new KsKssjDistributeTask();
            ksDistributeTask.setTProgress(String.format("%.2f", progress));
            if (successCsList.size() == 0) {
                ksDistributeTask.setComplete(DistributeTaskCompleteStatusEnum.Fail.getCode());
            }
            Example taskExample = new Example(KsKssjDistributeTask.class);
            taskExample.createCriteria().andEqualTo("id", paramDO.getTaskId());
            ksDistributeTaskService.updateByExampleSelective(ksDistributeTask, taskExample);

            ksDistributeTaskLogService.insertSingle(ksDistributeTaskLog);
        }
        failCsList.removeAll(successCsList);
    }

    /**
     * 设备检测
     *
     * @param xlh
     * @return 在线返回空, 离线返回具体描述
     */
    private String sbjc(String xlh) {
//        try {
//            SbztjcDTO sbztjcDTO = new SbztjcDTO();
//            sbztjcDTO.setDetectType(3);
//            sbztjcDTO.setKeyList(Arrays.asList(xlh));
//            sbztjcDTO.setKeyType(DevDetectKeyEnum.XLH.getCode());
//            sbztjcDTO.setOpt("1");
//
//            Wrapper<SbztjcVO> sbztjc = sbjcqueryXcmcFeignApi.sbztjc(sbztjcDTO);
//            if (sbztjc != null && sbztjc.getCode() == 200 && sbztjc.getResult() != null && CollectionUtil.isNotEmpty(sbztjc.getResult().getDevDetectQueryList())) {
//                DevDetectQueryItemVO devDetectQueryItemVO = sbztjc.getResult().getDevDetectQueryList().get(0);
//                if (!StringUtils.equals(devDetectQueryItemVO.getResult(), "1")) {
//                    String detectDesc = devDetectQueryItemVO.getDetectDesc();
//                    return detectDesc;
//                }
//            } else {
//                log.warn("设备检测接口sbjcqueryXcmcFeignApi.sbztjc(sbztjcDTO)调用失败, dto={}, vo={}", sbztjcDTO, sbztjc);
//            }
//        } catch (Exception e) {
//            log.warn("设备检测接口sbjcqueryXcmcFeignApi.sbztjc(sbztjcDTO)调用失败", e);
//        }

        return "";
    }

    private void sendOvertimeMsg(String ksjhbh, String csbh, String cslx, Date noticeTime) {
        JSONObject overTimeMsg = new JSONObject();
        overTimeMsg.put("ksjhbh", ksjhbh);
        overTimeMsg.put("csbh", csbh);
        overTimeMsg.put("cslx", cslx);
        overTimeMsg.put("noticeTime", DateUtil.formatDateTime(noticeTime));
        xcMsgService.pushMsgDelayOfRocket(revDistributeOverTimeTopic, overTimeMsg.toJSONString(), DateUtil.addSeconds(noticeTime, distributeOverTime));
    }


    private void handleDistributeJg(String taskId, String csbh, String type, Integer noticeResult) {
        Example emDistributeJg = new Example(KsKssjDistributeTaskCs.class);
        emDistributeJg.createCriteria()
                .andEqualTo("taskId", taskId)
                .andEqualTo("csbh", csbh)
                .andEqualTo("cslx", PkgTaskTypeEnum.get(type).getMc());

        KsKssjDistributeTaskCs ksKssjDistributeTaskCs = new KsKssjDistributeTaskCs();
        ksKssjDistributeTaskCs.setNoticeResult(noticeResult);

        distributeTaskCsService.updateByExampleSelective(ksKssjDistributeTaskCs, emDistributeJg);
    }

    /**
     * 设置文件下发状态
     *
     * @param ksjhbh        考试计划编号
     * @param bzhkcid       标准化考场编号
     * @param type          下发类型
     * @param noticeTime    下发时间
     * @param pkgStatus     下发状态
     * @param fileDetailVOS 文件列表,只有存在于文件列表的才会更新下发状态
     */
    private void handleDistributeStatus(String ksjhbh, String bzhkcid, String type, Date noticeTime, Integer pkgStatus, List<FileDetailVO> fileDetailVOS) {
        Example emDistributeStatus = new Example(KsKssjDistributeStatus.class);
        Criteria criStatus = emDistributeStatus.createCriteria()
                .andEqualTo("ksjhbh", ksjhbh)
                .andEqualTo("bzhkcbh", bzhkcid);

        KsKssjDistributeStatus ksDistributeStatus = new KsKssjDistributeStatus();
        ksDistributeStatus.setTNoticeTime(noticeTime);

        for (FileDetailVO fileDetailVO : fileDetailVOS) {
            switch (PackEnum.getByType(fileDetailVO.getType())) {
                case Pack_GxHisomeStu:
                    ksDistributeStatus.setStuinfPkgStatus(pkgStatus);
                    break;
                case Pack_GxHisomeJkry:
                    ksDistributeStatus.setJkryjbxxPkgStatus(pkgStatus);
                    break;
                case Pack_GxHisomeJkryBp:
                    ksDistributeStatus.setJkrybpxxPkgStatus(pkgStatus);
                    break;
                case Pack_GxHisomeCommon:
                    ksDistributeStatus.setCommonPkgStatus(pkgStatus);
                    break;
                case Pack_GxHisomeKdKsZp:
                    ksDistributeStatus.setStuzpPkgStatus(pkgStatus);
                    break;
                case Pack_GxHisomeKdJkryZp:
                    ksDistributeStatus.setJkryzpPkgStatus(pkgStatus);
                    break;
            }
        }
        ksDistributeStatusService.updateByExampleSelective(ksDistributeStatus, emDistributeStatus);
    }
}
