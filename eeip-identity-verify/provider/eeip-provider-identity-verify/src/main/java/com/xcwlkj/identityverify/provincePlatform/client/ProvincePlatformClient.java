package com.xcwlkj.identityverify.provincePlatform.client;


import com.alibaba.fastjson.JSONObject;
import com.xcwlkj.base.exception.XcBaseErrorCode;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.identityverify.model.constant.GlobalKeys;
import com.xcwlkj.identityverify.model.domain.JyCommonPlatform;
import com.xcwlkj.identityverify.model.domain.JySysDict;
import com.xcwlkj.identityverify.model.vo.sysconfig.PtcslbItemVO;
import com.xcwlkj.identityverify.model.vo.sysconfig.PtcsxxcxVO;
import com.xcwlkj.identityverify.provincePlatform.request.*;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SetValidateInfoDTO;
import com.xcwlkj.identityverify.provincePlatform.request.dto.SetValidateStuEnterDTO;
import com.xcwlkj.identityverify.provincePlatform.response.*;
import com.xcwlkj.identityverify.provincePlatform.response.items.ExamPlanVO;
import com.xcwlkj.identityverify.service.JyCommonPlatformService;
import com.xcwlkj.identityverify.service.JySysDictService;
import com.xcwlkj.identityverify.third.superior.http.model.enums.HisomeParamEnum;
import com.xcwlkj.identityverify.third.superior.http.model.enums.HisomeUriEnum;
import com.xcwlkj.identityverify.third.superior.http.model.enums.SuperiorPlatEnum;
import com.xcwlkj.identityverify.util.HsUtils;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.util.wrapper.Wrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import tk.mybatis.mapper.entity.Example;
import cn.hutool.crypto.symmetric.SM4;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProvincePlatformClient {
    private RestTemplate restTemplate;

    private RestTemplate restTemplateWithProxy;

    @Autowired
    private JySysDictService jySysDictService;
    @Autowired
    private JyCommonPlatformService jyCommonPlatformService;
    @Autowired
    private RedisUtil redisUtil;
    Map<String,String> map = new HashMap<>();

    // appid
//    @Value("${xc.identityVerify.hisomeProvicePlat.appid}")
//    private String appid;
//    // secret
//    @Value("${xc.identityVerify.hisomeProvicePlat.secret}")
//    private String secret;
    @Value("${xc.identityVerify.hisomeProvicePlat.sm4Key:hssfhy@2025~$#@!}")
    private String sm4SecretKey;

    private String proxyIp;
    private String proxyPort;

    private static final String CHAR_SET = "UTF-8";
    private static final String HMAC_SHA1_ALGORITHM = "HmacSHA1";
    private static final String PROVINCE_USERNAME_KEY = "HISOME_username";
    private static final String PROVINCE_PASSWORD_KEY = "HISOME_userpwd";
    private static final String PROVINCE_APPID = "PROVINCE_APPID";
    private static final String PROVINCE_APPKEY = "PROVINCE_APPKEY";
    private static final String PROVINCE_URL_KEY = "URL";
    private static final String PROVINCE_PORT_KEY = "PROVINCE_PORT_KEY";
    private static final String PROVINCE_PROXY_URL_KEY = "PROVINCE_PROXY_URL_KEY";
    private static final String PROVINCE_PROXY_PORT_KEY = "PROVINCE_PROXY_PORT_KEY";
    private static final String PROVINCE_ACCESS_TOKEN = "province:accessToken";
    private static final String PROVINCE_TOKEN_TYPE = "province:tokenType";
    private static final String PROVINCE_USER_TOKEN = "province:userToken";
    // xc_client_sessionid
    private static final String PROVINCE_SESSIONID = "province:xcClientSessionid";

    private static final int INVALID_CONNECT = 80000001;
    //conntoken过期时间设置为20小时
    private static final int connTokenExpireSec = 3600*20;

    // SM4加密相关常量

    @PostConstruct
    public void init(){
        restTemplate = new RestTemplate();
    }
    private RestTemplate getRestTemplate(){
        String proxyIp = getPlatInfoForProvince(PROVINCE_PROXY_URL_KEY);
        String proxyPort = getPlatInfoForProvince(PROVINCE_PROXY_PORT_KEY);
        if(StringUtil.isNotBlank(proxyIp) && StringUtil.isNotBlank(proxyPort)){
            if(StringUtil.equals(this.proxyIp,proxyIp) && StringUtil.equals(this.proxyPort,proxyPort)){
                return restTemplateWithProxy;
            }
            this.proxyIp = proxyIp;
            this.proxyPort = proxyPort;
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(proxyIp, Integer.parseInt(proxyPort)));
            SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
            requestFactory.setProxy(proxy);
            restTemplateWithProxy = new RestTemplate(requestFactory);
            log.info("使用代理：{}:{}",proxyIp,proxyPort);
            return restTemplateWithProxy;
        }
        log.info("不使用代理");
        return restTemplate;
    }

    /**
     * 获取平台有效连接
     * @return
     */
    private void getConnToken(){
        // body:appid,secret
        Map<String,String> body = new HashMap<>();
        body.put("appId",getPlatInfoForProvince(PROVINCE_APPID));
        body.put("secret",getPlatInfoForProvince(PROVINCE_APPKEY));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Map<String, String>> mapHttpEntity = new HttpEntity<>(body, httpHeaders);
        ParameterizedTypeReference<Wrapper<ConnTokenResp>> typeReference = new ParameterizedTypeReference<Wrapper<ConnTokenResp>>(){};
        ResponseEntity<Wrapper<ConnTokenResp>> response = getRestTemplate().exchange(
                getPrefixUrl() + "/manager/getConnToken",
                HttpMethod.POST,
                mapHttpEntity,
                typeReference);
        if(response.getBody() == null || response.getBody().getCode() != 200){
            throw new RuntimeException("获取connToken失败");
        }
        // set xc_client_sessionid
//        this.xc_client_sessionid = response.getBody().getResult().getToken();
        redisUtil.set(PROVINCE_SESSIONID,response.getBody().getResult().getToken(),connTokenExpireSec);
    }

    private void getAccessToken(String connToken){
        AccessTokenRequest accessTokenRequest = new AccessTokenRequest();
        accessTokenRequest.setUserName(getUserInfo(PROVINCE_USERNAME_KEY));
        accessTokenRequest.setPassword(HsUtils.hisomeUperPwdEncode(getUserInfo(PROVINCE_PASSWORD_KEY)));
        accessTokenRequest.setTransChannel("app");
        accessTokenRequest.setCaptchaCode("8800");
        String jsonString = JSONObject.toJSONString(accessTokenRequest);
//        HttpHeaders headers = createHttpHeaders(JSONObject.toJSONString(accessTokenRequest));
        String xcClientSessionid = getXcClientSessionid();
        HttpHeaders headers = new HttpHeaders();
        headers.set("xc_client_sessionid",xcClientSessionid);
        headers.set("Content-Signature",generateContentSignature(jsonString));
        headers.setContentType(MediaType.APPLICATION_JSON);
        // type
        ParameterizedTypeReference<Wrapper<AccessTokenResp>> typeReference = new ParameterizedTypeReference<Wrapper<AccessTokenResp>>(){};

        ResponseEntity<Wrapper<AccessTokenResp>> response = getRestTemplate().exchange(
                getPrefixUrl() + "/manager/secret/getAccessToken",
                HttpMethod.POST,
                new HttpEntity<>(jsonString, headers),
                typeReference);
        log.info("获取accessToken返回结果：{}",response);
        if(response.getBody() == null || response.getBody().getCode() != 200){
            log.warn("省平台账号登录错误！错误信息：{}",response.getBody().getMessage());
            throw new RuntimeException("获取accessToken失败!错误信息："+response.getBody().getMessage());
        }
        // token过期时间
        long expiresIn = Long.parseLong(response.getBody().getResult().getExpiresIn());
        // set accessToken
//        this.accessToken = response.getBody().getResult().getAccessToken();
        redisUtil.set(PROVINCE_ACCESS_TOKEN,response.getBody().getResult().getAccessToken(),expiresIn-600);
        // set tokenType
//        this.tokenType = response.getBody().getResult().getTokenType();
        redisUtil.set(PROVINCE_TOKEN_TYPE,response.getBody().getResult().getTokenType(), expiresIn-600);
        // set userToken
//        this.userToken = response.getBody().getResult().getUserToken();
        redisUtil.set(PROVINCE_USER_TOKEN,response.getBody().getResult().getUserToken(), expiresIn-600);
    }

    private HttpHeaders createHttpHeaders(String bodyString){
        String xcClientSessionId = getXcClientSessionid();
        Object accessToken = redisUtil.get(PROVINCE_ACCESS_TOKEN);
        Object tokenType = redisUtil.get(PROVINCE_TOKEN_TYPE);
        Object userToken = redisUtil.get(PROVINCE_USER_TOKEN);
        // 如果redis中userToken已过期，则重新获取
        if(userToken == null || accessToken == null || tokenType == null){
            this.freshToken();
            accessToken = redisUtil.get(PROVINCE_ACCESS_TOKEN);
            tokenType = redisUtil.get(PROVINCE_TOKEN_TYPE);
            userToken = redisUtil.get(PROVINCE_USER_TOKEN);
        }
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        httpHeaders.set("xc_client_sessionid",xcClientSessionId);
        httpHeaders.set("xc_sso_sessionid",userToken.toString());
        httpHeaders.set("Authorization",tokenType + " " + accessToken);
        String signature = generateContentSignature(bodyString);
        httpHeaders.set("Content-Signature",signature);
        return httpHeaders;
    }

    private String generateContentSignature(String bodyString){
        try {
            String xcClientSessionid = this.getXcClientSessionid();
            Key secretKeySpec = new SecretKeySpec(xcClientSessionid.getBytes(), HMAC_SHA1_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA1_ALGORITHM);
            mac.init(secretKeySpec);
            byte[] rawData = bodyString.getBytes(CHAR_SET);
            byte[] rawHmac = mac.doFinal(rawData);
            return new String(Base64Utils.encode(rawHmac));
        } catch (NoSuchAlgorithmException | InvalidKeyException | UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送请求
     * @param req
     * @return
     */
//    public abstract Wrapper<Resp> send(Req req);

    private <Req extends BaseRequest, Resp> Wrapper<Resp> sendRequest(Req req, String url, ParameterizedTypeReference<Wrapper<Resp>> typeReference) {
        String reqJsonString = JSONObject.toJSONString(req);
        HttpHeaders headers = this.createHttpHeaders(reqJsonString);
        log.info("请求省平台，请求url={}", getPrefixUrl() + url);
        log.info("请求省平台，请求头={}", headers);
        log.info("请求省平台，请求体={}", reqJsonString);
        HttpEntity<String> httpEntity = new HttpEntity<>(reqJsonString, headers);
//        ParameterizedTypeReference<Wrapper<Resp>> typeReference = new ParameterizedTypeReference<Wrapper<Resp>>() {
//        };
        ResponseEntity<Wrapper<Resp>> response = null;
        try {
            // 可能存在token过期，
            // 因此如果第一次失败并且失败原因为登录过期时
            // 则重新获取token并重新发送请求
            for(int i = 0;i<2;++i){
                response = getRestTemplate().exchange(
                        getPrefixUrl() + url,
                        HttpMethod.POST,
                        httpEntity,
                        typeReference
                );
                // 连接失效，重新获取连接
                if(response.getBody().getCode() == INVALID_CONNECT||response.getBody().getCode() == XcBaseErrorCode.ACCOUNT_NOT_LOGGED_IN.value()){
                    this.freshToken();
                }else{
                    break;
                }
            }
        } catch (RestClientException e) {
            throw new RuntimeException(e);
        }
        log.info("请求省平台，返回结果={}", response);
        return response.getBody();
    }

    public void freshToken(){
        this.freshLoginInfo();
        this.getConnToken();
        String xcClientSessionid = getXcClientSessionid();
        this.getAccessToken(xcClientSessionid);
    }

    private void freshLoginInfo(){
        getUserInfo(PROVINCE_USERNAME_KEY);
        getUserInfo(PROVINCE_PASSWORD_KEY);
//        getPlatInfoForProvince(PROVINCE_URL_KEY);

    }

    private String getXcClientSessionid(){
        Object sessionId = redisUtil.get(PROVINCE_SESSIONID);
        if(sessionId == null){
            this.getConnToken();
        }
        sessionId = redisUtil.get(PROVINCE_SESSIONID);
        return sessionId.toString();
    }

    private JyCommonPlatform getPlatInfoForProvince(){
        Object valueObj = redisUtil.get(GlobalKeys.PROVINCE_PLAT_INFO);
        if(valueObj == null){
            JySysDict jySysDict = jySysDictService.queryConfigValueByKey(GlobalKeys.DEFAULT_PLAT);
            if(null == jySysDict){
                log.warn("默认上级平台未设置！");
            }
            String defaultPlatType = jySysDict.getTValue();
            Example example = new Example(JyCommonPlatform.class);
            example.createCriteria().andEqualTo("platType",defaultPlatType);
            List<JyCommonPlatform> jyCommonPlatforms = jyCommonPlatformService.selectListByExample(example);
            if(CollectionUtils.isEmpty(jyCommonPlatforms)){
                log.warn("未找到上级平台配置！:{}",defaultPlatType);
            }
            JyCommonPlatform jyCommonPlatform = jyCommonPlatforms.get(0);
            redisUtil.set(GlobalKeys.PROVINCE_PLAT_INFO,jyCommonPlatform,600);
            valueObj = jyCommonPlatform;
        }
        return (JyCommonPlatform) valueObj;
    }

    private String getPlatInfoForProvince(String key){
        JyCommonPlatform platInfoForProvince = getPlatInfoForProvince();
        switch (key){
            case PROVINCE_APPID:
                return platInfoForProvince.getAppId();
            case PROVINCE_APPKEY:
                return platInfoForProvince.getAppKey();
            case PROVINCE_URL_KEY:
                return platInfoForProvince.getServerIp();
            case PROVINCE_PORT_KEY:
                return platInfoForProvince.getServerPort();
            case PROVINCE_PROXY_URL_KEY:
                return platInfoForProvince.getProxyIp();
            case PROVINCE_PROXY_PORT_KEY:
                return platInfoForProvince.getProxyPort();
            default:
                return null;
        }
    }

    public String getUserInfo(String key){
        PtcsxxcxVO ptcsxxcx = null;
        Map<String, String> ptcs = null;
        String _key;
        _key = getKey(key);
        if (_key == null) return null;
        Object object = redisUtil.get(_key);
        if(object == null) {
            ptcsxxcx = jyCommonPlatformService.ptcsxxcx(SuperiorPlatEnum.HISOME.getCode());
            if (CollectionUtils.isEmpty(ptcsxxcx.getPtcslb())) {
                log.warn("未找到上级平台配置！:{}", SuperiorPlatEnum.HISOME.getCode());
            }
            ptcs = ptcsxxcx.getPtcslb().stream().collect(Collectors.toMap((item)->SuperiorPlatEnum.HISOME.getCode()+"_"+item.getCode(), PtcslbItemVO::getValue));
            object = ptcs.get(_key);
            redisUtil.set(_key, object, 600);
        }
        return object.toString();
    }

    public void clearUserInfoCache(){
        redisUtil.del(getKey(PROVINCE_USERNAME_KEY));
        redisUtil.del(getKey(PROVINCE_PASSWORD_KEY));
    }

    private String getKey(String key) {
        String _key;
        switch (key){
            case PROVINCE_USERNAME_KEY:
                _key = SuperiorPlatEnum.HISOME.getCode() + "_" + HisomeParamEnum.USER_NAME.getCode();
                break;
            case PROVINCE_PASSWORD_KEY:
                _key = SuperiorPlatEnum.HISOME.getCode()+"_"+HisomeParamEnum.USER_PWD.getCode();
                break;
            default:
                return null;
        }
        return _key;
    }

    private String getPrefixUrl(){
        return "http://" + getPlatInfoForProvince(PROVINCE_URL_KEY) + ":" + getPlatInfoForProvince(PROVINCE_PORT_KEY);
    }

    /**
     * 使用SM4加密数据
     * @param data 需要加密的数据对象
     * @return 加密后的字符串
     */
    private String encryptDataWithSM4(Object data) {
        try {
            String jsonString = JSONObject.toJSONString(data);
            log.debug("加密前的JSON数据：{}", jsonString);
            String encryptedData = sm4Encrypt(jsonString, sm4SecretKey);
            log.debug("SM4加密后的数据：{}", encryptedData);
            return encryptedData;
        } catch (Exception e) {
            log.error("SM4加密失败", e);
            throw new RuntimeException("数据加密失败", e);
        }
    }

    /**
     * SM4加密方法
     * @param plainText 明文
     * @param key 密钥
     * @return 加密后的字符串
     */
    private String sm4Encrypt(String plainText, String key) {
        try {
            String sm4Key = ensureSM4KeyLength(key);
            SM4 sm4 = new SM4(sm4Key.getBytes());
            return sm4.encryptHex(plainText);
        } catch (Exception e) {
            log.error("SM4加密失败，plainText: {}, key: {}", plainText, key, e);
            throw new RuntimeException("SM4加密失败", e);
        }
    }

    /**
     * SM4解密方法
     * @param encryptedText 密文
     * @param key 密钥
     * @return 解密后的字符串
     */
    private String sm4Decrypt(String encryptedText, String key) {
        try {
            String sm4Key = ensureSM4KeyLength(key);
            SM4 sm4 = new SM4(sm4Key.getBytes());
            return sm4.decryptStr(encryptedText);
        } catch (Exception e) {
            log.error("SM4解密失败，encryptedText: {}, key: {}", encryptedText, key, e);
            throw new RuntimeException("SM4解密失败", e);
        }
    }

    /**
     * 确保SM4密钥长度为16位
     * @param key 原始密钥
     * @return 16位密钥
     */
    private String ensureSM4KeyLength(String key) {
        if (StringUtil.isBlank(key)) {
            throw new IllegalArgumentException("SM4密钥不能为空");
        }
        if (key.length() == 16) {
            return key;
        } else if (key.length() > 16) {
            return key.substring(0, 16);
        } else {
            return key + "0000000000000000".substring(0, 16 - key.length());
        }
    }

    /**
     * 使用默认密钥进行SM4加密
     * @param plainText 明文
     * @return 加密后的字符串
     */
    public String sm4EncryptWithDefaultKey(String plainText) {
        return sm4Encrypt(plainText, sm4SecretKey);
    }

    /**
     * 使用默认密钥进行SM4解密
     * @param encryptedText 密文
     * @return 解密后的字符串
     */
    public String sm4DecryptWithDefaultKey(String encryptedText) {
        return sm4Decrypt(encryptedText, sm4SecretKey);
    }

    /**
     * 使用指定密钥进行SM4加密
     * @param plainText 明文
     * @param key 密钥
     * @return 加密后的字符串
     */
    public String sm4EncryptWithKey(String plainText, String key) {
        return sm4Encrypt(plainText, key);
    }

    /**
     * 使用指定密钥进行SM4解密
     * @param encryptedText 密文
     * @param key 密钥
     * @return 解密后的字符串
     */
    public String sm4DecryptWithKey(String encryptedText, String key) {
        return sm4Decrypt(encryptedText, key);
    }

    /**
     * 构建考生核验信息请求
     * @param dataDTO 核验数据
     * @param sbxlh 设备序列号
     * @return 构建好的请求对象
     */
    public SetValidateInfoReq buildSetValidateInfoRequest(
            SetValidateInfoDTO dataDTO,
            String sbxlh) {
        String encryptedJson = encryptDataWithSM4(dataDTO);

        return new SetValidateInfoReq(encryptedJson, sbxlh);
    }

    /**
     * 构建考场入场人工请求
     * @param dataDTO 入场数据
     * @param sbxlh 设备序列号
     * @return 构建好的请求对象
     */
    public SetValidateStuEnterReq buildSetValidateStuEnterRequest(
            SetValidateStuEnterDTO dataDTO,
            String sbxlh) {
        String encryptedJson = encryptDataWithSM4(dataDTO);

        return new SetValidateStuEnterReq(encryptedJson, sbxlh);
    }

    /**
     * 构建考生照片上传请求
     * @param dataDTO 照片数据
     * @param sbxlh 设备序列号
     * @return 构建好的请求对象
     */
    public SetValidatePicReq buildSetValidatePicRequest(
            com.xcwlkj.identityverify.provincePlatform.request.dto.SetValidatePicDTO dataDTO,
            String sbxlh) {
        String encryptedJson = encryptDataWithSM4(dataDTO);

        return new SetValidatePicReq(encryptedJson, sbxlh);
    }

    // 考场列表拉取
    public Wrapper<KclbResponse> kclblq(KclbRequest kclbRequest){
        ParameterizedTypeReference<Wrapper<KclbResponse>> typeReference =
                new ParameterizedTypeReference<Wrapper<KclbResponse>>() {};
        return this.sendRequest(kclbRequest,"/ksyw/csCollect/kclb",typeReference);
    }
    // 上报设备信息
    public Wrapper<SubDevInfoResp> subDevInfo(SubDevInfoRequest subDevInfoRequest){
        ParameterizedTypeReference<Wrapper<SubDevInfoResp>> typeReference =
                new ParameterizedTypeReference<Wrapper<SubDevInfoResp>>() {};
        return this.sendRequest(subDevInfoRequest,"/sbyw/devInfoCollect/subDevInfo",typeReference);
    }
    // 拉取设备信息
    public Wrapper<FetchDevInfoResp> fetchDevInfo(FetchDevInfoRequest fetchDevInfoRequest){
        ParameterizedTypeReference<Wrapper<FetchDevInfoResp>> typeReference =
                new ParameterizedTypeReference<Wrapper<FetchDevInfoResp>>() {};
        return this.sendRequest(fetchDevInfoRequest,"/sbyw/devInfoCollect/fetchDevInfo",typeReference);
    }
    // 上报违规考生信息
    public Wrapper<ReportWgksInfoResp> reportWgksInfo(ReportWgksInfoRequest reportWgksInfoRequest) {
        ParameterizedTypeReference<Wrapper<ReportWgksInfoResp>> typeReference = new ParameterizedTypeReference<Wrapper<ReportWgksInfoResp>>() {};
        return this.sendRequest(reportWgksInfoRequest,"/gxdj/wgksZf", typeReference);
    }

    // 获取考试信息文件地址
    public Wrapper<ExamArrgInfoResp> examArrgInfo(BaseDTO<DataDTO> examArrgInfoReq) {
        ParameterizedTypeReference<Wrapper<ExamArrgInfoResp>> typeReference = new ParameterizedTypeReference<Wrapper<ExamArrgInfoResp>>() {};
        return this.sendRequest(examArrgInfoReq, HisomeUriEnum.GET_EXAM_ARRG_INFO.getUrl(), typeReference);
    }

    // 获取考试计划列表
    public Wrapper<List<ExamPlanVO>> examPlan(ExamPlanReq examPlanReq){
        ParameterizedTypeReference<Wrapper<List<ExamPlanVO>>> typeReference = new ParameterizedTypeReference<Wrapper<List<ExamPlanVO>>>() {};
        return this.sendRequest(examPlanReq, HisomeUriEnum.GET_EXAM_PLAN.getUrl(), typeReference);
    }

    // 数据包下载完成上报
    public Wrapper<BaseResponse> pkgDownloadComplete(BaseDTO<PkgDownloadCompleteDTO> pkgDownloadCompleteDTO){
        ParameterizedTypeReference<Wrapper<BaseResponse>> typeReference = new ParameterizedTypeReference<Wrapper<BaseResponse>>() {};
        return this.sendRequest(pkgDownloadCompleteDTO, HisomeUriEnum.PKG_DOWNLOAD_COMPLETE.getUrl(), typeReference);
    }

    // 考生核验信息（新接口）
    public Wrapper<Void> setValidateInfo(SetValidateInfoReq validateInfoReq) {
        ParameterizedTypeReference<Wrapper<Void>> typeReference = new ParameterizedTypeReference<Wrapper<Void>>() {};
        return this.sendRequest(validateInfoReq, HisomeUriEnum.SET_VALIDATE_INFO_NEW.getUrl(), typeReference);
    }

    // 考场入场人工（入场、缺考）（新接口）
    public Wrapper<Void> setValidateStuEnter(SetValidateStuEnterReq validateStuEnterReq) {
        ParameterizedTypeReference<Wrapper<Void>> typeReference = new ParameterizedTypeReference<Wrapper<Void>>() {};
        return this.sendRequest(validateStuEnterReq, HisomeUriEnum.SET_VALIDATE_STU_ENTER_NEW.getUrl(), typeReference);
    }

    // 考生照片上传
    public Wrapper<Void> setValidatePic(SetValidatePicReq validatePicReq) {
        ParameterizedTypeReference<Wrapper<Void>> typeReference = new ParameterizedTypeReference<Wrapper<Void>>() {};
        return this.sendRequest(validatePicReq, HisomeUriEnum.SET_VALIDATE_PIC.getUrl(), typeReference);
    }

    //监考人员签到上传
    public Wrapper<Void> jkryQd(JkryQdReq jkryQdReqBaseDTO){
        ParameterizedTypeReference<Wrapper<Void>> typeReference = new ParameterizedTypeReference<Wrapper<Void>>() {};
        return this.sendRequest(jkryQdReqBaseDTO, HisomeUriEnum.JKRYQD.getUrl(), typeReference);
    }
    //监考照片上传
    public Wrapper<Void> jkqdzp(JkqdZpReq jkqdZpReqBaseDTO){
        ParameterizedTypeReference<Wrapper<Void>> typeReference = new ParameterizedTypeReference<Wrapper<Void>>() {};
        return this.sendRequest(jkqdZpReqBaseDTO, HisomeUriEnum.JKQDZP.getUrl(), typeReference);
    }
}
