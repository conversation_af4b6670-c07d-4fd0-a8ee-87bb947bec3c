package com.xcwlkj.identityverify.third.superior.http.model.enums;

public enum SuperiorPlatEnum {

    JS_JF("JSJ<PERSON>", "江苏佳发", JsJFParamEnum.class, JsJFUriEnum.class),
    HB_TH("HBTH", "湖北泰和", HbTHParamEnum.class, HbTHUriEnum.class),
    HISOME("HISOME","恒生",HisomeParamEnum.class,HisomeUriEnum.class),
    HISOME_V1("HISOMEV1", "省平台老版本核验", null, HisomeV1UriEnum.class),
    SXCJXX("SXCJXX","山西采集信息",null,null);


    private String code;

    private String name;

    private Class<?> superiorPlatParamEnum;

    private Class<?> superiorUriEnum;

    SuperiorPlatEnum(String code, String name, Class<?> superiorPlatParamEnum, Class<?> superiorUriEnum) {
        this.code = code;
        this.name = name;
        this.superiorPlatParamEnum = superiorPlatParamEnum;
        this.superiorUriEnum = superiorUriEnum;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
