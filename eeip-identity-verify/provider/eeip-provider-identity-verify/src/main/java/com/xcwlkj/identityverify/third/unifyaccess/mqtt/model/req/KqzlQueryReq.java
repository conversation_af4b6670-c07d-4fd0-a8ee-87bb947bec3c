package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class KqzlQueryReq extends BaseOperationReq<KqzlQueryReq> {

    /** 考试计划 */
    private String ksjh;
    /** 考试场次 */
    private String kscc;
    /** 设备序列号 */
    private String sbxlh;
    /** 设备类型 */
    private String sblx;
}
