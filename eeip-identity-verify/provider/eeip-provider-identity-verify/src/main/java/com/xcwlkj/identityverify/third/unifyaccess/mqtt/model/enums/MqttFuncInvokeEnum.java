package com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.enums;

import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.vo.FunctionInvokeVO;
import com.xcwlkj.util.StringUtil;

public enum MqttFuncInvokeEnum {
    NOTIFY_XFKSJH("", "下发考试计划", "NOTIFY_XFKSJH", FunctionInvokeVO.class, MqttUnifyAccessEventHandlerEnum.KSSJDR_EVENT_HANDLER)
    ,NOTIFY_XFYDZDGJ("XFYDZDGJ", "移动终端固件下发", "NOTIFY_XFYDZDGJ", FunctionInvokeVO.class, MqttUnifyAccessEventHandlerEnum.YDZDGJJS_EVENT_HANDLER)
    ;
    /**
     * 主题key
     */
    final String code;
    /**
     * 下行functionId
     */
    final String functionId;
    /**
     * 名称
     */
    final String name;
    /**
     * 请求类
     */
    final Class<?> reqClass;

    final MqttUnifyAccessEventHandlerEnum eventHandlerEnum;

    MqttFuncInvokeEnum(String code, String name,String functionId,Class<?> clazz,MqttUnifyAccessEventHandlerEnum eventHandlerEnum) {
        this.code = code;
        this.functionId = functionId;
        this.name = name;
        this.reqClass = clazz;
        this.eventHandlerEnum = eventHandlerEnum;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public String getFunctionId() {
        return functionId;
    }

    public Class<?> getReqClass() {
        return reqClass;
    }

    public MqttUnifyAccessEventHandlerEnum getEventHandlerEnum() {
        return eventHandlerEnum;
    }

    public static MqttFuncInvokeEnum getEvent(String code){
        for (MqttFuncInvokeEnum ele : MqttFuncInvokeEnum.values()) {
            if (StringUtil.equals(ele.getCode(), code)) {
                return ele;
            }
        }
        return null;
    }

    public static MqttFuncInvokeEnum getEventByFuncId(String functionId){
        for (MqttFuncInvokeEnum ele : MqttFuncInvokeEnum.values()) {
            if (StringUtil.equals(ele.getFunctionId(), functionId)) {
                return ele;
            }
        }
        return null;
    }
}
