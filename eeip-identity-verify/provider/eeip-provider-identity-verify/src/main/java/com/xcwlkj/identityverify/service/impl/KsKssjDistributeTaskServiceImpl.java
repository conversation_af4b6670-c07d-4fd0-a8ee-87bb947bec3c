/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.identityverify.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xcwlkj.core.util.RedisUtil;
import com.xcwlkj.exceptions.IdentityVerifyException;
import com.xcwlkj.identityverify.mapper.CsJsjbxxMapper;
import com.xcwlkj.identityverify.mapper.KsKssjDistributeTaskCsMapper;
import com.xcwlkj.identityverify.mapper.KsKssjDistributeTaskMapper;
import com.xcwlkj.identityverify.mapper.SbSbcsgxMapper;
import com.xcwlkj.identityverify.model.domain.*;
import com.xcwlkj.identityverify.model.dos.DistributeParamDO;
import com.xcwlkj.identityverify.model.dos.JcsbxxDO;
import com.xcwlkj.identityverify.model.dos.KcPkgStatusDo;
import com.xcwlkj.identityverify.model.dto.sbzc.XlhcxsbxxDTO;
import com.xcwlkj.identityverify.model.dto.sjxf.*;
import com.xcwlkj.identityverify.model.enums.*;
import com.xcwlkj.identityverify.model.vo.attachment.UploadAttachmentReturnUrlVO;
import com.xcwlkj.identityverify.model.vo.sbzc.XlhcxsbxxVO;
import com.xcwlkj.identityverify.model.vo.sjxf.*;
import com.xcwlkj.identityverify.model.vo.zygl.JsxxlbItemVO;
import com.xcwlkj.identityverify.service.*;
import com.xcwlkj.identityverify.service.impl.task.DelayMessageExecutorRegistry;
import com.xcwlkj.identityverify.service.impl.task.DelayMessageService;
import com.xcwlkj.identityverify.taskcenter.distributeTask.exception.ToppingOffException;
import com.xcwlkj.identityverify.template.DistributeTaskLogTemplate;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.FileDowndloadStatusItem;
import com.xcwlkj.identityverify.third.unifyaccess.mqtt.model.req.KdwgxzztItem;
import com.xcwlkj.identityverify.util.RedisUtilAdapter;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 考试数据下发任务服务
 *
 * <AUTHOR>
 * @version $Id: KsKssjDistributeTaskServiceImpl.java, v 0.1 2023年09月19日 17时16分 xcwlkj.com Exp $
 */
@Slf4j
@Service("ksKssjDistributeTaskService")
public class KsKssjDistributeTaskServiceImpl extends BaseServiceImpl<KsKssjDistributeTaskMapper, KsKssjDistributeTask> implements KsKssjDistributeTaskService {
    // 下发线程状态标记key前缀
    private final static String distributeThreadFlagRedisPrefix = SubSysEnum.IDENTITYVERIFY.getCode() + ":KSYW:distributeThreadFlag";
    @Value("${xc.temp.path}")
    private String tempPath;
    @Value("${xc.identityVerify.distributePkg.max_retry_count}")
    private int maxRetryCount;

    @Resource
    private KsKssjDistributeTaskMapper modelMapper;
    @Resource
    private KsKssjDistributeTaskCsMapper distributeTaskCsMapper;
    @Resource
    private DelayMessageExecutorRegistry delayMessageExecutorRegistry;
    @Resource
    private SbSbxxService sbSbxxService;
    @Resource
    private KsKssjDistributeTaskCsService ksKssjDistributeTaskCsService;
    @Resource
    private KsKssjDistributeStatusService kssjDistributeStatusService;
    @Resource
    private KsKssjDistributeTaskLogService kssjDistributeTaskLogService;
    @Resource
    private KsKcxxService ksKcxxService;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    protected RedisUtilAdapter redisUtilAdapter;
    @Resource
    private KsKdxxService ksKdxxService;
    @Resource
    private AttachmentHandler attachmentHandler;
    @Resource
    private ExamDataService examDataService;
    @Resource
    private KsKsjhService ksKsjhService;
    @Resource
    private KsKssjPkgStatusService ksKssjPkgStatusService;
    @Resource
    private CsJsjbxxMapper csJsjbxxMapper;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private SbSbcsgxMapper sbSbcsgxMapper;

    private ThreadPoolExecutor asyncTaskExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);

    @Override
    public SjxfrwlbVO sjxfrwlb(SjxfrwlbDTO dto) {
        log.info("数据下发任务列表 SjxfrwlbDTO={}", dto);
        Example taskExample = new Example(KsKssjDistributeTask.class);
        Example.Criteria criTask = taskExample.createCriteria().andEqualTo("ksjhbh", dto.getKsjhbh());
        if (dto.getStatus() != null) {
            criTask.andEqualTo("complete", dto.getStatus());
        }
        taskExample.orderBy("startTime").desc();
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<KsKssjDistributeTask> ksDistributeTasks = modelMapper.selectByExample(taskExample);

        Example kcxxExample = new Example(KsKcxx.class);
        kcxxExample.createCriteria()
                .andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
                .andEqualTo("ksjhbh", dto.getKsjhbh())
                .andEqualTo("sfwbykc", CslxEnum.KWB.getCode());
        List<KsKcxx> ksKcxxes = ksKcxxService.selectListByExample(kcxxExample);
        List<String> kwbList = ksKcxxes.stream().map(KsKcxx::getBzhkcid).collect(Collectors.toList());

        List<DistributeTaskItemVO> distributeTaskItemVOS = new ArrayList<>();
        for (KsKssjDistributeTask ksDistributeTask : ksDistributeTasks) {
            DistributeTaskItemVO distributeTaskItemVO = new DistributeTaskItemVO();
            distributeTaskItemVO.setId(ksDistributeTask.getId());
            distributeTaskItemVO.setTaskName(ksDistributeTask.getName());
            distributeTaskItemVO.setDistributeType("2");

            //通知成功数
            int noticeSuccessNum = 0;
            // 通知失败数
            int noticeFailNum = 0;
            // 下载成功数
            int downloadSuccessNum = 0;
            // 下载失败数
            int downloadFailNum = 0;
            Example emTaskCs = new Example(KsKssjDistributeTaskCs.class);
            emTaskCs.createCriteria().andEqualTo("taskId", ksDistributeTask.getId());
            List<KsKssjDistributeTaskCs> distributeTaskCsList = distributeTaskCsMapper.selectByExample(emTaskCs);
            List<String> csbhList = distributeTaskCsList.stream().map(KsKssjDistributeTaskCs::getCsbh).collect(Collectors.toList());
            if (CollectionUtils.containsAny(kwbList, csbhList)) {
                distributeTaskItemVO.setDistributeType("1");
            }
            for (KsKssjDistributeTaskCs distributeTaskCs : distributeTaskCsList) {
                if (distributeTaskCs.getNoticeResult() != null) {
                    if (distributeTaskCs.getNoticeResult() == 1) {
                        noticeSuccessNum++;
                    } else {
                        noticeFailNum++;
                    }
                }
                if (distributeTaskCs.getDownloadFailResult() != null && distributeTaskCs.getDownloadFailResult() > 0) {
                    downloadFailNum++;
                } else if (distributeTaskCs.getDownloadSucResult() != null && distributeTaskCs.getDownloadSucResult() > 0) {
                    downloadSuccessNum++;
                }
            }
            distributeTaskItemVO.setTargetNum(distributeTaskCsList.size());
            distributeTaskItemVO.setNoticeSuccessNum(noticeSuccessNum);
            distributeTaskItemVO.setNoticeFailNum(noticeFailNum);
            distributeTaskItemVO.setDownloadSuccessNum(downloadSuccessNum);
            distributeTaskItemVO.setDownloadFailNum(downloadFailNum);

            distributeTaskItemVO.setStatus(DistributeTaskCompleteStatusEnum.get(ksDistributeTask.getComplete()).getDesc());
            if (ksDistributeTask.getComplete() == DistributeTaskCompleteStatusEnum.Done.getCode()) {
                distributeTaskItemVO.setResult("成功");
            } else if (ksDistributeTask.getComplete() == DistributeTaskCompleteStatusEnum.Fail.getCode()) {
                distributeTaskItemVO.setResult("失败");
            }
            distributeTaskItemVO.setProgress(ksDistributeTask.getTProgress());

            distributeTaskItemVOS.add(distributeTaskItemVO);
        }

        SjxfrwlbVO result = new SjxfrwlbVO();
        result.setDistributeTaskList(distributeTaskItemVOS);
        result.setTotalRows((int) page.getTotal());
        log.info("sjxfrwlb - 数据下发任务列表. [OK] ");
        return result;
    }

    @Override
    public void pkgDownloadCompleteV1(KdwgxzztItem kdwgxzztItem) {
        Example example = new Example(KsKssjDistributeTaskCs.class);
        Example.Criteria criTaskJg = example.createCriteria();
        criTaskJg.andEqualTo("taskId", kdwgxzztItem.getTaskId());

        // 考场模式
        if (StringUtils.equals(kdwgxzztItem.getDevType(), JcsblxEnum.KCWG.getCode())||StringUtils.equals(kdwgxzztItem.getDevType(), JcsblxEnum.DZBP.getCode())) {
            XlhcxsbxxDTO xlhcxsbxxDTO = new XlhcxsbxxDTO();
            xlhcxsbxxDTO.setCssbxlh(kdwgxzztItem.getSn());
            XlhcxsbxxVO result = sbSbxxService.xlhcxsbxx(xlhcxsbxxDTO);
            if (result != null && result.getCssbxxLb().size() < 1) {
                log.error("没找到设备对应的考场信息xlh={}", kdwgxzztItem.getSn());
                return;
            }
            criTaskJg.andEqualTo("csbh", result.getCssbxxLb().get(0).getCsdm())
                    .andEqualTo("cslx", PkgTaskTypeEnum.KC.getMc());
        } else {
            criTaskJg.andEqualTo("csbh", kdwgxzztItem.getOrgCode())
                    .andEqualTo("cslx", PkgTaskTypeEnum.KD.getMc());
        }

        KsKssjDistributeTaskCs ksKssjDistributeTaskCs = formatDownloadStatus(kdwgxzztItem);

        if (!BeanUtil.isEmpty(ksKssjDistributeTaskCs)) {
            ksKssjDistributeTaskCsService.updateByExampleSelective(ksKssjDistributeTaskCs, example);
        }

        //如果下发成功的场所都响应完成，将所属的任务设为 完成
//        Example taskCsExample = new Example(KsKssjDistributeTaskCs.class);
//        Example.Criteria criteria = taskCsExample.createCriteria()
//                .andEqualTo("taskId", kdwgxzztItem.getTaskId());
//        List<KsKssjDistributeTaskCs> distributeTaskCsList = ksKssjDistributeTaskCsService.selectListByExample(taskCsExample);
//        List<String> csbhList = distributeTaskCsList.stream().map(KsKssjDistributeTaskCs::getCsbh).collect(Collectors.toList());
//
//        Example statusExample = new Example(KsKssjDistributeStatus.class);
//        statusExample.createCriteria()
//                .andEqualTo("ksjhbh", kdwgxzztItem.getExamPlanCode())
//                .andIn("bzhkcbh", csbhList);
//        statusExample.and()
//                .orEqualTo("commonPkgStatus", DataDistributeStatusEnum.Doing.getCode())
//                .orEqualTo("stuinfPkgStatus", DataDistributeStatusEnum.Doing.getCode())
//                .orEqualTo("stuzpPkgStatus", DataDistributeStatusEnum.Doing.getCode())
//                .orEqualTo("jkryjbxxPkgStatus", DataDistributeStatusEnum.Doing.getCode())
//                .orEqualTo("jkrybpxxPkgStatus", DataDistributeStatusEnum.Doing.getCode())
//                .orEqualTo("jkryzpPkgStatus", DataDistributeStatusEnum.Doing.getCode());
//        List<KsKssjDistributeStatus> ksKssjDistributeStatuses = kssjDistributeStatusService.selectListByExample(statusExample);
//
//        if (ksKssjDistributeStatuses.size() == 0) {
//            KsKssjDistributeTask task = new KsKssjDistributeTask();
//            task.setComplete(DistributeTaskCompleteStatusEnum.Done.getCode());
//            task.setCompleteTime(new Date());
//            task.setTProgress("100.00");
//            Example taskExample = new Example(KsKssjDistributeTask.class);
//            taskExample.createCriteria().andEqualTo("id", kdwgxzztItem.getTaskId());
//            modelMapper.updateByExampleSelective(task, taskExample);
//        }
    }

    /**
     * 设置下载状态
     *
     * @param kdwgxzztItem
     * @return
     */
    private KsKssjDistributeTaskCs formatDownloadStatus(KdwgxzztItem kdwgxzztItem) {
        KsKssjDistributeTaskCs ksKssjDistributeTaskCs = new KsKssjDistributeTaskCs();
        for (FileDowndloadStatusItem fileDowndloadStatusItem : kdwgxzztItem.getFileDowndloadStatusList()) {
            if (!StringUtils.equals(fileDowndloadStatusItem.getStatus(), "0")) {
                int result = 0;
                switch (PackEnum.getByType(fileDowndloadStatusItem.getType())) {
                    case Pack_GxHisomeStu:
                        result = 1;
                        break;
                    case Pack_GxHisomeJkry:
                        result = 2;
                        break;
                    case Pack_GxHisomeJkryBp:
                        result = 4;
                        break;
                    case Pack_GxHisomeCommon:
                        result = 8;
                        break;
                    case Pack_GxHisomeKdKsZp:
                        result = 16;
                        break;
                    case Pack_GxHisomeKdJkryZp:
                        result = 32;
                        break;
                }
                if (StringUtils.equals(fileDowndloadStatusItem.getStatus(), "1") || StringUtils.equals(fileDowndloadStatusItem.getStatus(), "2")) {
                    ksKssjDistributeTaskCs.setDownloadSucResult((ksKssjDistributeTaskCs.getDownloadSucResult() == null ? 0 : ksKssjDistributeTaskCs.getDownloadSucResult()) | result);
                } else {
                    ksKssjDistributeTaskCs.setDownloadFailResult((ksKssjDistributeTaskCs.getDownloadFailResult() == null ? 0 : ksKssjDistributeTaskCs.getDownloadFailResult()) | result);
                }
            }
        }
        return ksKssjDistributeTaskCs;
    }

    @Override
    public SjxfrwxjVO sjxfrwxj(SjxfrwxjDTO dto) {
        log.info("数据下发任务新建 SjxfrwxjDTO={}", dto);
        SjxfrwxjVO result = new SjxfrwxjVO();
        KcPkgStatusDo kcPkgStatusDo = ksKssjPkgStatusService.getPkgStatusByType(dto.getKsjhbh(), dto.getPkgType(), dto.getJgidList());
        if (StringUtils.equals(kcPkgStatusDo.getStatus(), "0")) {
            BeanUtil.copyProperties(kcPkgStatusDo, result);
            return result;
        }
        List<String> jgidList = dto.getJgidList();
        checkStatus(jgidList);
        if (StringUtils.isBlank(dto.getTaskName())) {
            dto.setTaskName(jgidList.get(0) + "_" + DateUtil.format(new Date(), "yyyyMMdd_HHmmss"));
        }


        List<JsxxlbItemVO> jsxxlb = csJsjbxxMapper.jsxxlbcx(null, null, null, null);
        Map<String, String> kcMap = jsxxlb.stream()
                .filter(item -> StringUtils.isNotBlank(item.getBzhkcid()))
                .collect(Collectors.toMap(JsxxlbItemVO::getBzhkcid, JsxxlbItemVO::getCsmc, (oldV, newV) -> newV));

        // 初始化任务
        KsKssjDistributeTask distributeTask = new KsKssjDistributeTask();
        distributeTask.setId(IdGenerateUtil.generateId());
        distributeTask.setName(dto.getTaskName());
        distributeTask.setKsjhbh(dto.getKsjhbh());
        distributeTask.setTProgress("0");
        distributeTask.setComplete(DistributeTaskCompleteStatusEnum.Begin.getCode());
        distributeTask.setStartTime(new Date());
        modelMapper.insertSelective(distributeTask);

        Example statusExample = new Example(KsKssjDistributeStatus.class);
        statusExample.createCriteria().andEqualTo("ksjhbh", dto.getKsjhbh());
        List<String> bzhkcLIst = kssjDistributeStatusService.selectListByExample(statusExample).stream().map(KsKssjDistributeStatus::getBzhkcbh).collect(Collectors.toList());

        List<KsKssjDistributeTaskCs> distributeTaskCsList = new ArrayList<>();
        ArrayList<KsKssjDistributeStatus> distributeStatusList = new ArrayList<>();
        for (String jgid : jgidList) {
            KsKssjDistributeTaskCs ksDistributeTaskJg = new KsKssjDistributeTaskCs();
            ksDistributeTaskJg.setId(IdGenerateUtil.generateId());
            ksDistributeTaskJg.setTaskId(distributeTask.getId());
            ksDistributeTaskJg.setCsbh(jgid);
            ksDistributeTaskJg.setCslx("kc");
            distributeTaskCsList.add(ksDistributeTaskJg);

            if (!bzhkcLIst.contains(jgid)) {
                KsKssjDistributeStatus ksKssjDistributeStatus = new KsKssjDistributeStatus();
                ksKssjDistributeStatus.setId(IdGenerateUtil.generateId());
                ksKssjDistributeStatus.setKsjhbh(dto.getKsjhbh());
                ksKssjDistributeStatus.setBzhkcbh(jgid);
                ksKssjDistributeStatus.setBzhkcmc(kcMap.getOrDefault(jgid, ""));
                distributeStatusList.add(ksKssjDistributeStatus);
            }
        }
        distributeTaskCsMapper.insertListSelective(distributeTaskCsList);
        if (CollectionUtils.isNotEmpty(distributeStatusList)) {
            kssjDistributeStatusService.insertListSelective(distributeStatusList);
        }

        kssjDistributeStatusService.initStatus(dto.getKsjhbh(), jgidList);

        DistributeTask task = new DistributeTask(dto.getKsjhbh(), jgidList, distributeTask.getId(), dto.getPkgType());
        asyncTaskExecutor.execute(task);

        log.info("sjxfrwxj - 数据下发任务新建. [OK] ");
        result.setPkgType(dto.getPkgType());
        result.setStatus("1");
        result.setResult("数据下发任务新建成功");
        return result;
    }

    class DistributeTask implements Runnable {
        String ksjhbh;
        List<String> jgidList;
        String taskId;
        String pkgType;

        public DistributeTask(String ksjhbh, List<String> jgidList, String taskId, String pkgType) {
            this.ksjhbh = ksjhbh;
            this.jgidList = jgidList;
            this.taskId = taskId;
            this.pkgType = pkgType;
        }

        @Override
        public void run() {
            invoke(ksjhbh, jgidList, taskId, pkgType);
        }

        public void invoke(String ksjhbh, List<String> csbhList, String taskId, String pkgType) {
            Example taskExample = new Example(KsKssjDistributeTask.class);
            taskExample.createCriteria().andEqualTo("id", taskId);

            KsKssjDistributeTask ksDistributeTask = new KsKssjDistributeTask();
            ksDistributeTask.setComplete(DistributeTaskCompleteStatusEnum.Doing.getCode());
            modelMapper.updateByExampleSelective(ksDistributeTask, taskExample);
            try {
                //加入延时队列，用于超时判断
                DelayMessageService dms = delayMessageExecutorRegistry.getExecutor(DelayMessageServiceEnum.DELAY_MESSAGE_PKG_DISTRIBUTE);
                dms.addJob(taskId, 1000 * 60 * 10);

                DistributeParamDO distributeParamDO = new DistributeParamDO();
                distributeParamDO.setKsjhbh(ksjhbh);
                distributeParamDO.setTaskId(taskId);
                distributeParamDO.setTaskType(PkgTaskTypeEnum.KC.getCode());
                distributeParamDO.setCsbhList(csbhList);
                distributeParamDO.setRetyrCount(maxRetryCount);
                distributeParamDO.setRetryInterval(10);
                distributeParamDO.setPkgType(pkgType);

                examDataService.distributePack(distributeParamDO);
            } catch (ToppingOffException toppingOffException) {
                log.info("手动停止, taskId = " + taskId);
                log.warn(toppingOffException.getMessage(), toppingOffException);
                KsKssjDistributeTask failTask = new KsKssjDistributeTask();
                failTask.setComplete(DistributeTaskCompleteStatusEnum.ToppingOff.getCode());
                modelMapper.updateByExampleSelective(failTask, taskExample);
            } catch (Exception e) {
                log.error("任务失败, taskId = " + taskId);
                log.error(e.getMessage(), e);
                KsKssjDistributeTask failTask = new KsKssjDistributeTask();
                failTask.setComplete(DistributeTaskCompleteStatusEnum.Fail.getCode());
                modelMapper.updateByExampleSelective(failTask, taskExample);
            }

        }
    }


    @Override
    public void sjxfrwxj(String ksjhbh) {
        List<String> bzhkcidList = new ArrayList<>();
        Example example = new Example(KsKcxx.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhbh", ksjhbh);
        criteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        criteria.andIsNotNull("bzhkcid");
        List<KsKcxx> ksKcxxes = ksKcxxService.selectListByExample(example);
        if (CollectionUtils.isNotEmpty(ksKcxxes)) {
            bzhkcidList = ksKcxxes.stream().map(KsKcxx::getBzhkcid).distinct().collect(Collectors.toList());
        } else {
            List<JcsbxxDO> jcsbxxDOS = sbSbcsgxMapper.jcsbxxlbcxV2(null, null);
            if (CollectionUtils.isEmpty(jcsbxxDOS)) {
                throw new IdentityVerifyException("数据同步-数据下发任务创建失败！，无可下发设备！");
            }
            bzhkcidList = jcsbxxDOS.stream().map(JcsbxxDO::getCsbh).distinct().collect(Collectors.toList());
        }
        SjxfrwxjDTO sjxfrwxjDTO = new SjxfrwxjDTO();
        sjxfrwxjDTO.setKsjhbh(ksjhbh);
        sjxfrwxjDTO.setTaskName("数据同步-数据下发任务" + "_" + ksjhbh + "_" + DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT));
        sjxfrwxjDTO.setJgidList(bzhkcidList);
        sjxfrwxjDTO.setPkgType("zdsp");
        SjxfrwxjVO sjxfrwxj = sjxfrwxj(sjxfrwxjDTO);
        log.info("数据同步-数据下发任务: {}", sjxfrwxj.getResult());
    }

    public void checkStatus(List<String> csbhList) {
        Example emTask = new Example(KsKssjDistributeTask.class);
        emTask.createCriteria().andEqualTo("complete", DistributeTaskCompleteStatusEnum.Doing.getCode());
        List<KsKssjDistributeTask> ksDistributeTasks = modelMapper.selectByExample(emTask);

        if (ksDistributeTasks.isEmpty()) {
            return;
        }
        List<String> taskIds = ksDistributeTasks.stream().map(KsKssjDistributeTask::getId).collect(Collectors.toList());
        Example emCs = new Example(KsKssjDistributeTaskCs.class);
        emCs.createCriteria().andIn("taskId", taskIds);
        List<KsKssjDistributeTaskCs> distributeTaskCsList = distributeTaskCsMapper.selectByExample(emCs);
        if (distributeTaskCsList.isEmpty()) {
            return;
        }
        Set<String> doingCsbhs = distributeTaskCsList.stream().map(KsKssjDistributeTaskCs::getCsbh).collect(Collectors.toSet());
        if (doingCsbhs.containsAll(csbhList)) {
            throw new IdentityVerifyException("考点正在下发中,请勿重复操作!");
        }
    }

    @Override
    public void sjxfrwplsc(SjxfrwplscDTO dto) {
        log.info("数据下发任务删除 SjxfrwplscDTO={}", dto);

        Example emTask = new Example(KsKssjDistributeTask.class);
        emTask.createCriteria().andIn("id", dto.getIds());
        modelMapper.deleteByExample(emTask);

        Example emTaskCs = new Example(KsKssjDistributeTaskCs.class);
        emTaskCs.createCriteria().andIn("taskId", dto.getIds());
        distributeTaskCsMapper.deleteByExample(emTaskCs);

        log.info("sjxfrwplsc - 数据下发任务删除. [OK] ");
    }

    @Override
    public SjxfrwxqVO sjxfrwxq(SjxfrwxqDTO dto) {
        log.info("数据下发任务详情 SjxfrwxqDTO={}", dto);

        Example example = new Example(KsKssjDistributeTask.class);
        example.createCriteria().andEqualTo("id", dto.getId());
        KsKssjDistributeTask distributeTask = modelMapper.selectOneByExample(example);

        SjxfrwxqVO result = new SjxfrwxqVO();
        result.setKsjhbh(distributeTask.getKsjhbh());
        result.setTaskName(distributeTask.getName());

        log.info("sjxfrwxq - 数据下发任务详情. [OK] SjxfrwxqVO={}", result);
        return result;
    }

    @Override
    public SjxfrwxfjgfwVO sjxfrwxfjgfw(SjxfrwxfjgfwDTO dto) {
        log.info("数据下发任务下发机构范围 SjxfrwxfjgfwDTO={}", dto);

        Example example = new Example(KsKssjDistributeTask.class);
        example.createCriteria().andEqualTo("id", dto.getTaskId());
        KsKssjDistributeTask ksDistributeTask = modelMapper.selectOneByExample(example);

        Example emDistributeTaskCs = new Example(KsKssjDistributeTaskCs.class);
        emDistributeTaskCs.createCriteria()
                .andEqualTo("taskId", dto.getTaskId())
                .andEqualTo("cslx", PkgTaskTypeEnum.KC.getMc());
        emDistributeTaskCs.selectProperties("csbh");
        Page<Object> page = PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<String> csbhList = distributeTaskCsMapper.selectByExample(emDistributeTaskCs).stream().map(KsKssjDistributeTaskCs::getCsbh).collect(Collectors.toList());

        Example emKcxx = new Example(KsKcxx.class);
        emKcxx.createCriteria()
                .andIn("bzhkcid", csbhList)
                .andEqualTo("ksjhbh", ksDistributeTask.getKsjhbh());
        emKcxx.selectProperties("bzhkcmc");
        List<String> jgNameList = ksKcxxService.selectListByExample(emKcxx).stream().map(KsKcxx::getBzhkcmc).distinct().collect(Collectors.toList());

        SjxfrwxfjgfwVO result = new SjxfrwxfjgfwVO();
        result.setTotalNum((int) page.getTotal());
        result.setJgNameList(jgNameList);

        log.info("sjxfrwxfjgfw - 数据下发任务下发机构范围. [OK] SjxfrwxfjgfwVO={}", result);
        return result;
    }

    @Override
    public void sjxfrwsdtz(SjxfrwsdtzDTO dto) {
        log.info("数据下发任务手动停止 {}", dto);
        String taskId = dto.getId();
        Long num = redisUtilAdapter.zRemove(DelayMessageServiceEnum.DELAY_MESSAGE_PKG_DISTRIBUTE.getRedisKey(), taskId);
        if (num > 0) {
            log.info("延时任务移除数据下发任务成功");
            String distributeThreadFlagRedisKey = distributeThreadFlagRedisPrefix + ":" + taskId;
            redisUtil.lock(distributeThreadFlagRedisKey + "_lock", 5000, () -> {
                redisUtil.del(distributeThreadFlagRedisKey);
                return null;
            });
            Example example = new Example(KsKssjDistributeTask.class);
            example.createCriteria().andEqualTo("id", taskId);
            KsKssjDistributeTask failTask = new KsKssjDistributeTask();
            failTask.setComplete(DistributeTaskCompleteStatusEnum.ToppingOff.getCode());
            modelMapper.updateByExampleSelective(failTask, example);
        } else {
            log.info("数据下发任务已完成");
        }

        log.info("dataDistributeToppingOff - 数据下发任务手动停止. [OK]");
    }

    @Override
    public SjfxrwrzxzVO sjfxrwrzxz(SjfxrwrzxzDTO dto) {
        log.info("数据下发任务日志 {}", dto);
        SjfxrwrzxzVO result = new SjfxrwrzxzVO();

        String taskId = dto.getId();
        Example taskExample = new Example(KsKssjDistributeTask.class);
        taskExample.createCriteria().andEqualTo("id", taskId);
        KsKssjDistributeTask ksDistributeTask = modelMapper.selectOneByExample(taskExample);

        // 日志信息
        Example emtaskLog = new Example(KsKssjDistributeTaskLog.class);
        emtaskLog.createCriteria().andEqualTo("dTaskId", taskId);
        emtaskLog.orderBy("distributeTime");
        List<KsKssjDistributeTaskLog> ksDistributeTaskLogs = kssjDistributeTaskLogService.selectListByExample(emtaskLog);
        if (ksDistributeTaskLogs.size() == 0) {
            throw new IdentityVerifyException("日志记录为空");
        }

        // 场所信息
        Map<String, KsKcxx> csMap; // 场所id-场所映射
        Map<String, String> kdMap; // 考点id-名称映射
        List<String> kdidList = ksDistributeTaskLogs.stream().map(KsKssjDistributeTaskLog::getBzhkdid).distinct().collect(Collectors.toList());
        Example kdxxExample = new Example(KsKdxx.class);
        kdxxExample.createCriteria().andEqualTo("ksjhbh", ksDistributeTask.getKsjhbh()).andIn("bzhkdid", kdidList);
        kdxxExample.selectProperties("bzhkdid", "bzhkdmc");
        List<KsKdxx> ksKdxxList = ksKdxxService.selectListByExample(kdxxExample);
        kdMap = ksKdxxList.stream().collect(Collectors.toMap(KsKdxx::getBzhkdid, KsKdxx::getBzhkdmc, (oldV, newV) -> newV));

        List<String> kcbhList = ksDistributeTaskLogs.stream().map(KsKssjDistributeTaskLog::getBzhkcbh).distinct().collect(Collectors.toList());
        Example kcxxExample = new Example(KsKcxx.class);
        kcxxExample.createCriteria().andEqualTo("ksjhbh", ksDistributeTask.getKsjhbh()).andIn("bzhkcid", kcbhList);
        csMap = ksKcxxService.selectListByExample(kcxxExample).stream().collect(Collectors.toMap(KsKcxx::getBzhkcid, Function.identity(), (oldV, newV) -> newV));

        List<DistributeTaskLogTemplate> datas = new ArrayList<>();
        for (KsKssjDistributeTaskLog log : ksDistributeTaskLogs) {
            DistributeTaskLogTemplate data = new DistributeTaskLogTemplate();
            data.setDistributeTime(DateUtil.formatDateTime(log.getDistributeTime()));
            data.setKsjhbh(log.getKsjhbh());
            data.setBzhkdmc(kdMap.get(log.getBzhkdid()));
            KsKcxx ksKcxx = csMap.get(log.getBzhkcbh());
            if (ksKcxx != null) {
                String cslx = StringUtils.isBlank(ksKcxx.getSfwbykc()) ? "0" : ksKcxx.getSfwbykc();
                data.setCsmc(ksKcxx.getBzhkcmc());
                data.setCslx(CslxEnum.get(cslx).getDesc());
            } else {
                data.setCsmc(log.getBzhkcbh());
                data.setCslx("-");
            }
            data.setSn(log.getTSn());
            data.setDevType(log.getDevType());
            data.setStuinfVersion(log.getStuinfVersion());
            data.setStuinfResult(DistributeLogResultEnum.get(log.getStuinfResult()).getDesc());
            data.setCommonVersion(log.getCommonVersion());
            data.setCommonResult(DistributeLogResultEnum.get(log.getCommonResult()).getDesc());
            data.setJkryjbxxVersion(log.getJkryjbxxVersion());
            data.setJkryjbxxResult(DistributeLogResultEnum.get(log.getJkryjbxxResult()).getDesc());
            data.setJkrybpxxVersion(log.getJkrybpxxVersion());
            data.setJkrybpxxResult(DistributeLogResultEnum.get(log.getJkrybpxxResult()).getDesc());
            datas.add(data);
        }

        String dir = "distributeTaskLog_" + DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT);
        String logPath = tempPath + dir;
        File logPathFile = new File(logPath);
        if (!logPathFile.exists()) {
            logPathFile.mkdirs();
        }
        String logName = "下发任务日志_" + ksDistributeTask.getName() + "_" + taskId + ".xlsx";
        String excelPath = logPath + File.separator + logName;
        EasyExcel.write(excelPath, DistributeTaskLogTemplate.class).sheet().doWrite(datas);

        Date expireTIme = DateUtil.offsetHour(DateUtil.getCurrentDT(), 2);
        UploadAttachmentReturnUrlVO returnUrlVO = attachmentHandler.uploadAttachmentReturnUrl(excelPath, expireTIme, null);
        log.info("returnUrlVO:" + returnUrlVO);
        if (returnUrlVO != null) {
            result.setPath(returnUrlVO.getAttachmentUrl());
        }
        try {
            FileUtils.deleteDirectory(new File(logPath));
        } catch (IOException e) {
            e.printStackTrace();
        }
        log.info("sjfxrwrzxz - 数据下发任务日志下载. [OK]");
        return result;
    }

    @Override
    public SjxfrwxjVO pkgDoneAutoDist(String ksjhbh, Integer packMode) {
        Example example = new Example(KsKcxx.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ksjhbh", ksjhbh);
        criteria.andEqualTo("sczt", ScztEnum.NOTDEL.getCode());
        criteria.andIsNotNull("bzhkcid");

        List<String> bzhkcidList = new ArrayList<>();
        if (Objects.equals(packMode, PkgCatalogEnum.KD.getValue())) {
            Example.Criteria and = example.and();
            and.andEqualTo("sfwbykc", CslxEnum.BYKC.getCode());
            and.orEqualTo("sfwbykc", CslxEnum.KWB.getCode());
            List<KsKcxx> ksKcxxes = ksKcxxService.selectListByExample(example);
            bzhkcidList = ksKcxxes.stream().map(KsKcxx::getBzhkcid).distinct().collect(Collectors.toList());
        } else if (Objects.equals(packMode, PkgCatalogEnum.KC.getValue())) {
            Example.Criteria and = example.and();
            and.andEqualTo("sfwbykc", CslxEnum.KC.getCode());
            and.orIsNull("sfwbykc");
            List<KsKcxx> ksKcxxes = ksKcxxService.selectListByExample(example);
            bzhkcidList = ksKcxxes.stream().map(KsKcxx::getBzhkcid).distinct().collect(Collectors.toList());
        } else if (Objects.equals(packMode, PkgCatalogEnum.QL.getValue())) {
            List<KsKcxx> ksKcxxes = ksKcxxService.selectListByExample(example);
            bzhkcidList = ksKcxxes.stream().map(KsKcxx::getBzhkcid).distinct().collect(Collectors.toList());
        }
        if(CollectionUtils.isEmpty(bzhkcidList)) {
            SjxfrwxjVO sjxfrwxjVO = new SjxfrwxjVO();
            log.warn("当前打包任务无考场对应,取消下发任务");
            sjxfrwxjVO.setStatus("-1");
            return sjxfrwxjVO;
        }
        SjxfrwxjDTO sjxfrwxjDTO = new SjxfrwxjDTO();
        sjxfrwxjDTO.setKsjhbh(ksjhbh);
        sjxfrwxjDTO.setTaskName(DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.DATE_SHORT_FORMAT));
        sjxfrwxjDTO.setJgidList(bzhkcidList);
        sjxfrwxjDTO.setPkgType("zdsp");
        SjxfrwxjVO sjxfrwxj = sjxfrwxj(sjxfrwxjDTO);
        return sjxfrwxj;
    }
}