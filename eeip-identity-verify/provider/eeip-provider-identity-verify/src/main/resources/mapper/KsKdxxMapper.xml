<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xcwlkj.identityverify.mapper.KsKdxxMapper">

    <resultMap id="BaseResultMap" type="com.xcwlkj.identityverify.model.domain.KsKdxx">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="ksjhbh" column="ksjhbh" jdbcType="VARCHAR"/>
            <result property="ccm" column="ccm" jdbcType="VARCHAR"/>
            <result property="kqbh" column="kqbh" jdbcType="VARCHAR"/>
            <result property="kqmc" column="kqmc" jdbcType="VARCHAR"/>
            <result property="kdbh" column="kdbh" jdbcType="VARCHAR"/>
            <result property="kdmc" column="kdmc" jdbcType="VARCHAR"/>
            <result property="bzhkdid" column="bzhkdid" jdbcType="VARCHAR"/>
            <result property="bzhkdmc" column="bzhkdmc" jdbcType="VARCHAR"/>
            <result property="scztw" column="scztw" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,ksjhbh,ccm,
        kqbh,kqmc,kdbh,
        kdmc,bzhkdid,bzhkdmc,
        scztw,create_time,update_time
    </sql>
</mapper>
