/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2034 All Rights Reserved.
 */
package com.xcwlkj.identityverify.model.req.zygl;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 校区信息修改请求
 * <AUTHOR>
 * @version $Id: XqxxxgReqModel.java, v 0.1 2024年01月29日 15时11分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class XqxxxgReqModel extends RemoteReqBaseModel {
    /** 序列ID */
    private static final long serialVersionUID = -1L;

    /** 校区号 */
    @NotBlank(message = "校区号不能为空")
    private String xqh;
    /** 校区名称 */
    private String xqmc;
    /** 校区地址 */
    private String xqdz;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}