package com.xcwlkj.identityverify.model.req.sbmlpz;

import com.xcwlkj.base.remote.RemoteReqBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;

@Data
@EqualsAndHashCode(callSuper = true)
public class SbmlpzXgReqModel extends RemoteReqBaseModel {

    /** id */
    @NotBlank(message = "id不能为空")
    private String id;
    /** 命令类型 0-配置端口转发,1-接入配置参数,2-NTP配置参数，3-设备重启 */
    @NotBlank(message = "命令类型不能为空")
    private String cmdType;

    //2-NTP配置参数
    /** ntp源 */
    private String ntpSource;
    /** ntp启用状态 1-启用，0-不启用 */
    private String ntpEnable;
    /** ntp间隔 单位：秒 */
    private String ntpInterval;

    //3-设备重启
    /** 设备重启倒计时 单位：秒 */
    private String delay;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
