/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.model.dto.zbxg;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotNull;


/**
 * 01  值守情况查询dto
 * <AUTHOR>
 * @version $Id: ZsqkcxDTO.java, v 0.1 2020年05月19日 17时14分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class ZsqkcxDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 保密室编号 */
    private String bmsbh;
    /** 考试管理机构编号 */
    private String ksgjlgbh;
    /** 开始时间 */
    private String kssj;
    /** 结束时间 */
    private String jssj;
    /** 当前页 */
    @NotNull(message = "当前页不能为空")
    private Integer pageNum;
    /** 页数 */
    @NotNull(message = "页数不能为空")
    private Integer pageSize;

}
