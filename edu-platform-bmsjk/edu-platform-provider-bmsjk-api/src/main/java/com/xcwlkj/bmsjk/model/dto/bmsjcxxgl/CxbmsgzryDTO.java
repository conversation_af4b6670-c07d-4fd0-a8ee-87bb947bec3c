/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.model.dto.bmsjcxxgl;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import java.util.List;


/**
 * 11  查询保密室工作人员dto
 * <AUTHOR>
 * @version $Id: CxbmsgzryDTO.java, v 0.1 2020年06月16日 20时51分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CxbmsgzryDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 机构id */
    @NotBlank(message = "机构id不能为空")
    private String jgid;
    /** ksgjjg/bms */
    @NotBlank(message = "ksgjjg/bms不能为空")
    private String jglx;
    /** 工作人员编号，要排除掉 */
    private List<String> rybhs;

}
