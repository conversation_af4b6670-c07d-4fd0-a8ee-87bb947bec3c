/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.model.vo.zbap;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 11  查看保密室值班安排表情况--下级vo
 * <AUTHOR>
 * @version $Id: CkbmszbapbqkxjVO.java, v 0.1 2020年04月26日 09时58分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CkbmszbapbqkxjVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 值班安排表列表 */
    private List<ZbapbItemVO> zbapbList;
    /** 总条数 */
    private Integer totalRows;

}
