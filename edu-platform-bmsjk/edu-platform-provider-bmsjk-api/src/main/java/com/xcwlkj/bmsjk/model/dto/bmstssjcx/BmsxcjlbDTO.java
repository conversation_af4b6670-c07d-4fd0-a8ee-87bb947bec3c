/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.model.dto.bmstssjcx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 保密室推送数据查询-1.巡查记录表dto
 * <AUTHOR>
 * @version $Id: BmsxcjlbDTO.java, v 0.1 2021年07月15日 18时05分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class BmsxcjlbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	

}
