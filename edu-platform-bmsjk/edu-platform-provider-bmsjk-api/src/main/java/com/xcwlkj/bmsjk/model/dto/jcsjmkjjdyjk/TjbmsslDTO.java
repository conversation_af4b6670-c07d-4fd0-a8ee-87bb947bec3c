/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.model.dto.jcsjmkjjdyjk;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 09 统计保密室数量（包含下级）dto
 * <AUTHOR>
 * @version $Id: TjbmsslDTO.java, v 0.1 2020年05月28日 14时22分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class TjbmsslDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 考试管理机构id */
    private String ksgljgid;

}
