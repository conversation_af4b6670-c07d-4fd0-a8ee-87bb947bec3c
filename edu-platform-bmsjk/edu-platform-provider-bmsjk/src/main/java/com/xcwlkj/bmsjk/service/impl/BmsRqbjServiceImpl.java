/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.service.impl;

import org.springframework.stereotype.Service;

import com.xcwlkj.bmsjk.mapper.BmsRqbjMapper;
import com.xcwlkj.bmsjk.model.domain.BmsRqbj;
import com.xcwlkj.bmsjk.service.BmsRqbjService;

import tk.mybatis.mapper.entity.Example;

import java.util.List;

import javax.annotation.Resource;


/**
 * 入侵报警信息服务
 * <AUTHOR>
 * @version $Id: BmsRqbjServiceImpl.java, v 0.1 2020年09月16日 10时42分 xcwlkj.com Exp $
 */
@Service("bmsRqbjService")
public class BmsRqbjServiceImpl  implements BmsRqbjService  {

    @Resource
    private BmsRqbjMapper modelMapper;
    
    @Override
    public List<BmsRqbj> selectBmsrqbjByKsgljgid(String ksgljgid, String kssj, String jssj) {
        return modelMapper.selectBmsrqbjByKsgljgid(ksgljgid, kssj, jssj);
    }

    @Override
    public List<BmsRqbj> selectByExample(Example example) {
        return modelMapper.selectByExample(example);
    }

}