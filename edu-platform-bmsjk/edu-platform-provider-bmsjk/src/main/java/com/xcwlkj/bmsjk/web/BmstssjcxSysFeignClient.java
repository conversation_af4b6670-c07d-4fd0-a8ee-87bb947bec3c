/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.bmsjk.web;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.xcwlkj.bmsjk.model.domain.BmsSphfjlsb;
import com.xcwlkj.bmsjk.model.domain.BmsXcjl;
import com.xcwlkj.bmsjk.model.domain.PushMessageTemp;
import com.xcwlkj.bmsjk.model.enums.DataBaseEnum;
import com.xcwlkj.bmsjk.model.vo.bmstssjcx.BmssphfjlbLbItemVO;
import com.xcwlkj.bmsjk.model.vo.bmstssjcx.BmsxcjlLbItemVO;
import com.xcwlkj.bmsjk.service.BmsSphfjlsbService;
import com.xcwlkj.bmsjk.service.BmsXcjlService;
import com.xcwlkj.bmsjk.service.PushMessageTempService;
import com.xcwlkj.model.enums.ScztEnum;
import com.xcwlkj.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.xcwlkj.bmsjk.service.BmstssjcxSysFeignApi;
import com.xcwlkj.base.BaseFeignClient;

import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.bmsjk.model.dto.bmstssjcx.BmsxcjlbDTO;
import com.xcwlkj.bmsjk.model.vo.bmstssjcx.BmsxcjlbVO;
import com.xcwlkj.bmsjk.model.dto.bmstssjcx.BmssphfjlbDTO;
import org.springframework.validation.annotation.Validated;
import com.xcwlkj.bmsjk.model.vo.bmstssjcx.BmssphfjlbVO;
import com.xcwlkj.util.wrapper.Wrapper;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Bmstssjcx接口
 * <AUTHOR>
 * @version $Id: BmstssjcxSysFeignClient.java, v 0.1 2021年07月15日 18时05分 xcwlkj.com Exp $
 */
@RestController
public class BmstssjcxSysFeignClient extends BaseFeignClient implements BmstssjcxSysFeignApi{

	@Autowired
	private BmsXcjlService xcjlService;

	@Autowired
	private BmsSphfjlsbService sphfjlsbService;

	@Autowired
	private PushMessageTempService pushMessageTempService;

    
    /** 
     * @see com.xcwlkj.bmsjk.service.BmstssjcxSysFeignApi#bmsxcjlb(com.xcwlkj.bmsjk.model.dto.bmstssjcx.BmsxcjlbDTO)
     */
	@Override
	public Wrapper<BmsxcjlbVO> bmsxcjlb(@RequestBody @Validated BmsxcjlbDTO dto) {
		logger.info("保密室推送数据查询-1.巡查记录表BmsxcjlbDTO={}", dto);
		BmsxcjlbVO result = new BmsxcjlbVO();
		List<BmsxcjlLbItemVO> list = new ArrayList<>();

		PushMessageTemp tempTable = pushMessageTempService.selectByKey(DataBaseEnum.XCJLB.getCode());

		Example xcjlExam = new Example(BmsXcjl.class);
		Example.Criteria xcjxCri = xcjlExam.createCriteria()
				.andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
				.andIsNotNull("createTime");
		if (!BeanUtil.isEmpty(tempTable)){//非空 查询同步时间之后的
			Date synchroTime = tempTable.getSynchroTime();
			xcjxCri.andGreaterThan("createTime",synchroTime);
		}

		xcjlExam.setOrderByClause(" create_time DESC");
		List<BmsXcjl> bmsXcjlList = xcjlService.selectByExample(xcjlExam);

		if (CollectionUtil.isNotEmpty(bmsXcjlList)){
			//更新时间为数据的最近一条
			pushMessageTempService.updateSychroTime(DataBaseEnum.XCJLB.getCode(),bmsXcjlList.get(0).getCreateTime());
			bmsXcjlList.forEach(xcjl -> {
				BmsxcjlLbItemVO itemVO = new BmsxcjlLbItemVO();
				itemVO.setBmsbh(xcjl.getBmsbh());
				itemVO.setZskssj(xcjl.getZskssj());
				itemVO.setZsjssj(xcjl.getZsjssj());
				itemVO.setSfmz4ryszg(StringUtils.equals(xcjl.getSfmz4ryszg(),"1") ? "1" : "2" );
				itemVO.setSfygawjzg(StringUtils.equals(xcjl.getSfygawjzg(),"1") ? "1" : "2" );
				itemVO.setBmgftsfzc(StringUtils.equals(xcjl.getBmgftsfzc(),"1") ? "1" : "2" );
				itemVO.setXcjg(StringUtils.equals(xcjl.getXcjg(),"1") ? "1" : "2" );
				itemVO.setXcbz(xcjl.getXcbz());
				itemVO.setRyxm(xcjl.getRyxm());
				itemVO.setCreate_time(DateUtil.format(xcjl.getCreateTime(), "yyyyMMdd HHmmss"));
				itemVO.setXcjlbh(xcjl.getXcjlbh());
				list.add(itemVO);
			});
		}

		result.setBmsxcjlLb(list);
		logger.info("bmsxcjlb - 保密室推送数据查询-1.巡查记录表. [OK] BmsxcjlbVO={}", result);
		return WrapMapper.ok(result);
	}
    /** 
     * @see com.xcwlkj.bmsjk.service.BmstssjcxSysFeignApi#bmssphfjlb(com.xcwlkj.bmsjk.model.dto.bmstssjcx.BmssphfjlbDTO)
     */
	@Override
	public Wrapper<BmssphfjlbVO> bmssphfjlb(@RequestBody @Validated BmssphfjlbDTO dto) {
		logger.info("保密室推送数据查询-2..视频回放记录表BmssphfjlbDTO={}", dto);
		BmssphfjlbVO result = new BmssphfjlbVO();
		List<BmssphfjlbLbItemVO> list = new ArrayList<>();

		PushMessageTemp tempTable = pushMessageTempService.selectByKey(DataBaseEnum.SPHFJLB.getCode());

		Example sphfExam = new Example(BmsSphfjlsb.class);
		Example.Criteria sphfCri = sphfExam.createCriteria()
				.andEqualTo("sczt", ScztEnum.NOTDEL.getCode())
				.andIsNotNull("createTime");

		if (!BeanUtil.isEmpty(tempTable)){//非空 查询同步时间之后的
			Date synchroTime = tempTable.getSynchroTime();
			sphfCri.andGreaterThan("createTime",synchroTime);
		}

		sphfExam.setOrderByClause(" create_time DESC");
		List<BmsSphfjlsb> bmsSphfjlsbList = sphfjlsbService.selectByExample(sphfExam);
		if (CollectionUtil.isNotEmpty(bmsSphfjlsbList)) {
			//更新时间为数据的最近一条
			pushMessageTempService.updateSychroTime(DataBaseEnum.SPHFJLB.getCode(),bmsSphfjlsbList.get(0).getCreateTime());
			bmsSphfjlsbList.forEach(sphf -> {
				BmssphfjlbLbItemVO itemVO = new BmssphfjlbLbItemVO();
				BeanUtil.copyProperties(sphf,itemVO);
				list.add(itemVO);
			});
		}

		result.setBmssphfjlbLb(list);
		logger.info("bmssphfjlb - 保密室推送数据查询-2..视频回放记录表. [OK] BmssphfjlbVO={}", result);
		return WrapMapper.ok(result);
	}
}



