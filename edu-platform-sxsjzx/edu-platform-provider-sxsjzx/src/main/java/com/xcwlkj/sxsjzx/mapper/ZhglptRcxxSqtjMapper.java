/**
 * xcwlkj.com Inc.
 * Copyright (c) 2021-2031 All Rights Reserved.
 */
package com.xcwlkj.sxsjzx.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.sxsjzx.model.domain.ZhglptRcxxSqtj;



/**
 * zhglpt_rcxx_sqtj数据库操作
 * <AUTHOR>
 * @version $Id: InitZhglptRcxxSqtjMapper.java, v 0.1 2021年06月04日 23时29分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface ZhglptRcxxSqtjMapper extends MyMapper<ZhglptRcxxSqtj> {

    /**
	 * 分页查询zhglpt_rcxx_sqtj
	 * 
	 * @param example
	 * @return
	 */
	List<ZhglptRcxxSqtj> pageList(ZhglptRcxxSqtj example);
}
