/**
 * xcwlkj.com Inc
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sxsjzx.web;

import com.xcwlkj.base.BaseFeignClient;
import com.xcwlkj.model.enums.SblxEnum;
import com.xcwlkj.sxsjzx.model.domain.SfrzSn;
import com.xcwlkj.sxsjzx.model.domain.ZbfkSn;
import com.xcwlkj.sxsjzx.model.dto.sbsj.TbscsbsjDTO;
import com.xcwlkj.sxsjzx.model.dto.sbsj.TbtjsbsjDTO;
import com.xcwlkj.sxsjzx.model.dto.sbsj.TbxgsbsjDTO;
import com.xcwlkj.sxsjzx.service.SbsjSysFeignApi;
import com.xcwlkj.sxsjzx.service.SfrzSnService;
import com.xcwlkj.sxsjzx.service.ZbfkSnService;
import com.xcwlkj.util.wrapper.WrapMapper;
import com.xcwlkj.util.wrapper.Wrapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Sbsj接口
 * <AUTHOR>
 * @version $Id: SbsjSysFeignClient.java, v 0.1 2020年12月15日 17时14分 xcwlkj.com Exp $
 */
@RestController
public class SbsjSysFeignClient extends BaseFeignClient implements SbsjSysFeignApi{

	@Autowired
	private SfrzSnService sfrzSnService;
	@Resource
	private ZbfkSnService zbfkSnService;
	@Autowired
    private TransactionTemplate transactionTemplate;
	
	//身份认证设备类型
	private final static List<String> SFRZSB_LIST = Arrays.asList(SblxEnum.SFYZSB.getCode(),SblxEnum.SFYZPT.getCode(),SblxEnum.RLSBRZSB.getCode(),SblxEnum.RZHYRZSB.getCode());
	//作弊防控设备类型
	private final static List<String> ZBFKSB_LIST = Arrays.asList(SblxEnum.ZBFKSB.getCode());
	
	private final String sfrzKsmc = "2022年研究生考试"; //"身份认证系统数据对接";
	private final String zbfkKsmc = "2022年研究生考试"; //"作弊防控系统数据对接";
	

    /** 
     * @see com.xcwlkj.sxsjzx.service.SbsjSysFeignApi#tbtjsbsj(com.xcwlkj.sxsjzx.model.dto.sbsj.TbtjsbsjDTO)
     */
	@Override
	public Wrapper<Void> tbtjsbsj(@RequestBody @Validated TbtjsbsjDTO dto) {
		logger.info("【设备数据】-同步添加设备数据TbtjsbsjDTO={}", dto);
		
		if (SFRZSB_LIST.contains(dto.getSblx())) {
			SfrzSn sfrzSn = new SfrzSn();
			BeanUtils.copyProperties(dto, sfrzSn);
			sfrzSn.setKsmc(sfrzKsmc);
			if(StringUtils.isNotBlank(sfrzSn.getKddm())) sfrzSn.setSfys("1");
			sfrzSnService.insertSelective(sfrzSn);
		}
		if (ZBFKSB_LIST.contains(dto.getSblx())) {
			ZbfkSn zbfkSn = new ZbfkSn();
			BeanUtils.copyProperties(dto, zbfkSn);
			zbfkSn.setKsmc(zbfkKsmc);
            if(StringUtils.isNotBlank(zbfkSn.getKddm())) zbfkSn.setSfys("1");
			zbfkSnService.insertSelective(zbfkSn);
		}
		
		logger.info("tbtjsbsj - 【设备数据】-同步添加设备数据. [OK] ");
		return WrapMapper.ok();
	}
    /** 
     * @see com.xcwlkj.sxsjzx.service.SbsjSysFeignApi#tbxgsbsj(com.xcwlkj.sxsjzx.model.dto.sbsj.TbxgsbsjDTO)
     */
	@Override
	public Wrapper<Void> tbxgsbsj(@RequestBody @Validated TbxgsbsjDTO dto) {
		logger.info("【设备数据】-同步修改设备数据TbxgsbsjDTO={}", dto);
		
		if (SFRZSB_LIST.contains(dto.getSblx())) {
			transactionTemplate.execute(new TransactionCallbackWithoutResult() {
	            @Override
	            protected void doInTransactionWithoutResult(TransactionStatus status) {
	            	//删除旧的
	            	sbsjsc(dto.getOldsn());
	    			
	    			//添加新的
	    			SfrzSn sfrzSn = new SfrzSn();
	    			BeanUtils.copyProperties(dto, sfrzSn);
	    			sfrzSn.setKsmc(sfrzKsmc);
                    if(StringUtils.isNotBlank(sfrzSn.getKddm())) sfrzSn.setSfys("1");
	    			sfrzSnService.insertSelective(sfrzSn);
	            }
	        });
		}
		
		if (ZBFKSB_LIST.contains(dto.getSblx())) {
			transactionTemplate.execute(new TransactionCallbackWithoutResult() {
	            @Override
	            protected void doInTransactionWithoutResult(TransactionStatus status) {
					//删除旧的
	            	sbsjsc(dto.getOldsn());
					
					//添加新的
					ZbfkSn zbfkSn = new ZbfkSn();
					BeanUtils.copyProperties(dto, zbfkSn);
					zbfkSn.setKsmc(zbfkKsmc);
                    if(StringUtils.isNotBlank(zbfkSn.getKddm())) zbfkSn.setSfys("1");
					zbfkSnService.insertSelective(zbfkSn);
	            }
	        });
		}
		logger.info("tbxgsbsj - 【设备数据】-同步修改设备数据. [OK] ");
		return WrapMapper.ok();
	}
    /** 
     * @see com.xcwlkj.sxsjzx.service.SbsjSysFeignApi#tbscsbsj(com.xcwlkj.sxsjzx.model.dto.sbsj.TbscsbsjDTO)
     */
	@Override
	public Wrapper<Void> tbscsbsj(@RequestBody @Validated TbscsbsjDTO dto) {
		logger.info("【设备数据】-同步删除设备数据TbscsbsjDTO={}", dto);
		sbsjsc(dto.getSn());
		logger.info("tbscsbsj - 【设备数据】-同步删除设备数据. [OK] ");
		return WrapMapper.ok();
	}
	
	/**
	 * [根据考点代码、sn删除数据中心相应的设备]
	 * @date 2020年12月25日 上午11:29:58
	 * <AUTHOR>
	 * @version 1.0
	 * @param sn
	 */
	public void sbsjsc(String sn){
		Example sfrzef = new Example(SfrzSn.class);
		sfrzef.createCriteria().andEqualTo("sn",sn);
		sfrzSnService.deleteByExample(sfrzef);
		
		Example zbfkef = new Example(ZbfkSn.class);
		zbfkef.createCriteria().andEqualTo("sn",sn);
		zbfkSnService.deleteByExample(zbfkef);
	}
}



