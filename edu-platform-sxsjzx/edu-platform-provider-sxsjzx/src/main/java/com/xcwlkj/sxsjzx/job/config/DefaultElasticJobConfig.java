package com.xcwlkj.sxsjzx.job.config;

import com.dangdang.ddframe.job.config.JobCoreConfiguration;
import com.dangdang.ddframe.job.config.simple.SimpleJobConfiguration;
import com.dangdang.ddframe.job.event.JobEventConfiguration;
import com.dangdang.ddframe.job.lite.config.LiteJobConfiguration;
import com.dangdang.ddframe.job.lite.spring.api.SpringJobScheduler;
import com.dangdang.ddframe.job.reg.zookeeper.ZookeeperRegistryCenter;
import com.xcwlkj.sxsjzx.job.center.defaultjob.SfrzjgJob;
import com.xcwlkj.sxsjzx.job.center.defaultjob.SfrzjgKqtjJob;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 默认定时任务初始化
 * 
 * <AUTHOR>
 * @version $Id: DefaultElasticJobConfig.java, v 0.1 May 29, 2020 5:01:39 PM White Wolf Exp $
 */
@Configuration
public class DefaultElasticJobConfig {

    /**
     * 注册中心
     */
    @Resource
    private ZookeeperRegistryCenter regCenter;

    /**
     * job事件配置
     */
    @Resource
    private JobEventConfiguration   jobEventConfiguration;
    
    @Resource
    private SfrzjgJob sfrzjgJob;
    
    @Resource
    private SfrzjgKqtjJob sfrzjgKqtjJob;
    
    /**
     * 初始化 考点-考生入场统计job
     * 
     * @param cron
     * @param shardingTotalCount
     * @param shardingItemParameters
     * @return
     */
    @Bean(initMethod = "init")
    public SpringJobScheduler sfrzjgJobInit(@Value("${elasticjob.sfrzjgJob.cron}") final String cron,
                                                    @Value("${elasticjob.sfrzjgJob.shardingTotalCount}") final int shardingTotalCount,
                                                    @Value("${elasticjob.sfrzjgJob.shardingItemParameters}") final String shardingItemParameters,
                                                    @Value("${elasticjob.sfrzjgJob.jobName}")final String jobName) {

        return new SpringJobScheduler(sfrzjgJob, regCenter,
            getLiteJobConfiguration(sfrzjgJob.getClass(), cron, shardingTotalCount,
                shardingItemParameters,jobName),
            jobEventConfiguration);
    }
    
    /**
     * [考区-考生入场增量统计]
     * @date 2021年6月4日 下午11:49:13
     * <AUTHOR>
     * @version 1.0
     * @param cron
     * @param shardingTotalCount
     * @param shardingItemParameters
     * @param jobName
     * @return
     */
    @Bean(initMethod = "init")
    public SpringJobScheduler sfrzjgKqtjJobInit(@Value("${elasticjob.sfrzjgKqtjJob.cron}") final String cron,
                                                    @Value("${elasticjob.sfrzjgKqtjJob.shardingTotalCount}") final int shardingTotalCount,
                                                    @Value("${elasticjob.sfrzjgKqtjJob.shardingItemParameters}") final String shardingItemParameters,
                                                    @Value("${elasticjob.sfrzjgKqtjJob.jobName}")final String jobName) {

        return new SpringJobScheduler(sfrzjgKqtjJob, regCenter,
            getLiteJobConfiguration(sfrzjgKqtjJob.getClass(), cron, shardingTotalCount,
                shardingItemParameters,jobName),
            jobEventConfiguration);
    }


    /**
    *
    * @param jobClass 任务调度类
    * @param cron 定时任务cron配置
    * @param shardingTotalCount 任务分片数
    * @param shardingItemParameters 任务分片参数
    * @return LiteJobConfiguration 任务配置
    */
    private LiteJobConfiguration getLiteJobConfiguration(final Class<? extends com.dangdang.ddframe.job.api.simple.SimpleJob> jobClass,
                                                         final String cron,
                                                         final int shardingTotalCount,
                                                         final String shardingItemParameters,
                                                         final String jobName) {
        return LiteJobConfiguration.newBuilder(new SimpleJobConfiguration(
            JobCoreConfiguration.newBuilder(jobName, cron, shardingTotalCount)
                .shardingItemParameters(shardingItemParameters).build(),
            jobClass.getCanonicalName())).overwrite(true).build();
    }
}
