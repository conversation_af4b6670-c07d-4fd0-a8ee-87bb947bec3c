/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sxsjzx.model.vo.sfhypt;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【身份验证】-【验收】-获取身份验证结果vo
 * <AUTHOR>
 * @version $Id: SfyzjgItemVO.java, v 0.1 2020年12月18日 11时22分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SfyzjgItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 证件号码 */
    private String zjhm;
    /** SN */
    private String devsn;
    /** 指纹匹配率 */
    private String zwppl;
    /** 人脸匹配率 */
    private String rlppl;
    /** 人脸照片 */
    private String rlzp;
    /** 指纹图片 */
    private String zwtp;
    /** 身份证照片 */
    private String sfzzp;
    /** 考点代码 */
    private String kddm;
    /** 认证时间 */
    private String rzsj;
    /** 认证结果 */
    private String rzjg;
    /** 考试名称 */
    private String ksmc;
    /** 认证方式 */
    private String rzfs;
    /** 上传时间 */
    private String scsj;

}
