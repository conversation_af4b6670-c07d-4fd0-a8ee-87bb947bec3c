/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sxsjzx.model.vo.sxfzjc;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 7、省市县考生科类统计vo
 * <AUTHOR>
 * @version $Id: SsxklItemVO.java, v 0.1 2020年11月06日 17时19分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SsxklItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 科类名称 */
    private String klmc;
    /** 考生数量 */
    private Integer kssl;

}
