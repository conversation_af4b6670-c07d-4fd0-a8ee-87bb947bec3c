<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.sbyw.mapper.ZdxjLxjcqsmxMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.sbyw.model.domain.ZdxjLxjcqsmx">
        <id column="lxjcqsbh" jdbcType="VARCHAR" property="lxjcqsbh" />
        <result column="jcjgmxbh" jdbcType="VARCHAR" property="jcjgmxbh" />
        <result column="sipdz" jdbcType="VARCHAR" property="sipdz" />
        <result column="qsqssj" jdbcType="TIMESTAMP" property="qsqssj" />
        <result column="qszzsj" jdbcType="TIMESTAMP" property="qszzsj" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        lxjcqsbh,
        jcjgmxbh,
        sipdz,
        qsqssj,
        qszzsj

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="lxjcqsbh != null and lxjcqsbh != ''">
            AND lxjcqsbh = #{lxjcqsbh,jdbcType=VARCHAR}
        </if>
        <if test="jcjgmxbh != null and jcjgmxbh != ''">
            AND jcjgmxbh = #{jcjgmxbh,jdbcType=VARCHAR}
        </if>
        <if test="sipdz != null and sipdz != ''">
            AND sipdz = #{sipdz,jdbcType=VARCHAR}
        </if>
        <if test="qsqssj != null and qsqssj != ''">
            AND qsqssj = #{qsqssj,jdbcType=TIMESTAMP}
        </if>
        <if test="qszzsj != null and qszzsj != ''">
            AND qszzsj = #{qszzsj,jdbcType=TIMESTAMP}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="lxjcqsbh != null ">
            lxjcqsbh = #{lxjcqsbh,jdbcType=VARCHAR},
        </if>
        <if test="jcjgmxbh != null ">
            jcjgmxbh = #{jcjgmxbh,jdbcType=VARCHAR},
        </if>
        <if test="sipdz != null ">
            sipdz = #{sipdz,jdbcType=VARCHAR},
        </if>
        <if test="qsqssj != null ">
            qsqssj = #{qsqssj,jdbcType=TIMESTAMP},
        </if>
        <if test="qszzsj != null ">
            qszzsj = #{qszzsj,jdbcType=TIMESTAMP}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.sbyw.model.domain.ZdxjLxjcqsmx"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from zdxj_lxjcqsmx
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>

    <select id="getQslbBySbbhAndsj" parameterType="map" resultMap="BaseResultMap">
        select lxjcqsmx.sipdz,lxjcqsmx.qsqssj,lxjcqsmx.qszzsj
            from zdxj_jcjgmx jcjgmx
            join zdxj_lxjcqsmx lxjcqsmx
            on jcjgmx.jcjgmxbh = lxjcqsmx.jcjgmxbh
        <where>
            jcjgmx.sbbh = #{sbbh}
            and  lxjcqsmx.qsqssj >=#{kssj}
          <![CDATA[ and lxjcqsmx.qsqssj < #{jssj}]]>
        </where>
    </select>

    <select id="getQslbBySipdzAndsj" parameterType="map" resultMap="BaseResultMap">
        select lxjcqsmx.sipdz,lxjcqsmx.qsqssj,lxjcqsmx.qszzsj
        from zdxj_jcjgmx jcjgmx
        join zdxj_lxjcqsmx lxjcqsmx
        on jcjgmx.jcjgmxbh = lxjcqsmx.jcjgmxbh
        <where>
            jcjgmx.sipdz = #{sipdz}
            and  lxjcqsmx.qsqssj >=#{kssj}
            <![CDATA[ and lxjcqsmx.qsqssj < #{jssj}]]>
        </where>
    </select>

    <insert id="insertZdxjLxjcqsmxList" parameterType="map">
        insert into zdxj_lxjcqsmx (lxjcqsbh, jcjgmxbh, sipdz, qsqssj, qszzsj)
        values
        <foreach collection="zdxjLxjcqsmxes" separator="," item="item">
            (#{item.lxjcqsbh},#{item.jcjgmxbh},#{item.sipdz},#{item.qsqssj},#{item.qszzsj})
        </foreach>
    </insert>
</mapper>
