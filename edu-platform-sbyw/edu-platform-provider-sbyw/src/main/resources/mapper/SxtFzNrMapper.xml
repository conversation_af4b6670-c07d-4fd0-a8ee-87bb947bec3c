<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xcwlkj.sbyw.mapper.SxtFzNrMapper">
	<resultMap id="BaseResultMap" type="com.xcwlkj.sbyw.model.domain.SxtFzNr">
        <id column="fznrbh" jdbcType="VARCHAR" property="fznrbh" />
        <result column="zxxbh" jdbcType="VARCHAR" property="zxxbh" />
        <result column="sbbh" jdbcType="VARCHAR" property="sbbh" />
        <result column="sipdz" jdbcType="VARCHAR" property="sipdz" />
        <result column="sbcsbh" jdbcType="VARCHAR" property="sbcsbh" />
        <result column="sbcsmc" jdbcType="VARCHAR" property="sbcsmc" />

	</resultMap>
	<!-- 列信息 -->
	<sql id="Base_Column_List">
        fznrbh,
        zxxbh,
        sbbh,
        sipdz,
        sbcsbh,
        sbcsmc

	</sql>

	<!-- where条件 -->
	<sql id="Base_Where_Condition">
        <if test="fznrbh != null and fznrbh != ''">
            AND fznrbh = #{fznrbh,jdbcType=VARCHAR}
        </if>
        <if test="zxxbh != null and zxxbh != ''">
            AND zxxbh = #{zxxbh,jdbcType=VARCHAR}
        </if>
        <if test="sbbh != null and sbbh != ''">
            AND sbbh = #{sbbh,jdbcType=VARCHAR}
        </if>
        <if test="sipdz != null and sipdz != ''">
            AND sipdz = #{sipdz,jdbcType=VARCHAR}
        </if>
        <if test="sbcsbh != null and sbcsbh != ''">
            AND sbcsbh = #{sbcsbh,jdbcType=VARCHAR}
        </if>
        <if test="sbcsmc != null and sbcsmc != ''">
            AND sbcsmc = #{sbcsmc,jdbcType=VARCHAR}
        </if>

	</sql>

	<!-- order by条件 -->
	<sql id="Base_OrderBy_Condition">
		<if test="orderBy != null and orderBy !=''">
			ORDER BY ${orderBy}
		</if>
	</sql>

	<!-- update条件 -->
	<sql id="Base_Set_Condition">
	<set>
        <if test="fznrbh != null ">
            fznrbh = #{fznrbh,jdbcType=VARCHAR},
        </if>
        <if test="zxxbh != null ">
            zxxbh = #{zxxbh,jdbcType=VARCHAR},
        </if>
        <if test="sbbh != null ">
            sbbh = #{sbbh,jdbcType=VARCHAR},
        </if>
        <if test="sipdz != null ">
            sipdz = #{sipdz,jdbcType=VARCHAR},
        </if>
        <if test="sbcsbh != null ">
            sbcsbh = #{sbcsbh,jdbcType=VARCHAR},
        </if>
        <if test="sbcsmc != null ">
            sbcsmc = #{sbcsmc,jdbcType=VARCHAR}
        </if>

	</set>
	</sql>

	<!-- 分页查询 -->
	<select id="pageList" parameterType="com.xcwlkj.sbyw.model.domain.SxtFzNr"
		resultMap="BaseResultMap">
		select
		<include refid="Base_Column_List" />
		from sxt_fz_nr
		where 1=1 
		<include refid="Base_Where_Condition" />
		<include refid="Base_OrderBy_Condition" />
	</select>
</mapper>
