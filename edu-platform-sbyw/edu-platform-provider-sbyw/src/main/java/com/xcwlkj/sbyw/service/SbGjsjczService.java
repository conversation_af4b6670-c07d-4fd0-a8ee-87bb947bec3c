/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.service;

import com.xcwlkj.sbyw.model.domain.SbGjsjcz;
import org.springframework.stereotype.Service;

import com.xcwlkj.sbyw.model.dto.gjgl.GjsjjgmxDTO;
import com.xcwlkj.sbyw.model.vo.gjgl.GjsjjgmxVO;
import java.util.List;


/**
 * 设备固件升级操作服务
 * <AUTHOR>
 * @version $Id: SbGjsjczService.java, v 0.1 2020年06月03日 11时31分 xcwlkj.com Exp $
 */
@Service
public interface SbGjsjczService  {

    /**
     * 批量插入数据
     * @param sbGjsjczs 设备固件升级操作列表
     * @return
     */
    int insertList(List<SbGjsjcz> sbGjsjczs);

    /**
     * 根据批次号查询该批次的升级设备列表
     * @param pch 批次号
     * @param shardingTotalCount 分片总数
     * @param shardingItem 分片数
     * @return 操作列表
     */
    List<SbGjsjcz> selectListBypch(String pch,int shardingTotalCount,int shardingItem);

    /**
     * 根据批次号和升级状态获取总数
     * @param pch
     * @param jgm
     * @return
     */
    int selectCountBypch(String pch, String jgm);

    /**
     * 根据主键批量更新设备状态
     * @param zjlb
     * @param sjzt
     * @return 更新条数
     */
    int updateSjztByzj(List<String> zjlb, String sjzt,String sjjg);
	
	/**
	 * 固件升级结果明细
	 * @param dto
	 * @return
	 */
	GjsjjgmxVO gjsjjgmx(GjsjjgmxDTO dto);
}