/**
 * xcwlkj.com Inc.
 * Copyright (c) 2022-2032 All Rights Reserved.
 */
package com.xcwlkj.sbyw.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.xcwlkj.base.mybatis.MyMapper;
import com.xcwlkj.sbyw.model.domain.SbsbFtpszxx;



/**
 * 设备上报ftp设置信息数据库操作
 * <AUTHOR>
 * @version $Id: InitSbsbFtpszxxMapper.java, v 0.1 2022年01月13日 17时40分 xcwlkj.com Exp $
 */
@Mapper
@Repository
public interface SbsbFtpszxxMapper extends MyMapper<SbsbFtpszxx> {

    /**
	 * 分页查询设备上报ftp设置信息
	 * 
	 * @param example
	 * @return
	 */
	List<SbsbFtpszxx> pageList(SbsbFtpszxx example);
}
