package com.xcwlkj.sbyw.third.channel.vqd.model.req.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class UploadDTO {

    /** 服务器协议,http/ftp */
    @JSONField(name = "Proto")
    private String Proto;

    /** 上传目录 */
    @JSONField(name = "Catalog")
    private String Catalog;

    /** 登录上传服务器的用户名 */
    @JSONField(name = "User")
    private String User;

    /** 登录上传服务器的密码 */
    @JSONField(name = "Pwd")
    private String Pwd;
}
