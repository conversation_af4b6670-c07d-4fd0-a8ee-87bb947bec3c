package com.xcwlkj.sbyw.third.channel.vqd.model.resp.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class VqdResultResp {

    @JSONField(name = "TaskID")
    private String TaskID;

    @J<PERSON>NField(name = "VQD")
    private VqdResp VQD;

    @JSONField(name = "Method")
    private String Method;

    @J<PERSON>NField(name = "ID")
    private String ID;

    @JSONField(name = "Result")
    private ResultResp Result;
}
