/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2032 All Rights Reserved.
 */
package com.xcwlkj.sbyw.service.impl;

import com.xcwlkj.sbyw.model.domain.SbsbCzjlLog;
import org.springframework.stereotype.Service;

import com.xcwlkj.sbyw.mapper.SbsbCzjlLogMapper;
import com.xcwlkj.sbyw.service.SbsbCzjlLogService;
import javax.annotation.Resource;


/**
 * 设备上报操作记录日志服务
 * <AUTHOR>
 * @version $Id: SbsbCzjlLogServiceImpl.java, v 0.1 2022年01月13日 17时37分 xcwlkj.com Exp $
 */
@Service("sbsbCzjlLogService")
public class SbsbCzjlLogServiceImpl  implements SbsbCzjlLogService  {

    @Resource
    private SbsbCzjlLogMapper modelMapper;

    @Override
    public void insertSelective(SbsbCzjlLog sbsbCzjlLog) {
        modelMapper.insertSelective(sbsbCzjlLog);
    }
}