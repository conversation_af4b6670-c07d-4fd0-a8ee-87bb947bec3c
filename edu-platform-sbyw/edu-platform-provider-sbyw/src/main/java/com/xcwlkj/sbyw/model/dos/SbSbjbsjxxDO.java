/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dos;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;


/**
 * 在线状态统计DO
 * <AUTHOR>
 * @version $Id: CxsblbVO.java, v 0.1 2020年05月15日 15时03分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbSbjbsjxxDO implements Serializable {

    /** 序列ID */
    private static final long serialVersionUID = 1L;

    /** 变化的字段名称 */
    private String fieldName;
    /** 原有数据 */
    private String yysj;
    /** 本次数据 */
    private String bcsj;

}
