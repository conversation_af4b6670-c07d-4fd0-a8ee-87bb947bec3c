/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.service.impl;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.xcwlkj.sbyw.model.dto.gjgl.*;
import com.xcwlkj.sbyw.model.vo.gjgl.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.multipart.MultipartFile;

import com.gexin.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.JsonObject;
import com.xcwlkj.core.sso.user.XcSsoUser;
import com.xcwlkj.dfs.model.vo.DownloadVO;
import com.xcwlkj.dfs.model.vo.TimePathVO;
import com.xcwlkj.dfs.model.vo.UploadVO;
import com.xcwlkj.dfs.util.XcDfsClient;
import com.xcwlkj.model.enums.SblxEnum;
import com.xcwlkj.model.enums.ValidEnum;
import com.xcwlkj.sbyw.exceptions.SbywBusiException;
import com.xcwlkj.sbyw.job.model.enums.JobServiceEnum;
import com.xcwlkj.sbyw.job.model.enums.ScheduleTaskEnums;
import com.xcwlkj.sbyw.mapper.SbGjbxxMapper;
import com.xcwlkj.sbyw.model.domain.SbGjbxx;
import com.xcwlkj.sbyw.model.domain.SbGjsjcz;
import com.xcwlkj.sbyw.model.domain.SbGjsjczzl;
import com.xcwlkj.sbyw.model.domain.SbXxb;
import com.xcwlkj.sbyw.model.dos.ZcsblbDO;
import com.xcwlkj.sbyw.model.enums.GjbztEnum;
import com.xcwlkj.sbyw.model.enums.GjsjjgEnum;
import com.xcwlkj.sbyw.model.enums.GjsjpcztEnum;
import com.xcwlkj.sbyw.service.SbGjbxxService;
import com.xcwlkj.sbyw.service.SbGjsjczService;
import com.xcwlkj.sbyw.service.SbGjsjczzlService;
import com.xcwlkj.sbyw.service.SbXxbService;
import com.xcwlkj.sbyw.service.TaskDefineService;
import com.xcwlkj.sbyw.third.channel.sip.model.req.GjsjCatalogDeviceReq;
import com.xcwlkj.sbyw.third.channel.sip.model.req.model.GjCatalogDevice;
import com.xcwlkj.sbyw.third.channel.sip.model.req.model.GjDevice;
import com.xcwlkj.sbyw.third.channel.sip.model.resp.GjsjCatalogDeviceResp;
import com.xcwlkj.sbyw.third.channel.sip.service.SipServiceGeneralService;
import com.xcwlkj.util.DateUtil;
import com.xcwlkj.util.IdGenerateUtil;
import com.xcwlkj.util.RequestUtil;
import com.xcwlkj.util.StringUtil;
import com.xcwlkj.utils.UnZipTgzFileUtil;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;


/**
 * 固件包信息服务
 * <AUTHOR>
 * @version $Id: SbGjbxxServiceImpl.java, v 0.1 2020年06月01日 20时15分 xcwlkj.com Exp $
 */
@Slf4j
@Service("sbGjbxxService")
public class SbGjbxxServiceImpl  implements SbGjbxxService  {

    @Resource
    private SbGjbxxMapper modelMapper;

	@Autowired
	private SipServiceGeneralService sipServiceGeneralService;

	@Autowired
	private SbXxbService sbXxbService;

	@Autowired
	private SbGjsjczService sbGjsjczService;

	@Autowired
	private SbGjsjczzlService sbGjsjczzlService;


	@Autowired
	private TaskDefineService taskDefineService;

	@Value("${xc.temp.path}")
	private String localPath;

//	@Value("${gjsj.gateway.sip:sip:httpgw@cnjy:9902}")
    @Value("${gjsj.gateway.sip:sip:<EMAIL>:9902}")
	private String gateWaySip;

	private static final String  FILE_SUFFIX = "tgz";
	private static final String  VERSION_TXT_FILE_NAME = "app_version.txt";


    /** 
     * @see com.xcwlkj.sbyw.service.SbGjbxxService#gjbsc(MultipartFile,String,String)
     */
	@Override
	public GjbscVO gjbsc(MultipartFile file, String gjbm, String sblx) {
		GjbscVO gjbscVO = new GjbscVO();
		Map<String,String> versionMap = new HashMap<>();
		String path = localPath+ DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT)+ File.separator;
		String fileName = file.getOriginalFilename();
		if (!fileName.endsWith(FILE_SUFFIX)){
			throw new SbywBusiException("上传的文件不是tgz文件，请检查");
		}
		File dirFile = new File(path);
		dirFile.mkdirs();
		File localFile = new File(path+fileName);
		String rootPath = "";
		try {
			//保存上传文件到本地
			file.transferTo(localFile);
			//解压文件
			rootPath = UnZipTgzFileUtil.deCompressTGZFile(localFile.getCanonicalPath());
			if (StringUtil.isBlank(rootPath)) {
				throw new SbywBusiException("解压固件包失败，请检查固件包是否正确");
			}
			//获取version.txt的文件路径
			String versionTxtPath = UnZipTgzFileUtil.exist(rootPath,VERSION_TXT_FILE_NAME);
			if (StringUtil.isBlank(versionTxtPath)) {
				throw new SbywBusiException("固件包内找不到"+VERSION_TXT_FILE_NAME+"文件信息");
			}
			//读取配置文件内容，把数据放到versionMap中
			BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(new File(versionTxtPath)),
					"GBK"));
			String lineTxt = null;
			while ((lineTxt = br.readLine()) != null) {
				if (StringUtil.isBlank(lineTxt)){
					continue;
				}
				String[] lineString = lineTxt.split("=");
				if (lineString.length > 1) {
					versionMap.put(lineString[0],lineString[1]);
				}
			}
			br.close();
			if (StringUtil.isBlank(versionMap.get("FirmwareVer"))) {
				throw new SbywBusiException(VERSION_TXT_FILE_NAME+"中FirmwareVer不能为空");
			}
			Example example = new Example(SbGjbxx.class);
			example.createCriteria().andEqualTo("sbxh",versionMap.get("Target"))
					.andEqualTo("firmwarebb",versionMap.get("FirmwareVer"));
			List<SbGjbxx> sbGjbxxes = modelMapper.selectByExample(example);
			if (!CollectionUtils.isEmpty(sbGjbxxes)){
				throw new SbywBusiException("固件包已存在，不能重复上传");
			}
			//上传文件到fastDFS
			log.info("localFile.getCanonicalPath()    " + localFile.getCanonicalPath() );
			UploadVO uploadVO = XcDfsClient.uploadStream(localFile.getCanonicalPath(),0,"private");
			//文件信息落库
			FileInputStream fin = new FileInputStream(localFile);

			SbGjbxx sbGjbxx = new SbGjbxx();
			sbGjbxx.setGjbxxbh(IdGenerateUtil.generateId());
			sbGjbxx.setGjbmc(gjbm);
			sbGjbxx.setSblx(sblx);
			sbGjbxx.setSbxh(versionMap.get("Target"));
			sbGjbxx.setFirmwarebb(versionMap.get("FirmwareVer"));
			sbGjbxx.setMd5zy(DigestUtils.md5Hex(fin));
			sbGjbxx.setGjbdx(String.valueOf(localFile.length()));
			sbGjbxx.setWjccbh(uploadVO.getList().get(0).getId());
			sbGjbxx.setWjzt(GjbztEnum.CSH.getCode());
			XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
			sbGjbxx.setCzr(xcSsoUser.getUserName());
			modelMapper.insertSelective(sbGjbxx);
			fin.close();
			//组装返回参数
			gjbscVO.setSbxh(sbGjbxx.getSbxh());
			gjbscVO.setFirmwarebb(sbGjbxx.getFirmwarebb());
			gjbscVO.setMd5zy(sbGjbxx.getMd5zy());
			gjbscVO.setGjbbh(sbGjbxx.getGjbxxbh());
			gjbscVO.setWjdx(sbGjbxx.getGjbdx());
		} catch (IOException e) {
			log.error(e.getMessage(),e);
			throw new SbywBusiException("保存文件失败");
		}  finally {
			//删除本地所有临时文件
			FileSystemUtils.deleteRecursively(dirFile);
		}
		return gjbscVO;
	}
    /** 
     * @see com.xcwlkj.sbyw.service.SbGjbxxService#gjbbcqx(com.xcwlkj.sbyw.model.dto.gjgl.GjbbcqxDTO)
     */
	@Override
	public void gjbbcqx(GjbbcqxDTO dto) {
		String cz = dto.getCz();
		SbGjbxx sbGjbxx = new SbGjbxx();
		sbGjbxx.setGjbxxbh(dto.getGjbbh());
		//根据操作不同执行不同动作 VALID表示保存操作就是更新状态
		if (ValidEnum.VALID.getCode().equalsIgnoreCase(cz)) {
			sbGjbxx.setWjzt(GjbztEnum.YX.getCode());
			sbGjbxx.setGjbmc(dto.getGjbm());
			sbGjbxx.setSblx(dto.getSblx());
			modelMapper.updateByPrimaryKeySelective(sbGjbxx);
		}
		if (ValidEnum.INVALID.getCode().equalsIgnoreCase(cz)){
			modelMapper.deleteByPrimaryKey(dto.getGjbbh());
		}
	}
    /** 
     * @see com.xcwlkj.sbyw.service.SbGjbxxService#gjblb(com.xcwlkj.sbyw.model.dto.gjgl.GjblbDTO)
     */
	@Override
	public GjblbVO gjblb(GjblbDTO dto) {
		GjblbVO gjblbVO = new GjblbVO();
		gjblbVO.setGjbxxlb(new ArrayList<>());
		Example example = new Example(SbGjbxx.class);
		Example.Criteria criteria =example.createCriteria();
		if (StringUtil.isNotBlank(dto.getGjbmc())) {
			criteria.andLike("gjbmc","%"+dto.getGjbmc()+"%");
		}
		if (StringUtil.isNotBlank(dto.getSblx())) {
			criteria.andEqualTo("sblx",dto.getSblx());
		}
		if (StringUtil.isNotBlank(dto.getSbxh())) {
			criteria.andLike("sbxh","%"+dto.getSbxh()+"%");
		}
		if (StringUtil.isNotBlank(dto.getFirmwarebb())) {
			criteria.andLike("firmwarebb","%"+dto.getFirmwarebb()+"%");
		}
		PageHelper.startPage(dto.getPageNum(),dto.getPageSize());
		List<SbGjbxx> sbGjbxxes = modelMapper.selectByExample(example);
		PageInfo<SbGjbxx> sbGjbxxPageInfo = new PageInfo<>(sbGjbxxes);
		gjblbVO.setTotalRows((int)sbGjbxxPageInfo.getTotal());
		sbGjbxxPageInfo.getList().forEach(sbGjbxx -> {
			GjbxxlbItemVO gjbxxlbItemVO = new GjbxxlbItemVO();
			gjbxxlbItemVO.setGjbmc(sbGjbxx.getGjbmc());
			gjbxxlbItemVO.setGjbxxbh(sbGjbxx.getGjbxxbh());
			gjbxxlbItemVO.setSblx(sbGjbxx.getSblx());
			gjbxxlbItemVO.setSbxh(sbGjbxx.getSbxh());
			gjbxxlbItemVO.setFirmwarebb(sbGjbxx.getFirmwarebb());
			gjbxxlbItemVO.setMd5zy(sbGjbxx.getMd5zy());
			gjbxxlbItemVO.setWjdx(sbGjbxx.getGjbdx());
			gjbxxlbItemVO.setCclj(sbGjbxx.getWjccbh());
			gjblbVO.getGjbxxlb().add(gjbxxlbItemVO);
		});
		return gjblbVO;

	}
    /** 
     * @see com.xcwlkj.sbyw.service.SbGjbxxService#gjbzcsblb(com.xcwlkj.sbyw.model.dto.gjgl.GjbzcsblbDTO)
     */
	@Override
	public GjbzcsblbVO gjbzcsblb(GjbzcsblbDTO dto) {
		GjbzcsblbVO gjbzcsblbVO = new GjbzcsblbVO();
		gjbzcsblbVO.setZcsblb(new ArrayList<>());
		//检查固件包是否存在，获取信息
		SbGjbxx sbGjbxx = modelMapper.selectByPrimaryKey(dto.getGjbbh());
		if (sbGjbxx == null ) {
			throw new SbywBusiException("固件包不存在，请检查固件是否存在");
		}
		Map<String,String> reqMap = new HashMap<>();
//		XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
//		reqMap.put("csbh",xcSsoUser.getDepartmentId());
		reqMap.put("firmwarebb",sbGjbxx.getFirmwarebb());
		reqMap.put("cslx",dto.getCslx());
		reqMap.put("csmc",dto.getCsmc());
		PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
		/*if (dto.getSblx() == null || "".equals(dto.getSblx())) {
			reqMap.put("sblx",sbGjbxx.getSblx());
		}else {
		}*/
		reqMap.put("sblx",dto.getSblx());

		if (dto.getCsbh().startsWith("K")) {
			reqMap.put("bzhkd", dto.getCsbh());
		} else if (dto.getCsbh().startsWith("M")) {
			reqMap.put("bms", dto.getCsbh());
		} else {
			reqMap.put("ksgljgid", dto.getCsbh());
		}

		List<ZcsblbDO> zcsblbDOS = modelMapper.getSblbBygjxx(reqMap);
		PageInfo<ZcsblbDO> zcsblbDOPageInfo = new PageInfo<>(zcsblbDOS);
		gjbzcsblbVO.setTotalRows((int) zcsblbDOPageInfo.getTotal());
		zcsblbDOPageInfo.getList().forEach(zcsblbDO -> {
			ZcsblbItemVO zcsblbItemVO = new ZcsblbItemVO();
			zcsblbItemVO.setSbmc(zcsblbDO.getSbmc());
			zcsblbItemVO.setSbxh(zcsblbDO.getSbxh());
			zcsblbItemVO.setSblx(zcsblbDO.getSblx());
			zcsblbItemVO.setCsmc(zcsblbDO.getCsmc());
			zcsblbItemVO.setCslx(zcsblbDO.getCslx());
			zcsblbItemVO.setSipdz(zcsblbDO.getSipdz());
			zcsblbItemVO.setFjsipdz(zcsblbDO.getFjsipdz());
			gjbzcsblbVO.getZcsblb().add(zcsblbItemVO);
		});
		return gjbzcsblbVO;
	}
    /** 
     * @see com.xcwlkj.sbyw.service.SbGjbxxService#gjsj(com.xcwlkj.sbyw.model.dto.gjgl.GjsjDTO)
     */
	@Override
	public void gjsj(GjsjDTO dto) {
		List<SjsblbItemDTO> sjsblb = dto.getSjsblb();
		String batchNo = IdGenerateUtil.generateId();
		Map<String,List<String>> sbMap = new HashMap<>();
		Map<String,String>  resultMap = new HashMap<>();
 		SbGjbxx sbGjbxx = modelMapper.selectByPrimaryKey(dto.getGjbbh());
		if (sbGjbxx == null) {
			throw new SbywBusiException("固件包编号不正确,查询不到固件包信息,请确认");
		}
		TimePathVO timePathVO = XcDfsClient.timePath(2L, 0, sbGjbxx.getWjccbh());
		DownloadVO downloadVO = XcDfsClient.download(timePathVO.getFileList().get(0).getUrl());
		if (downloadVO == null) {
			throw new SbywBusiException("从服务器下载固件包异常");
		}
		//从FastDfs下载固件包到本地
		String oriFileName = downloadVO.getOriFileName();
		byte[] bytes = downloadVO.getContent();
		String path = localPath+ DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT)+ File.separator;
		new File(path).mkdirs();
		File file = new File(path+oriFileName);
		writeFile(bytes, file);
		//ipc设备下发
		if (SblxEnum.IPC.getCode().equalsIgnoreCase(sbGjbxx.getSblx())) {
			//获取所有父类地址
			Set<String> flsipdzs = sjsblb.stream().map(sjsblbItemDTO -> {
				return sjsblbItemDTO.getFlsipdz();
			}).collect(Collectors.toSet());
			//组装参数
			flsipdzs.forEach(flsipdz -> {
				List<String> sipdzlist = sjsblb.stream().filter(sjsblbItemDTO -> sjsblbItemDTO.getFlsipdz().equals(flsipdz)).collect(Collectors.toList()).stream().map(SjsblbItemDTO::getSipdz).collect(Collectors.toList());
				sbMap.put(flsipdz, sipdzlist);
			});
			//调用固件下发接口
			sbMap.forEach((flsipdz, sipdzlist) -> {
				String result = sendUpgrade(flsipdz, (ArrayList<String>) sipdzlist, path, oriFileName, sbGjbxx);
				resultMap.put(flsipdz, result);
			});
		}else {
			//sip 和nvr 直接下发
			sjsblb.forEach(sjsblbItemDTO -> {
				List<String> sipdzlist = new ArrayList<String>(1);
				sipdzlist.add(sjsblbItemDTO.getSipdz());
				sbMap.put(sjsblbItemDTO.getSipdz(),sipdzlist);
				String result = sendUpgrade(sjsblbItemDTO.getSipdz(), (ArrayList<String>) sipdzlist, path, oriFileName, sbGjbxx);
				resultMap.put(sjsblbItemDTO.getSipdz(), result);
			});
		}
		// 落库留痕
		XcSsoUser xcSsoUser = RequestUtil.getLoginUser();
		String czr = xcSsoUser.getUserName();
		String zt = resultMap.values().stream().anyMatch(s ->GjsjjgEnum.DYSJJKSB.getCode().equalsIgnoreCase(s)) ? GjsjpcztEnum.XFYC.getCode() :  GjsjpcztEnum.XFCG.getCode();
		SbGjsjczzl sbGjsjczzl = new SbGjsjczzl();
		sbGjsjczzl.setSjpch(batchNo);
		sbGjsjczzl.setSblx(sbGjbxx.getSblx());
		sbGjsjczzl.setFirmwarebb(sbGjbxx.getFirmwarebb());
		sbGjsjczzl.setSjsj(DateUtil.getCurrentDT());
		sbGjsjczzl.setZt(zt);
		sbGjsjczzl.setCzr(czr);
		sbGjsjczzlService.insertSelective(sbGjsjczzl);
		sbMap.forEach((flsipdz, sipdzlist) -> {
			List<SbXxb> sbXXbs = sbXxbService.selectSbxxBySipDz(sipdzlist);
			List<SbGjsjcz> sbGjsjczs = sbXXbs.stream().map(sbXxb -> {
				SbGjsjcz sbGjsjcz = new SbGjsjcz();
				sbGjsjcz.setSbgjsjczbh(IdGenerateUtil.generateId());
				sbGjsjcz.setPch(batchNo);
				sbGjsjcz.setSbbh(sbXxb.getSbxxbh());
				sbGjsjcz.setSbmc(sbXxb.getSbmc());
				sbGjsjcz.setSblx(sbGjbxx.getSblx());
				sbGjsjcz.setSipdz(sbXxb.getSipdz());
				sbGjsjcz.setFlsipdz(sbXxb.getFjsipdz());
				sbGjsjcz.setYfirmwarebb(sbXxb.getFirmwarebb());
				sbGjsjcz.setMbfirmwarebb(sbGjbxx.getFirmwarebb());
				sbGjsjcz.setSjjg(GjsjjgEnum.getMsgByCode(resultMap.get(flsipdz)));
				sbGjsjcz.setGjbdz(path + oriFileName);
				sbGjsjcz.setSjzt(resultMap.get(flsipdz));
				sbGjsjcz.setCzr(czr);
				return sbGjsjcz;
			}).collect(Collectors.toList());
			sbGjsjczService.insertList(sbGjsjczs);
		});
		//设置定时任务去检查是否完成升级
		JsonObject taskJson = new JsonObject();
		taskJson.addProperty("pch",batchNo);
		taskDefineService.createTaskDefine(JobServiceEnum.SBBBJX, taskJson.toString(), DateUtil.getCurrentDT(), DateUtil.addHours( DateUtil.getCurrentDT(), 3), true, ScheduleTaskEnums.PERMINUTE, "5");

	}

	@Override
	public GjbwjlbVO gjbwjlb(GjbwjlbDTO dto) {

		SbGjbxx sbGjbxx = modelMapper.selectByPrimaryKey(dto.getGjbbh());
		if (sbGjbxx == null) {
			throw new SbywBusiException("固件包编号不正确,查询不到固件包信息,请确认");
		}
		TimePathVO timePathVO = XcDfsClient.timePath(2L, 0, sbGjbxx.getWjccbh());
		DownloadVO downloadVO = XcDfsClient.download(timePathVO.getFileList().get(0).getUrl());
		if (downloadVO == null) {
			throw new SbywBusiException("从服务器下载固件包异常");
		}
		String oriFileName = downloadVO.getOriFileName();
		byte[] bytes = downloadVO.getContent();
		String path = localPath+ DateUtil.format(new Date(System.currentTimeMillis()), DateUtil.FULL_DATETIMEFORMAT)+ File.separator;
		log.info("下载路径 ：" +path);
		new File(path).mkdirs();
		File file = new File(path+oriFileName);
		log.info("path+oriFileName :" + path+oriFileName);
		writeFile(bytes, file);
		// 解压文件
		String tgzFilePath = UnZipTgzFileUtil.deCompressTGZFile(path + oriFileName);
		log.info("tgzFilePath :"+tgzFilePath);
		String packagePath = tgzFilePath + "/Package";
		log.info("packagePath :"+packagePath);
		List<String> packageFileList = showDir(packagePath);
		GjbwjlbVO gjbwjlbVO = new GjbwjlbVO();
		gjbwjlbVO.setGjbwjList(packageFileList);
		//apps.img 文件需第一个上传
		int appsImgIndex = packageFileList.indexOf("apps.img");
		Collections.swap(packageFileList, appsImgIndex, 0);
		//app_version.txt 最后上传
		int appsVersionIndex = packageFileList.indexOf("app_version.txt");
		Collections.swap(packageFileList, appsVersionIndex, (packageFileList.size() - 1));
		gjbwjlbVO.setGjbwjlj(packagePath);

		return gjbwjlbVO;
	}

	private List<String> showDir(String path) {
		List<String> FileNameList = new ArrayList<String>();
		File file = new File(path);
		if (file.exists()) {
			File[] files = file.listFiles();
			if (null != files) {
				for (int i = 0; i < files.length; i++) {
					if (files[i].isDirectory()) {
						showDir(file.getAbsolutePath());
					} else {
						FileNameList.add(files[i].getName());
					}
				}
			}
		} else {
			log.info("文件不存在！");
		}
		return FileNameList;
	}

	private String sendUpgrade(String flsipdz,ArrayList<String> sblb,String gjblj,String gjbmc,SbGjbxx sbGjbxx) {
		GjsjCatalogDeviceReq req = new GjsjCatalogDeviceReq();
		//父类sip地址
		req.setTo(flsipdz);
		GjCatalogDevice gjDevice = new GjCatalogDevice();
		//写死的
		gjDevice.setCmdType("DeviceUpgrade");
		//唯一编号
		gjDevice.setSn(IdGenerateUtil.generateId());
		//父类sip地址
		gjDevice.setUri(flsipdz);
		//升级设备的sip地址列表
		gjDevice.setDevices(sblb);
		//网关sip
		gjDevice.setFileURI(gateWaySip);
		GjDevice device = new GjDevice();
		//固件包名称
		device.setName(gjbmc);
		//固件包路径
		device.setPath(gjblj);
		//大小
		device.setSize(sbGjbxx.getGjbdx());
		//设备类型
		device.setMatch(sbGjbxx.getSblx().toUpperCase());
		//MD5摘要
		device.setMd5(sbGjbxx.getMd5zy());
		//版本 APPversion
		device.setVersion(sbGjbxx.getFirmwarebb());
		gjDevice.setUpgradeFile(device);
		req.setGjCatalogDevice(gjDevice);
		log.info("GjsjCatalogDeviceReq = {}",req);
		GjsjCatalogDeviceResp resp = sipServiceGeneralService.execute(req, GjsjCatalogDeviceResp.class);
		if (resp == null) {
			return GjsjjgEnum.DYSJJKSB.getCode();
		}
		return "200".equals(resp.getResult()) ? GjsjjgEnum.DYSJJKCG.getCode(): GjsjjgEnum.DYSJJKSB.getCode();
	}

	private void writeFile(byte[] bytes, File file) {
		try {
			FileOutputStream out = new FileOutputStream(file);
			InputStream is = new ByteArrayInputStream(bytes);
			byte[] buff = new byte[1024];
			int len = 0;
			while((len=is.read(buff))!=-1) {
				out.write(buff, 0, len);
			}
			is.close();
			out.close();
			} catch (Exception e) {
			throw new SbywBusiException("从服务器下载固件包异常");
		}
	}
}