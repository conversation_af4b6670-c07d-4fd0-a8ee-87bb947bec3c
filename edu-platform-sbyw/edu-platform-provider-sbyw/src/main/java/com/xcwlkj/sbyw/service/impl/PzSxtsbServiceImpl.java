/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.service.impl;

import org.springframework.stereotype.Service;

import com.xcwlkj.sbyw.mapper.PzSxtsbMapper;
import com.xcwlkj.sbyw.model.domain.PzSxtsb;
import com.xcwlkj.sbyw.service.PzSxtsbService;

import tk.mybatis.mapper.entity.Example;

import java.util.List;

import javax.annotation.Resource;

/**
 * 摄像头设备配置表服务
 * 
 * <AUTHOR>
 * @version $Id: PzSxtsbServiceImpl.java, v 0.1 2020年05月17日 15时39分 xcwlkj.com
 *          Exp $
 */
@Service("pzSxtsbService")
public class PzSxtsbServiceImpl implements PzSxtsbService {

	@Resource
	private PzSxtsbMapper modelMapper;

	@Override
	public void insertSxt(String azfw, String anfwm, String ccfw, String ccfwm, String sbbh) {
		// TODO Auto-generated method stub
		PzSxtsb sxtsb = new PzSxtsb();
		sxtsb.setSbxxbh(sbbh);
		sxtsb.setSxtazfw(azfw);
		sxtsb.setSxtazfwm(anfwm);
		sxtsb.setSxtcxfw(ccfw);
		sxtsb.setSxtcxfwm(ccfwm);
		modelMapper.insertSelective(sxtsb);
	}

	@Override
	public PzSxtsb getPzSxtsbBySbxxbh(String sbxxbh) {
		PzSxtsb pzSxtsb = new PzSxtsb();
		pzSxtsb = modelMapper.selectByPrimaryKey(sbxxbh);
		return pzSxtsb;
	}

	@Override
	public void updateSxt(PzSxtsb pzsxt, Example example) {
		// TODO Auto-generated method stub
		modelMapper.updateByExampleSelective(pzsxt, example);
	}
	
	@Override
	public int insertSelective(PzSxtsb record) {
		return modelMapper.insertSelective(record);
	}
	
	@Override
	public int updateByPrimaryKeySelective(PzSxtsb record) {
		return modelMapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public void deleteList(List<String> example) {
		// TODO Auto-generated method stub
		modelMapper.deleteList(example);
	}
	
	public void replaceInto(PzSxtsb record){
		modelMapper.replaceInto(record);
	}
}