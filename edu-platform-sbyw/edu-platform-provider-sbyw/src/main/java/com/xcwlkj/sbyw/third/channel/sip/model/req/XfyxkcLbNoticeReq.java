
package com.xcwlkj.sbyw.third.channel.sip.model.req;

import com.alibaba.fastjson.annotation.JSONField;
import com.xcwlkj.sbyw.third.channel.sip.model.req.model.XfyxkcLbNotify;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *   下发有效考场信息列表
 */
@Data
@EqualsAndHashCode(callSuper=false)
public class XfyxkcLbNoticeReq extends BaseSipServiceReq{
    //目标节点的SIP地址
    @JSONField(name = "Notify")
    private XfyxkcLbNotify xfyxkcLbNotify;

}