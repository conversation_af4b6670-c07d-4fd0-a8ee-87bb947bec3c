package com.xcwlkj.sbyw.third.channel.unifyaccess.model.req;

import com.google.common.collect.Sets;
import com.xcwlkj.util.DateUtil;
import lombok.Data;

import java.util.Set;

@Data
public class BatchCommandReqModel {
	
	/**
	 * 平台APPID
	 */
	private String appIdSrc;
	/**
	 * 目标APPID集合
	 */
	private Set<String> appIdTarget = Sets.newConcurrentHashSet();
	
	private String msgId;
	
	private String timestamp = DateUtil.getCurrentDateTime();
	/**
	 * 发送数据
	 */
	private String data;
	/**
	 * 签名
	 */
	private String sign;
	
}
