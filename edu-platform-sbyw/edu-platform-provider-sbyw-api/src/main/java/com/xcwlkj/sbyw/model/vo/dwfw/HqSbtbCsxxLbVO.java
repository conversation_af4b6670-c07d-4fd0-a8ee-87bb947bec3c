/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.dwfw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 42 数据同步 - 查询 设备同步场所信息 列表vo
 * <AUTHOR>
 * @version $Id: HqSbtbCsxxLbVO.java, v 0.1 2020年12月14日 01时10分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class HqSbtbCsxxLbVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备同步 场所信息列表 */
    private List<SbtbCsxxItemVO> sbtbCsxxList;

}
