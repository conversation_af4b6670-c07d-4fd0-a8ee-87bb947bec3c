/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.xjxxzs;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 详情-参数检查结果vo
 * <AUTHOR>
 * @version $Id: XtpzcsjcjgbtItemVO.java, v 0.1 2020年05月20日 17时01分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class XtpzcsjcjgbtItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 表头名称 */
    private String btmc;
    /** 表头类型 */
    private String btlx;

}
