/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.service;

import com.xcwlkj.core.annotation.mock.YapiMock;
import com.alibaba.fastjson.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


import com.xcwlkj.sbyw.service.hystrix.SpzdyjcXcmcFeignHystrix;
import com.xcwlkj.sbyw.model.dto.spzdyjc.SpyrwrDTO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.SpzlzdDTO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.SpyrcsDTO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.HcspurllbDTO;
import com.xcwlkj.sbyw.model.vo.spzdyjc.HcspurllbVO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.SpzdjchdDTO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.SpzlzdjgzsDTO;
import com.xcwlkj.sbyw.model.vo.spzdyjc.SpzlzdjgzsVO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.SpzlzdjgxqDTO;
import com.xcwlkj.sbyw.model.vo.spzdyjc.SpzlzdjgxqVO;
import com.xcwlkj.sbyw.model.dto.spzdyjc.YrwrjgzsDTO;
import com.xcwlkj.sbyw.model.vo.spzdyjc.YrwrjgzsVO;
import com.xcwlkj.util.wrapper.Wrapper;

/**
 * 
 * <AUTHOR>
 * @version $Id: SpzdyjcXcmcFeignApi.java, v 0.1 2020年12月02日 15时15分 xcwlkj.com Exp $
 */
@FeignClient(value = "sbyw-service", fallback = SpzdyjcXcmcFeignHystrix.class)
public interface SpzdyjcXcmcFeignApi {

   
	/**
	 * 02 - 视频有人无人检测
	 * @param spyrwrDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = Void.class)
    @PostMapping(value = "/sbyw/spzdyjc/spyrwr")
    Wrapper<Void> spyrwr(@RequestBody SpyrwrDTO spyrwrDto);
	/**
	 * 01 - 视频质量诊断
	 * @param spzlzdDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = Void.class)
    @PostMapping(value = "/sbyw/spzdyjc/spzlzd")
    Wrapper<Void> spzlzd(@RequestBody SpzlzdDTO spzlzdDto);
	/**
	 * 03 - 视频诊断测试
	 * @param spyrcsDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = Void.class)
    @PostMapping(value = "/sbyw/spzdyjc/spyrcs")
    Wrapper<Void> spyrcs(@RequestBody SpyrcsDTO spyrcsDto);
	/**
	 * 04 - 获取摄像头媒体地址
	 * @param hcspurllbDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = HcspurllbVO.class)
    @PostMapping(value = "/sbyw/spzdyjc/hcspurllb")
    Wrapper<HcspurllbVO> hcspurllb(@RequestBody HcspurllbDTO hcspurllbDto);
	/**
	 * 视频诊断与检测回调接口
	 * @param spzdjchdDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = Void.class)
    @PostMapping(value = "/sbyw/spzdyjc/spzdjchd")
    Wrapper<Void> spzdjchd(@RequestBody JSONObject spzdjchdDto);
	/**
	 * 05 - 视频质量诊断结果展示
	 * @param spzlzdjgzsDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = SpzlzdjgzsVO.class)
    @PostMapping(value = "/sbyw/spzdyjc/spzlzdjgzs")
    Wrapper<SpzlzdjgzsVO> spzlzdjgzs(@RequestBody SpzlzdjgzsDTO spzlzdjgzsDto);
	/**
	 * 06 - 视频质量诊断详情
	 * @param spzlzdjgxqDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = SpzlzdjgxqVO.class)
    @PostMapping(value = "/sbyw/spzdyjc/spzlzdjgxq")
    Wrapper<SpzlzdjgxqVO> spzlzdjgxq(@RequestBody SpzlzdjgxqDTO spzlzdjgxqDto);
	/**
	 * 07 - 有人无人结果列表
	 * @param yrwrjgzsDto
	 * @return
	 */
	@YapiMock(projectId="138", returnClass = YrwrjgzsVO.class)
    @PostMapping(value = "/sbyw/spzdyjc/yrwrjgzs")
    Wrapper<YrwrjgzsVO> yrwrjgzs(@RequestBody YrwrjgzsDTO yrwrjgzsDto);
}




