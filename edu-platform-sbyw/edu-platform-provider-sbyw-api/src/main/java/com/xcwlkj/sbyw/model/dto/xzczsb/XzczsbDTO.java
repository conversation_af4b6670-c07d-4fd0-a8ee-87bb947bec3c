/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dto.xzczsb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 车载-新增车载设备信息dto
 * <AUTHOR>
 * @version $Id: XzczsbDTO.java, v 0.1 2020年05月15日 14时55分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class XzczsbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 安放车辆编号 */
    @NotBlank(message = "安放车辆编号不能为空")
    private String afclbh;
    /** 车载设备名称 */
    @NotBlank(message = "车载设备名称不能为空")
    private String czsbmc;
    /** sip地址 */
    @NotBlank(message = "sip地址不能为空")
    private String sipdz;

}
