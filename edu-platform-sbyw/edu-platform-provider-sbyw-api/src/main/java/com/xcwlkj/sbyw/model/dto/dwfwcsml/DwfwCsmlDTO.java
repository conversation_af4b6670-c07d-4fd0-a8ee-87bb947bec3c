/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dto.dwfwcsml;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 标准化考场-查询场所目录dto
 * <AUTHOR>
 * @version $Id: DwfwCsmlDTO.java, v 0.1 2020年06月09日 10时08分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class DwfwCsmlDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** sip的地址 */
    @NotBlank(message = "sip的地址不能为空")
    private String sipdz;

}
