/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.dwfw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备-查询设备在线状态vo
 * <AUTHOR>
 * @version $Id: SbztlistItemVO.java, v 0.1 2020年05月27日 09时31分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbztlistItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备类型 */
    private String sblx;
    /** 设备类型名称 */
    private String sblxmc;
    /** 设备数量 */
    private Integer sbxl;
    /** 离线设备数量 */
    private Integer lxsbsl;
    /** 在线设备数量 */
    private Integer zxsbsl;

}
