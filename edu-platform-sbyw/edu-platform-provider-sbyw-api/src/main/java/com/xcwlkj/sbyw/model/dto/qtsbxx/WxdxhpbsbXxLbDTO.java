/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dto.qtsbxx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 【1.7】无线电信号屏蔽设备信息列表dto
 * <AUTHOR>
 * @version $Id: WxdxhpbsbXxLbDTO.java, v 0.1 2021年11月05日 16时21分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class WxdxhpbsbXxLbDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标准化考点id */
    @NotBlank(message = "标准化考点id不能为空")
    private String bzhkdid;
    /** 标准化考场id */
    @NotBlank(message = "标准化考场id不能为空")
    private String bzhkcid;
    /** 第几页 */
    @NotNull(message = "第几页不能为空")
    private Integer pageNum;
    /** 每页显示数 */
    @NotNull(message = "每页显示数不能为空")
    private Integer pageSize;

}
