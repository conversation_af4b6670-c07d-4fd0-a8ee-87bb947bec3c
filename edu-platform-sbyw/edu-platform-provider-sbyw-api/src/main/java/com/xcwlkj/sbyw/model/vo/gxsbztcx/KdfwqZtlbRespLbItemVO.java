/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.gxsbztcx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 状态查询-考点服务器列表vo
 * <AUTHOR>
 * @version $Id: KdfwqZtlbRespLbItemVO.java, v 0.1 2023年03月15日 15时06分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class KdfwqZtlbRespLbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备编号 */
    private String sbbh;
    /** 序列号 */
    private String xlh;
    /** 设备型号 */
    private String sbxh;
    /** 设备类型  174视频诊断服务，173数据服务，170考点网关 */
    private String sblx;
    /** 设备类型名称 */
    private String sblxmc;
    /** 设备名称 */
    private String sbmc;
    /** 运维状态  1:使用中;2:维修中;3:已报废 */
    private String ywzt;
    /** IP地址 */
    private String ipdz;
    /** mac地址 */
    private String macdz;
    /** 设备厂商码 */
    private String sbcsm;
    /** 设备品牌 */
    private String sbpp;
    /** 采购时间 */
    private String cgsj;
    /** 报废年限 */
    private String bfnx;
    /** 设备质保截止时间 */
    private String sbzbjzsj;
    /** 网关照片id */
    private String wgzpid;
    /** 签名字符 */
    private String qm;
    /** 在线状态 ：1-在线，0-离线 */
    private String zxzt;
    /** 更新时间 */
    private String gxsj;
    /** 设备安装日期 */
    private String sbazrq;
    /** 地市代码 */
    private String dsbh;
    /** 地市名称 */
    private String dsmc;
    /** 考区代码 */
    private String kqbh;
    /** 考区名称 */
    private String kqmc;
    /** 考点代码 */
    private String kdbh;
    /** 考点名称 */
    private String kdmc;

}
