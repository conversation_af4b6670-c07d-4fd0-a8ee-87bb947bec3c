/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.csh;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;


/**
 * 巡检方案初始化vo
 * <AUTHOR>
 * @version $Id: CshVO.java, v 0.1 2020年05月27日 14时18分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class CshVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 巡检项列表 */
    private List<XjxlbItemVO> xjxlb;

}
