/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2031 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dto.qtsbxx;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 【1.7】新增无线电信号屏蔽设备dto
 * <AUTHOR>
 * @version $Id: JybwxdxhpdsbParamItemDTO.java, v 0.1 2021年11月05日 16时07分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class JybwxdxhpdsbParamItemDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 标准化考点id */
    @NotBlank(message = "标准化考点id不能为空")
    private String bzhkdid;
    /** 标准化考场id */
    @NotBlank(message = "标准化考场id不能为空")
    private String bzhkcid;
    /** 设备厂商 */
    @NotBlank(message = "设备厂商不能为空")
    private String sbcs;
    /** 无线电信号频段列表 */
    @NotNull(message = "无线电信号频段列表不能为空")
    private List<String> wxdxhpdmList;

}
