/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dto.czsb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 车载设备dto
 * <AUTHOR>
 * @version $Id: GpsDTO.java, v 0.1 2020年05月14日 16时27分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class GpsDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 本次GPS数据的时间 */
    @NotBlank(message = "本次GPS数据的时间不能为空")
    private String gpstime;
    /** 经度数据，原始数据 */
    private String longitude;
    /** 经度数据，度分秒格式 */
    @NotBlank(message = "经度数据，度分秒格式不能为空")
    private String longitudedesc;
    /** 经度半球，东经E|西经W */
    @NotBlank(message = "经度半球，东经E|西经W不能为空")
    private String ew;
    /** 纬度数据，原始数据 */
    private String latitude;
    /** 纬度数据，度分秒格式 */
    @NotBlank(message = "纬度数据，度分秒格式不能为空")
    private String latitudedesc;
    /** 纬度半球，北纬N|南纬 */
    @NotBlank(message = "纬度半球，北纬N|南纬不能为空")
    private String ns;
    /** 时区偏移量 */
    private String timezone;
    /** 方向角 */
    private String direction;
    /** 高程 */
    private String height;
    /** 设备流水序号号 */
    private String gpsseq;
    /** 地面速度 */
    private String speed;

}
