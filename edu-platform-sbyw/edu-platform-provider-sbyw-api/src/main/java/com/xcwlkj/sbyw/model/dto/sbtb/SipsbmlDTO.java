/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.dto.sbtb;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotBlank;


/**
 * 获取sip节点列表（教育部规范定义）dto
 * <AUTHOR>
 * @version $Id: SipsbmlDTO.java, v 0.1 2020年12月29日 11时37分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SipsbmlDTO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 目标sip, 传-1 表示当前场所的根目录，当类型为DO 时传当前节点的uri，其余传该节点parentSipUri */
    @NotBlank(message = "目标sip, 传-1 表示当前场所的根目录，当类型为DO 时传当前节点的uri，其余传该节点parentSipUri不能为空")
    private String toSip;
    /** 节点目录, 传-1 表示当前场所的根目录 */
    @NotBlank(message = "节点目录, 传-1 表示当前场所的根目录不能为空")
    private String requestUri;
    /** 获取树列表的方式，0 或者 为空时，采用 sip+devList  方式 ；1，采用 sip 目录树节点方式 */
    private String hqfs;

}
