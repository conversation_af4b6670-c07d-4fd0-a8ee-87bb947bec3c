/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.cs;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备参数模板详情vo
 * <AUTHOR>
 * @version $Id: MbcslbItemVO.java, v 0.1 2020年06月20日 16时41分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class MbcslbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 参数key */
    private String csm;
    /** 参数value1 */
    private String csz1;
    /** 参数value2 */
    private String csz2;
    /** 参数value3 */
    private String csz3;
    /** 参数value4 */
    private String csz4;

}
