/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2030 All Rights Reserved.
 */
package com.xcwlkj.sbyw.model.vo.dwfw;

import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 设备-查询设备数量vo
 * <AUTHOR>
 * @version $Id: SbsllbItemVO.java, v 0.1 2020年05月21日 16时14分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false) 
public class SbsllbItemVO  implements Serializable {
	
	/** 序列ID */
	private static final long serialVersionUID = 1L;
	
    /** 设备类型 */
    private String sblx;
    /** 设备类型名称 */
    private String sblxmc;
    /** 设备类型数量 */
    private String sblxsl;

}
