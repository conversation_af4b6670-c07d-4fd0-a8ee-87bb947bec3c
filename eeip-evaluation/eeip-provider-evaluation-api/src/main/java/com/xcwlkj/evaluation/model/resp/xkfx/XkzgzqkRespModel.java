/**
 * xcwlkj.com Inc.
 * Copyright (c) 2015-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.resp.xkfx;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xcwlkj.base.remote.RemoteRespBaseModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.xcwlkj.evaluation.model.vo.xkfx.XkzgzqkItemVO;
import java.util.List;


/**
 * 巡课组工作情况响应
 * <AUTHOR>
 * @version $Id: XkzgzqkRespModel.java, v 0.1 2023年03月06日 16时52分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class XkzgzqkRespModel extends RemoteRespBaseModel {

    /** 序列ID */
    private static final long serialVersionUID = -1L;
	
    /** 巡课组工作情况列表 */
    private List<XkzgzqkItemVO> xkzgzqkList;
    /** 总条数 */
    private Integer totalRows;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}