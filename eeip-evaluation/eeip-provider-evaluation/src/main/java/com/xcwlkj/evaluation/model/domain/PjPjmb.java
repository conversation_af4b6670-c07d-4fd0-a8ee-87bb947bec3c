/**
 * xcwlkj.com Inc.
 * Copyright (c) 2023-2033 All Rights Reserved.
 */
package com.xcwlkj.evaluation.model.domain;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;


/**
 * 评价_评价模板
 * 
 * <AUTHOR>
 * @version $Id: PjPjmb.java, v 0.1 2023年02月09日 15时34分 xcwlkj.com Exp $
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Table(name = "pj_pjmb")
public class PjPjmb implements Serializable {
    /** 序列id */
    private static final long serialVersionUID = 1L;
    /** 模板编号 */
    @Id
    @Column(name = "mbbh")
    private String            mbbh;
    /** 模板名称 */
    @Column(name = "mbmc")
    private String            mbmc;
    /** 模板描述 */
    @Column(name = "mbms")
    private String            mbms;
    /** 创建人编号 */
    @Column(name = "cjrbh")
    private String            cjrbh;
    /** 创建人姓名 */
    @Column(name = "cjrxm")
    private String            cjrxm;
    /** 删除状态 */
    @Column(name = "sczt")
    private String            sczt;
    /** 创建时间 */
    @Column(name = "create_time")
    private Date            createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private Date            updateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}


