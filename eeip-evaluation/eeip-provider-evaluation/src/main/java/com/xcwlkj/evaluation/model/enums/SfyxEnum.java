package com.xcwlkj.evaluation.model.enums;

import com.xcwlkj.base.exception.BusinessException;

/** 
 * 是否有效
 * @author: Xucz
 * @version: $Id: SfyxEnum.java,v 0.1 2022年11月08日 下午 4:51:10 Xucz Exp $
 */
public enum SfyxEnum {

    VALID("0","有效"),
    INVALID("1","无效");

    private String code;
    private String desc;

    private SfyxEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    
    public String getCode() {
        return this.code;
    }
    
    public String getDesc() {
        return this.desc;
    }
    
    public static SfyxEnum get(String code) {
        for (SfyxEnum c : values()) {
            if (c.getCode().equals(code.toUpperCase())) {
                return c;
            }
        }
        throw new BusinessException("无此枚举，枚举值："+code);
    }

}
